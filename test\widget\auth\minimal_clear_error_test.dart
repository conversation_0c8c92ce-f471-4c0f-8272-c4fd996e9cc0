import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:safestride/core/providers/auth_provider.dart';
import 'package:safestride/presentation/screens/auth/register_screen.dart';

// Mock class
class MockAuthProvider extends Mock implements AuthProvider {}

void main() {
  group('RegisterScreen Clear Error Test', () {
    late MockAuthProvider mockAuthProvider;

    setUp(() {
      mockAuthProvider = MockAuthProvider();
      when(mockAuthProvider.state).thenReturn(AuthState.error);
      when(mockAuthProvider.errorMessage).thenReturn('Test error');
      when(mockAuthProvider.isLoading).thenReturn(false);
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const RegisterScreen(),
        ),
      );
    }

    testWidgets('should call clearError when typing in display name field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Type in the first text field (display name)
      await tester.enterText(find.byType(TextFormField).first, 'Test');
      await tester.pump();

      // Assert
      verify(mockAuthProvider.clearError()).called(1);
    });
  });
}