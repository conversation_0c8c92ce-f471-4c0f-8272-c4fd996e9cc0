import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';
import 'package:safestride/presentation/providers/auth_provider.dart';
import 'package:safestride/presentation/screens/auth/register_screen.dart';

import 'register_screen_test.mocks.dart';

@GenerateMocks([AuthProvider])
void main() {
  late MockAuthProvider mockAuthProvider;

  setUp(() {
    mockAuthProvider = MockAuthProvider();
    when(mockAuthProvider.state).thenReturn(AuthState.initial);
    when(mockAuthProvider.currentUser).thenReturn(null);
    when(mockAuthProvider.errorMessage).thenReturn(null);
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: ChangeNotifierProvider<AuthProvider>.value(
        value: mockAuthProvider,
        child: const RegisterScreen(),
      ),
    );
  }

  group('RegisterScreen Widget Tests', () {
    testWidgets('should display register form elements', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Create Account'), findsOneWidget);
      expect(find.text('Join SafeStride today'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(4)); // Name, email, password, confirm password
      expect(find.text('Full Name'), findsOneWidget);
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Confirm Password'), findsOneWidget);
      expect(find.text('Create Account'), findsNWidgets(2)); // Title and button
      expect(find.text('Sign up with Google'), findsOneWidget);
      expect(find.text('Already have an account? Sign In'), findsOneWidget);
    });

    testWidgets('should validate full name field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Try to submit with empty name
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      // Assert
      expect(find.text('Please enter your full name'), findsOneWidget);
    });

    testWidgets('should validate email field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter name but leave email empty
      await tester.enterText(find.byType(TextFormField).at(0), 'John Doe');
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      // Assert
      expect(find.text('Please enter your email'), findsOneWidget);
    });

    testWidgets('should validate email format', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter invalid email format
      await tester.enterText(find.byType(TextFormField).at(0), 'John Doe');
      await tester.enterText(find.byType(TextFormField).at(1), 'invalid-email');
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      // Assert
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('should validate password field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter name and email but leave password empty
      await tester.enterText(find.byType(TextFormField).at(0), 'John Doe');
      await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      // Assert
      expect(find.text('Please enter a password'), findsOneWidget);
    });

    testWidgets('should validate password length', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter short password
      await tester.enterText(find.byType(TextFormField).at(0), 'John Doe');
      await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(2), '123');
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      // Assert
      expect(find.text('Password must be at least 8 characters'), findsOneWidget);
    });

    testWidgets('should validate password confirmation', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter mismatched passwords
      await tester.enterText(find.byType(TextFormField).at(0), 'John Doe');
      await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(2), 'Password123');
      await tester.enterText(find.byType(TextFormField).at(3), 'DifferentPass456');
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      // Assert
      expect(find.text('Passwords do not match'), findsOneWidget);
    });

    testWidgets('should call register when form is valid', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter valid information and submit
      await tester.enterText(find.byType(TextFormField).at(0), 'John Doe');
      await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(2), 'Password123');
      await tester.enterText(find.byType(TextFormField).at(3), 'Password123');
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      // Assert
      verify(mockAuthProvider.registerWithEmail(
        email: '<EMAIL>',
        password: 'Password123',
        displayName: 'John Doe',
      )).called(1);
    });

    testWidgets('should call loginWithSSO when Google sign up is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act
      await tester.tap(find.text('Sign up with Google'));
      await tester.pump();

      // Assert
      verify(mockAuthProvider.loginWithSSO()).called(1);
    });

    testWidgets('should show loading indicator when state is loading', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.loading);
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show error message when state is error', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.error);
      when(mockAuthProvider.errorMessage).thenReturn('Registration failed');
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Registration failed'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should navigate to login screen when sign in is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const RegisterScreen(),
        ),
        routes: {
          '/login': (context) => const Scaffold(body: Text('Login Screen')),
        },
      ));
      
      // Act
      await tester.tap(find.text('Already have an account? Sign In'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Login Screen'), findsOneWidget);
    });

    testWidgets('should toggle password visibility for password field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Assert initial state (password hidden)
      expect(find.byIcon(Icons.visibility), findsAtLeastNWidgets(1));
      
      // Act - Tap visibility toggle for password field
      await tester.tap(find.byIcon(Icons.visibility).first);
      await tester.pump();

      // Assert password is now visible
      expect(find.byIcon(Icons.visibility_off), findsAtLeastNWidgets(1));
    });

    testWidgets('should toggle password visibility for confirm password field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Assert initial state (password hidden)
      expect(find.byIcon(Icons.visibility), findsAtLeastNWidgets(2));
      
      // Act - Tap visibility toggle for confirm password field
      await tester.tap(find.byIcon(Icons.visibility).last);
      await tester.pump();

      // Assert password is now visible
      expect(find.byIcon(Icons.visibility_off), findsAtLeastNWidgets(1));
    });

    testWidgets('should disable create account button when loading', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.loading);
      await tester.pumpWidget(createTestWidget());
      
      // Act & Assert
      final createAccountButton = find.text('Create Account').last;
      expect(createAccountButton, findsOneWidget);
      
      // Button should be disabled (not tappable) when loading
      await tester.tap(createAccountButton);
      await tester.pump();
      
      // Verify registerWithEmail is not called when button is disabled
      verifyNever(mockAuthProvider.registerWithEmail(
        email: anyNamed('email'),
        password: anyNamed('password'),
        displayName: anyNamed('displayName'),
      ));
    });

    testWidgets('should clear error when user starts typing', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.error);
      when(mockAuthProvider.errorMessage).thenReturn('Registration failed');
      await tester.pumpWidget(createTestWidget());
      
      // Act - Start typing in name field
      await tester.enterText(find.byType(TextFormField).first, 'J');
      await tester.pump();

      // Assert
      verify(mockAuthProvider.clearError()).called(1);
    });

    // TODO: Fix this test - currently causing framework issues
    // testWidgets('should show terms and conditions text', (WidgetTester tester) async {
    //   // Arrange
    //   await tester.pumpWidget(createTestWidget());

    //   // Assert
    //   expect(find.byType(Checkbox), findsOneWidget);
    //   expect(find.byType(RichText), findsAtLeastNWidgets(1));
    // });

    group('offline authentication scenarios', () {
      testWidgets('should show network error when registration fails offline', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('No network connection available');
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('No network connection available'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('should show appropriate error for offline SSO attempt', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('SSO requires network connection');
        await tester.pumpWidget(createTestWidget());
        
        // Act
        await tester.tap(find.text('Sign up with Google'));
        await tester.pump();

        // Assert
        expect(find.text('SSO requires network connection'), findsOneWidget);
        verify(mockAuthProvider.loginWithSSO()).called(1);
      });

      testWidgets('should handle network timeout during registration', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('Request timeout - check your connection');
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Request timeout - check your connection'), findsOneWidget);
      });

      testWidgets('should show retry option when network error occurs', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('Network error occurred');
        await tester.pumpWidget(createTestWidget());
        
        // Assert error is shown
        expect(find.text('Network error occurred'), findsOneWidget);
        
        // Act - Clear error and retry
        await tester.enterText(find.byType(TextFormField).first, 'John');
        await tester.pump();
        
        // Assert error is cleared
        verify(mockAuthProvider.clearError()).called(1);
      });

      testWidgets('should disable form submission when offline', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('Device is offline');
        await tester.pumpWidget(createTestWidget());
        
        // Act - Try to submit form
        await tester.enterText(find.byType(TextFormField).at(0), 'John Doe');
        await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
        await tester.enterText(find.byType(TextFormField).at(2), 'Password123');
        await tester.enterText(find.byType(TextFormField).at(3), 'Password123');
        await tester.tap(find.text('Create Account').last);
        await tester.pump();

        // Assert offline error is shown
        expect(find.text('Device is offline'), findsOneWidget);
      });
    });
  });
}