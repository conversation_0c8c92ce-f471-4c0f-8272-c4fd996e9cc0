import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';

import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/presentation/screens/walkabout/walkabout_creation_screen.dart';
import 'package:safestride/presentation/providers/walkabout_provider.dart';
import 'package:safestride/presentation/providers/auth_provider.dart';

import 'walkabout_creation_screen_test.mocks.dart';

@GenerateMocks([WalkaboutProvider, AuthProvider])
void main() {
  late MockWalkaboutProvider mockWalkaboutProvider;
  late MockAuthProvider mockAuthProvider;

  setUp(() {
    mockWalkaboutProvider = MockWalkaboutProvider();
    mockAuthProvider = MockAuthProvider();
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: MultiProvider(
        providers: [
          ChangeNotifierProvider<WalkaboutProvider>.value(
            value: mockWalkaboutProvider,
          ),
          ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
          ),
        ],
        child: const WalkaboutCreationScreen(),
      ),
    );
  }

  group('WalkaboutCreationScreen', () {
    testWidgets('should display all form fields', (WidgetTester tester) async {
      // Arrange
      when(mockWalkaboutProvider.isLoading).thenReturn(false);
      when(mockWalkaboutProvider.error).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(
        const User(
          uid: 'test-uid',
          email: '<EMAIL>',
          createdAt: null,
          updatedAt: null,
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Create Walkabout'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Title and description
      expect(find.byType(DropdownButtonFormField<WalkaboutStatus>), findsOneWidget);
      expect(find.text('Template'), findsOneWidget);
      expect(find.text('Location'), findsOneWidget);
      expect(find.text('Create Walkabout'), findsNWidgets(2)); // AppBar and button
    });

    testWidgets('should show error message when error exists', (WidgetTester tester) async {
      // Arrange
      const errorMessage = 'Test error message';
      when(mockWalkaboutProvider.isLoading).thenReturn(false);
      when(mockWalkaboutProvider.error).thenReturn(errorMessage);
      when(mockWalkaboutProvider.clearError).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(
        const User(
          uid: 'test-uid',
          email: '<EMAIL>',
          createdAt: null,
          updatedAt: null,
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text(errorMessage), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('should clear error when close button is tapped', (WidgetTester tester) async {
      // Arrange
      const errorMessage = 'Test error message';
      when(mockWalkaboutProvider.isLoading).thenReturn(false);
      when(mockWalkaboutProvider.error).thenReturn(errorMessage);
      when(mockWalkaboutProvider.clearError).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(
        const User(
          uid: 'test-uid',
          email: '<EMAIL>',
          createdAt: null,
          updatedAt: null,
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.byIcon(Icons.close));
      await tester.pump();

      // Assert
      verify(mockWalkaboutProvider.clearError()).called(1);
    });

    testWidgets('should validate required title field', (WidgetTester tester) async {
      // Arrange
      when(mockWalkaboutProvider.isLoading).thenReturn(false);
      when(mockWalkaboutProvider.error).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(
        const User(
          uid: 'test-uid',
          email: '<EMAIL>',
          createdAt: null,
          updatedAt: null,
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Try to submit without entering title
      await tester.tap(find.text('Create Walkabout').last);
      await tester.pump();

      // Assert
      expect(find.text('Title is required'), findsOneWidget);
      verifyNever(mockWalkaboutProvider.createWalkabout(
        title: anyNamed('title'),
        description: anyNamed('description'),
        userId: anyNamed('userId'),
        location: anyNamed('location'),
        status: anyNamed('status'),
      ));
    });

    testWidgets('should validate title length', (WidgetTester tester) async {
      // Arrange
      when(mockWalkaboutProvider.isLoading).thenReturn(false);
      when(mockWalkaboutProvider.error).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(
        const User(
          uid: 'test-uid',
          email: '<EMAIL>',
          createdAt: null,
          updatedAt: null,
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Enter title that's too long
      final longTitle = 'a' * 101;
      await tester.enterText(find.byType(TextFormField).first, longTitle);
      await tester.tap(find.text('Create Walkabout').last);
      await tester.pump();

      // Assert
      expect(find.text('Title cannot exceed 100 characters'), findsOneWidget);
    });

    testWidgets('should create walkabout when form is valid', (WidgetTester tester) async {
      // Arrange
      const testTitle = 'Test Walkabout';
      const testDescription = 'Test description';
      const testUserId = 'test-uid';
      
      final testWalkabout = Walkabout(
        id: 'test-id',
        title: testTitle,
        description: testDescription,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        status: WalkaboutStatus.draft,
        location: null,
        userId: testUserId,
        isCompleted: false,
        syncStatus: SyncStatus.local,
      );

      when(mockWalkaboutProvider.isLoading).thenReturn(false);
      when(mockWalkaboutProvider.error).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(
        const User(
          uid: testUserId,
          email: '<EMAIL>',
          createdAt: null,
          updatedAt: null,
        ),
      );
      when(mockWalkaboutProvider.createWalkabout(
        title: anyNamed('title'),
        description: anyNamed('description'),
        userId: anyNamed('userId'),
        location: anyNamed('location'),
        status: anyNamed('status'),
      )).thenAnswer((_) async => testWalkabout);

      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Fill in the form
      await tester.enterText(find.byType(TextFormField).first, testTitle);
      await tester.enterText(find.byType(TextFormField).last, testDescription);
      
      // Submit the form
      await tester.tap(find.text('Create Walkabout').last);
      await tester.pump();

      // Assert
      verify(mockWalkaboutProvider.createWalkabout(
        title: testTitle,
        description: testDescription,
        userId: testUserId,
        location: null,
        status: WalkaboutStatus.draft,
      )).called(1);
    });

    testWidgets('should show loading overlay when creating walkabout', (WidgetTester tester) async {
      // Arrange
      when(mockWalkaboutProvider.isLoading).thenReturn(true);
      when(mockWalkaboutProvider.error).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(
        const User(
          uid: 'test-uid',
          email: '<EMAIL>',
          createdAt: null,
          updatedAt: null,
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should disable create button when loading', (WidgetTester tester) async {
      // Arrange
      when(mockWalkaboutProvider.isLoading).thenReturn(true);
      when(mockWalkaboutProvider.error).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(
        const User(
          uid: 'test-uid',
          email: '<EMAIL>',
          createdAt: null,
          updatedAt: null,
        ),
      );

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      final createButton = tester.widget<ElevatedButton>(
        find.byType(ElevatedButton),
      );
      expect(createButton.onPressed, isNull);
    });
  });
}
