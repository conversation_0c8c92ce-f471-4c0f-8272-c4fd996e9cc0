// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in safestride/test/unit/presentation/providers/user_profile_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/user.dart' as _i2;
import 'package:safestride/domain/usecases/get_user_by_id.dart' as _i3;
import 'package:safestride/domain/usecases/sync_user.dart' as _i8;
import 'package:safestride/domain/usecases/update_user.dart' as _i5;
import 'package:safestride/domain/usecases/update_user_preferences.dart' as _i6;
import 'package:safestride/domain/usecases/update_user_role.dart' as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUser_0 extends _i1.SmartFake implements _i2.User {
  _FakeUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [GetUserByIdUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetUserByIdUseCase extends _i1.Mock
    implements _i3.GetUserByIdUseCase {
  MockGetUserByIdUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User?> call(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#call, [uid]),
            returnValue: _i4.Future<_i2.User?>.value(),
          )
          as _i4.Future<_i2.User?>);
}

/// A class which mocks [UpdateUserUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockUpdateUserUseCase extends _i1.Mock implements _i5.UpdateUserUseCase {
  MockUpdateUserUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User> call(_i2.User? user) =>
      (super.noSuchMethod(
            Invocation.method(#call, [user]),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(this, Invocation.method(#call, [user])),
            ),
          )
          as _i4.Future<_i2.User>);
}

/// A class which mocks [UpdateUserPreferencesUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockUpdateUserPreferencesUseCase extends _i1.Mock
    implements _i6.UpdateUserPreferencesUseCase {
  MockUpdateUserPreferencesUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User> call({
    required String? uid,
    required _i2.UserPreferences? preferences,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#call, [], {
              #uid: uid,
              #preferences: preferences,
            }),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(
                this,
                Invocation.method(#call, [], {
                  #uid: uid,
                  #preferences: preferences,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.User>);
}

/// A class which mocks [UpdateUserRoleUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockUpdateUserRoleUseCase extends _i1.Mock
    implements _i7.UpdateUserRoleUseCase {
  MockUpdateUserRoleUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User> call({
    required String? uid,
    required _i2.UserRole? role,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#call, [], {#uid: uid, #role: role}),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(
                this,
                Invocation.method(#call, [], {#uid: uid, #role: role}),
              ),
            ),
          )
          as _i4.Future<_i2.User>);
}

/// A class which mocks [SyncUserUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockSyncUserUseCase extends _i1.Mock implements _i8.SyncUserUseCase {
  MockSyncUserUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User> call(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#call, [uid]),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(this, Invocation.method(#call, [uid])),
            ),
          )
          as _i4.Future<_i2.User>);
}
