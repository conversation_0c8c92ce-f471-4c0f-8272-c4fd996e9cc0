import '../repositories/auth_repository.dart';

/// Use case for resetting user password
class ResetPasswordUseCase {
  final AuthRepository _authRepository;

  const ResetPasswordUseCase(this._authRepository);

  /// Execute the password reset process
  /// 
  /// Parameters:
  /// - [email]: User's email address to send reset link
  /// 
  /// Sends password reset email to the user
  /// 
  /// Throws: Exception if password reset fails
  Future<void> call({required String email}) async {
    // Validate email format
    if (!_isValidEmail(email)) {
      throw ArgumentError('Invalid email format');
    }

    try {
      await _authRepository.resetPassword(
        email: email.trim().toLowerCase(),
      );
    } catch (e) {
      throw Exception('Password reset failed: ${e.toString()}');
    }
  }

  /// Validate email format using regex
  bool _isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email.trim());
  }
}