import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/presentation/providers/user_profile_provider.dart';
import 'package:safestride/presentation/screens/profile/profile_edit_screen.dart';
import 'package:safestride/presentation/widgets/app_bar_widget.dart';
import 'package:safestride/presentation/widgets/loading_indicator.dart';

import 'profile_edit_screen_test.mocks.dart';

@GenerateMocks([UserProfileProvider])
void main() {
  late MockUserProfileProvider mockUserProfileProvider;

  setUp(() {
    mockUserProfileProvider = MockUserProfileProvider();
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: ChangeNotifierProvider<UserProfileProvider>.value(
        value: mockUserProfileProvider,
        child: const ProfileEditScreen(),
      ),
    );
  }

  final testUser = User(
    id: 'test-uid-123',
    email: '<EMAIL>',
    displayName: 'Test User',
    organization: 'Test Organization',
    role: UserRole.inspector,
    createdAt: DateTime(2024, 1, 1),
    lastLoginAt: DateTime(2024, 1, 2),
    preferences: UserPreferences(
      notificationsEnabled: true,
      language: 'English',
      theme: 'Light',
      offlineMode: false,
    ),
  );

  group('ProfileEditScreen Widget Tests', () {
    testWidgets('should display app bar with correct title', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow initialization

      // Assert
      expect(find.byType(AppBarWidget), findsOneWidget);
      expect(find.text('Edit Profile'), findsOneWidget);
    });

    testWidgets('should display loading indicator when loading', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(true);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loading);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(LoadingIndicator), findsOneWidget);
    });

    testWidgets('should display user not found when user is null', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(null);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('User not found'), findsOneWidget);
    });

    testWidgets('should display form with user data when loaded', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow initialization

      // Assert
      expect(find.byType(Form), findsOneWidget);
      expect(find.text('Basic Information'), findsOneWidget);
      expect(find.text('Preferences'), findsOneWidget);
    });

    testWidgets('should initialize form fields with user data', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow initialization

      // Assert
      final displayNameField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Display Name'),
      );
      expect(displayNameField.controller?.text, equals('Test User'));

      final organizationField = tester.widget<TextFormField>(
        find.widgetWithText(TextFormField, 'Organization'),
      );
      expect(organizationField.controller?.text, equals('Test Organization'));
    });

    testWidgets('should display basic information section', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Basic Information'), findsOneWidget);
      expect(find.widgetWithText(TextFormField, 'Display Name'), findsOneWidget);
      expect(find.widgetWithText(TextFormField, 'Organization'), findsOneWidget);
      expect(find.byIcon(Icons.person), findsOneWidget);
      expect(find.byIcon(Icons.business), findsOneWidget);
    });

    testWidgets('should display preferences section with switches and dropdowns', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Preferences'), findsOneWidget);
      expect(find.byType(SwitchListTile), findsNWidgets(2)); // Notifications and Offline Mode
      expect(find.byType(DropdownButton<String>), findsNWidgets(2)); // Language and Theme
      expect(find.text('Notifications'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
      expect(find.text('Offline Mode'), findsOneWidget);
    });

    testWidgets('should initialize preferences with user data', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      final notificationSwitch = tester.widget<SwitchListTile>(
        find.widgetWithText(SwitchListTile, 'Notifications'),
      );
      expect(notificationSwitch.value, isTrue);

      final offlineModeSwitch = tester.widget<SwitchListTile>(
        find.widgetWithText(SwitchListTile, 'Offline Mode'),
      );
      expect(offlineModeSwitch.value, isFalse);
    });

    testWidgets('should display action buttons', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Save'), findsOneWidget);
      expect(find.byType(OutlinedButton), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should validate required fields', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Clear the display name field
      await tester.enterText(find.widgetWithText(TextFormField, 'Display Name'), '');
      await tester.tap(find.text('Save'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter your display name'), findsOneWidget);
    });

    testWidgets('should validate organization field', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Clear the organization field
      await tester.enterText(find.widgetWithText(TextFormField, 'Organization'), '');
      await tester.tap(find.text('Save'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter your organization'), findsOneWidget);
    });

    testWidgets('should toggle notification switch', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Tap the notification switch
      await tester.tap(find.widgetWithText(SwitchListTile, 'Notifications'));
      await tester.pump();

      // Assert
      final notificationSwitch = tester.widget<SwitchListTile>(
        find.widgetWithText(SwitchListTile, 'Notifications'),
      );
      expect(notificationSwitch.value, isFalse);
      expect(find.text('Disabled'), findsNWidgets(2)); // Notifications and Offline Mode
    });

    testWidgets('should toggle offline mode switch', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Tap the offline mode switch
      await tester.tap(find.widgetWithText(SwitchListTile, 'Offline Mode'));
      await tester.pump();

      // Assert
      final offlineModeSwitch = tester.widget<SwitchListTile>(
        find.widgetWithText(SwitchListTile, 'Offline Mode'),
      );
      expect(offlineModeSwitch.value, isTrue);
      expect(find.text('Enabled'), findsNWidgets(2)); // Notifications and Offline Mode
    });

    testWidgets('should change language dropdown', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Find and tap the language dropdown
      final languageDropdown = find.ancestor(
        of: find.text('English'),
        matching: find.byType(DropdownButton<String>),
      );
      await tester.tap(languageDropdown);
      await tester.pumpAndSettle();

      // Select Spanish
      await tester.tap(find.text('Spanish').last);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Spanish'), findsOneWidget);
    });

    testWidgets('should change theme dropdown', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Find and tap the theme dropdown
      final themeDropdown = find.ancestor(
        of: find.text('Light'),
        matching: find.byType(DropdownButton<String>),
      );
      await tester.tap(themeDropdown);
      await tester.pumpAndSettle();

      // Select Dark
      await tester.tap(find.text('Dark').last);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Dark'), findsOneWidget);
    });

    testWidgets('should call updateProfile and updatePreferences on save', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.updateProfile(
        displayName: anyNamed('displayName'),
        organization: anyNamed('organization'),
      )).thenAnswer((_) async {});
      when(mockUserProfileProvider.updatePreferences(
        notificationsEnabled: anyNamed('notificationsEnabled'),
        language: anyNamed('language'),
        theme: anyNamed('theme'),
        offlineMode: anyNamed('offlineMode'),
      )).thenAnswer((_) async {});

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Modify form data
      await tester.enterText(find.widgetWithText(TextFormField, 'Display Name'), 'Updated User');
      await tester.enterText(find.widgetWithText(TextFormField, 'Organization'), 'Updated Org');
      
      // Save the form
      await tester.tap(find.text('Save'));
      await tester.pump();

      // Assert
      verify(mockUserProfileProvider.updateProfile(
        displayName: 'Updated User',
        organization: 'Updated Org',
      )).called(1);
      verify(mockUserProfileProvider.updatePreferences(
        notificationsEnabled: true,
        language: 'English',
        theme: 'Light',
        offlineMode: false,
      )).called(1);
    });

    testWidgets('should navigate back on cancel', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Assert - Should navigate back (screen should be popped)
      expect(find.byType(ProfileEditScreen), findsNothing);
    });

    testWidgets('should display error message when present', (WidgetTester tester) async {
      // Arrange
      const errorMessage = 'Failed to update profile';
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.error);
      when(mockUserProfileProvider.errorMessage).thenReturn(errorMessage);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text(errorMessage), findsOneWidget);
    });

    testWidgets('should disable save button when loading', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(true);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loading);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      final saveButton = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(saveButton.onPressed, isNull);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display correct preference icons', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.byIcon(Icons.notifications_active), findsOneWidget);
      expect(find.byIcon(Icons.language), findsOneWidget);
      expect(find.byIcon(Icons.palette), findsOneWidget);
      expect(find.byIcon(Icons.cloud_done), findsOneWidget);
    });

    testWidgets('should show success message and navigate back on successful save', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.updateProfile(
        displayName: anyNamed('displayName'),
        organization: anyNamed('organization'),
      )).thenAnswer((_) async {});
      when(mockUserProfileProvider.updatePreferences(
        notificationsEnabled: anyNamed('notificationsEnabled'),
        language: anyNamed('language'),
        theme: anyNamed('theme'),
        offlineMode: anyNamed('offlineMode'),
      )).thenAnswer((_) async {});

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      await tester.tap(find.text('Save'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Profile updated successfully'), findsOneWidget);
      expect(find.byType(ProfileEditScreen), findsNothing);
    });
  });
}