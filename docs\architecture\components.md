# Components

## Presentation Layer

**Responsibility:** UI components, screens, and user interaction handling

**Key Interfaces:**
- Screen widgets (WalkaboutListScreen, HazardDetailScreen, etc.)
- Custom UI components (HazardCard, PhotoGallery, etc.)
- Navigation and routing

**Dependencies:** Business Logic Layer, State Management (Provider)

**Technology Stack:** Flutter widgets, Material Design, custom themes

## Business Logic Layer

**Responsibility:** Application business rules, validation, and state management

**Key Interfaces:**
- Providers (WalkaboutProvider, HazardProvider, AuthProvider)
- Services (LocationService, CameraService, SyncService)
- Validators and business rule enforcement

**Dependencies:** Data Layer, External Services

**Technology Stack:** Provider pattern, Dart business logic classes

## Data Layer

**Responsibility:** Data persistence, synchronization, and external API communication

**Key Interfaces:**
- Repository interfaces (WalkaboutRepository, HazardRepository)
- Data sources (LocalDataSource, RemoteDataSource)
- Sync management and conflict resolution

**Dependencies:** SQLite, Firebase services

**Technology Stack:** Repository pattern, sqflite, Firebase SDKs

## External Services Layer

**Responsibility:** Integration with device capabilities and external APIs

**Key Interfaces:**
- Camera service for photo capture
- Location service for GPS coordinates
- File system for local storage
- Network connectivity monitoring

**Dependencies:** Device APIs, Flutter plugins

**Technology Stack:** image_picker, geolocator, connectivity_plus

## Component Diagrams

```mermaid
graph TB
    subgraph "Presentation Layer"
        A[Screens] --> B[Widgets]
        B --> C[Navigation]
    end
    
    subgraph "Business Logic Layer"
        D[Providers] --> E[Services]
        E --> F[Validators]
    end
    
    subgraph "Data Layer"
        G[Repositories] --> H[Local DataSource]
        G --> I[Remote DataSource]
        H --> J[SQLite]
        I --> K[Firebase]
    end
    
    subgraph "External Services"
        L[Camera Service]
        M[Location Service]
        N[File Service]
    end
    
    A --> D
    D --> G
    E --> L
    E --> M
    E --> N
```
