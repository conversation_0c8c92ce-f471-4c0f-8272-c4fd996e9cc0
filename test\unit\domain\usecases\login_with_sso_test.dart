import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/entities/user_preferences.dart';
import 'package:safestride/domain/repositories/auth_repository.dart';
import 'package:safestride/domain/usecases/login_with_sso.dart';

import 'login_with_sso_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  late LoginWithSSOUseCase useCase;
  late MockAuthRepository mockAuthRepository;
  late User testUser;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    useCase = LoginWithSSOUseCase(mockAuthRepository);
    testUser = User(
      uid: 'google-uid-123',
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      preferences: UserPreferences(
        theme: 'light',
        language: 'en',
        notifications: true,
      ),
    );
  });

  group('LoginWithSSOUseCase', () {
    test('should return user when Google SSO login is successful', () async {
      // Arrange
      when(mockAuthRepository.loginWithSSO())
          .thenAnswer((_) async => testUser);

      // Act
      final result = await useCase.call();

      // Assert
      expect(result, equals(testUser));
      verify(mockAuthRepository.loginWithSSO()).called(1);
    });

    test('should throw exception when SSO login is cancelled by user', () async {
      // Arrange
      const errorMessage = 'User cancelled login';
      when(mockAuthRepository.loginWithSSO())
          .thenThrow(Exception(errorMessage));

      // Act & Assert
      expect(
        () => useCase.call(),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('SSO login failed: Exception: $errorMessage'),
        )),
      );
      verify(mockAuthRepository.loginWithSSO()).called(1);
    });

    test('should throw exception when network error occurs during SSO', () async {
      // Arrange
      const errorMessage = 'Network error';
      when(mockAuthRepository.loginWithSSO())
          .thenThrow(Exception(errorMessage));

      // Act & Assert
      expect(
        () => useCase.call(),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('SSO login failed: Exception: $errorMessage'),
        )),
      );
      verify(mockAuthRepository.loginWithSSO()).called(1);
    });

    test('should throw exception when Google Play Services unavailable', () async {
      // Arrange
      const errorMessage = 'Google Play Services unavailable';
      when(mockAuthRepository.loginWithSSO())
          .thenThrow(Exception(errorMessage));

      // Act & Assert
      expect(
        () => useCase.call(),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('SSO login failed: Exception: $errorMessage'),
        )),
      );
      verify(mockAuthRepository.loginWithSSO()).called(1);
    });

    test('should throw exception when SSO account already exists with different provider', () async {
      // Arrange
      const errorMessage = 'Account exists with different credential';
      when(mockAuthRepository.loginWithSSO())
          .thenThrow(Exception(errorMessage));

      // Act & Assert
      expect(
        () => useCase.call(),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('SSO login failed: Exception: $errorMessage'),
        )),
      );
      verify(mockAuthRepository.loginWithSSO()).called(1);
    });

    test('should throw exception when SSO provider is disabled', () async {
      // Arrange
      const errorMessage = 'SSO provider disabled';
      when(mockAuthRepository.loginWithSSO())
          .thenThrow(Exception(errorMessage));

      // Act & Assert
      expect(
        () => useCase.call(),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('SSO login failed: Exception: $errorMessage'),
        )),
      );
      verify(mockAuthRepository.loginWithSSO()).called(1);
    });

    test('should cache user credentials after successful SSO login', () async {
      // Arrange
      when(mockAuthRepository.loginWithSSO())
          .thenAnswer((_) async => testUser);
      when(mockAuthRepository.cacheCredentials(any))
          .thenAnswer((_) async => {});

      // Act
      final result = await useCase.call();

      // Assert
      expect(result, equals(testUser));
      verify(mockAuthRepository.loginWithSSO()).called(1);
      verify(mockAuthRepository.cacheCredentials(testUser)).called(1);
    });

    test('should still return user even if caching fails', () async {
      // Arrange
      when(mockAuthRepository.loginWithSSO())
          .thenAnswer((_) async => testUser);
      when(mockAuthRepository.cacheCredentials(any))
          .thenThrow(Exception('Cache error'));

      // Act
      final result = await useCase.call();

      // Assert
      expect(result, equals(testUser));
      verify(mockAuthRepository.loginWithSSO()).called(1);
      verify(mockAuthRepository.cacheCredentials(testUser)).called(1);
    });
  });
}