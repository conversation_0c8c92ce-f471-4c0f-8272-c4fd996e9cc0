// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in safestride/test/unit/domain/usecases/update_hazard_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/hazard.dart' as _i2;
import 'package:safestride/domain/entities/walkabout.dart' as _i5;
import 'package:safestride/domain/repositories/hazard_repository.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeHazard_0 extends _i1.SmartFake implements _i2.Hazard {
  _FakeHazard_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [HazardRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockHazardRepository extends _i1.Mock implements _i3.HazardRepository {
  MockHazardRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.Hazard> createHazard(_i2.Hazard? hazard) =>
      (super.noSuchMethod(
            Invocation.method(#createHazard, [hazard]),
            returnValue: _i4.Future<_i2.Hazard>.value(
              _FakeHazard_0(this, Invocation.method(#createHazard, [hazard])),
            ),
          )
          as _i4.Future<_i2.Hazard>);

  @override
  _i4.Future<_i2.Hazard?> getHazardById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardById, [id]),
            returnValue: _i4.Future<_i2.Hazard?>.value(),
          )
          as _i4.Future<_i2.Hazard?>);

  @override
  _i4.Future<List<_i2.Hazard>> getHazardsByWalkaboutId(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsByWalkaboutId, [walkaboutId]),
            returnValue: _i4.Future<List<_i2.Hazard>>.value(<_i2.Hazard>[]),
          )
          as _i4.Future<List<_i2.Hazard>>);

  @override
  _i4.Future<List<_i2.Hazard>> getHazardsBySeverity(
    String? walkaboutId,
    _i2.HazardSeverity? severity,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsBySeverity, [walkaboutId, severity]),
            returnValue: _i4.Future<List<_i2.Hazard>>.value(<_i2.Hazard>[]),
          )
          as _i4.Future<List<_i2.Hazard>>);

  @override
  _i4.Future<List<_i2.Hazard>> getHazardsByCategory(
    String? walkaboutId,
    _i2.HazardCategory? category,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsByCategory, [walkaboutId, category]),
            returnValue: _i4.Future<List<_i2.Hazard>>.value(<_i2.Hazard>[]),
          )
          as _i4.Future<List<_i2.Hazard>>);

  @override
  _i4.Future<_i2.Hazard> updateHazard(_i2.Hazard? hazard) =>
      (super.noSuchMethod(
            Invocation.method(#updateHazard, [hazard]),
            returnValue: _i4.Future<_i2.Hazard>.value(
              _FakeHazard_0(this, Invocation.method(#updateHazard, [hazard])),
            ),
          )
          as _i4.Future<_i2.Hazard>);

  @override
  _i4.Future<bool> deleteHazard(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteHazard, [id]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<List<_i2.Hazard>> getHazardsToSync() =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsToSync, []),
            returnValue: _i4.Future<List<_i2.Hazard>>.value(<_i2.Hazard>[]),
          )
          as _i4.Future<List<_i2.Hazard>>);

  @override
  _i4.Future<_i2.Hazard> updateSyncStatus(
    String? id,
    _i5.SyncStatus? syncStatus,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateSyncStatus, [id, syncStatus]),
            returnValue: _i4.Future<_i2.Hazard>.value(
              _FakeHazard_0(
                this,
                Invocation.method(#updateSyncStatus, [id, syncStatus]),
              ),
            ),
          )
          as _i4.Future<_i2.Hazard>);

  @override
  _i4.Future<List<_i2.Hazard>> searchHazards(
    String? walkaboutId,
    String? query,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#searchHazards, [walkaboutId, query]),
            returnValue: _i4.Future<List<_i2.Hazard>>.value(<_i2.Hazard>[]),
          )
          as _i4.Future<List<_i2.Hazard>>);

  @override
  _i4.Future<List<_i2.Hazard>> getHazardsInArea(
    String? walkaboutId,
    _i5.GeoPoint? center,
    double? radiusMeters,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsInArea, [
              walkaboutId,
              center,
              radiusMeters,
            ]),
            returnValue: _i4.Future<List<_i2.Hazard>>.value(<_i2.Hazard>[]),
          )
          as _i4.Future<List<_i2.Hazard>>);

  @override
  _i4.Future<Map<String, dynamic>> getHazardStatistics(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardStatistics, [walkaboutId]),
            returnValue: _i4.Future<Map<String, dynamic>>.value(
              <String, dynamic>{},
            ),
          )
          as _i4.Future<Map<String, dynamic>>);
}
