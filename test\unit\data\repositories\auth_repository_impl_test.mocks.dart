// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in safestride/test/unit/data/repositories/auth_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/data/datasources/local/auth_local_datasource.dart'
    as _i5;
import 'package:safestride/data/datasources/remote/auth_remote_datasource.dart'
    as _i3;
import 'package:safestride/data/models/user_model.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUserModel_0 extends _i1.SmartFake implements _i2.UserModel {
  _FakeUserModel_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRemoteDataSource extends _i1.Mock
    implements _i3.AuthRemoteDataSource {
  MockAuthRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.UserModel?> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i4.Stream<_i2.UserModel?>.empty(),
          )
          as _i4.Stream<_i2.UserModel?>);

  @override
  _i4.Future<_i2.UserModel> registerWithEmail({
    required String? email,
    required String? password,
    String? displayName,
    String? organization,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerWithEmail, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
              #organization: organization,
            }),
            returnValue: _i4.Future<_i2.UserModel>.value(
              _FakeUserModel_0(
                this,
                Invocation.method(#registerWithEmail, [], {
                  #email: email,
                  #password: password,
                  #displayName: displayName,
                  #organization: organization,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.UserModel>);

  @override
  _i4.Future<_i2.UserModel> loginWithEmail({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginWithEmail, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i4.Future<_i2.UserModel>.value(
              _FakeUserModel_0(
                this,
                Invocation.method(#loginWithEmail, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.UserModel>);

  @override
  _i4.Future<_i2.UserModel> loginWithSSO() =>
      (super.noSuchMethod(
            Invocation.method(#loginWithSSO, []),
            returnValue: _i4.Future<_i2.UserModel>.value(
              _FakeUserModel_0(this, Invocation.method(#loginWithSSO, [])),
            ),
          )
          as _i4.Future<_i2.UserModel>);

  @override
  _i4.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> resetPassword({required String? email}) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [], {#email: email}),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.UserModel?> getCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUser, []),
            returnValue: _i4.Future<_i2.UserModel?>.value(),
          )
          as _i4.Future<_i2.UserModel?>);

  @override
  _i4.Future<bool> isAuthenticated() =>
      (super.noSuchMethod(
            Invocation.method(#isAuthenticated, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> refreshToken() =>
      (super.noSuchMethod(
            Invocation.method(#refreshToken, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

/// A class which mocks [AuthLocalDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthLocalDataSource extends _i1.Mock
    implements _i5.AuthLocalDataSource {
  MockAuthLocalDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> cacheUser(_i2.UserModel? user) =>
      (super.noSuchMethod(
            Invocation.method(#cacheUser, [user]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.UserModel?> getCachedUser() =>
      (super.noSuchMethod(
            Invocation.method(#getCachedUser, []),
            returnValue: _i4.Future<_i2.UserModel?>.value(),
          )
          as _i4.Future<_i2.UserModel?>);

  @override
  _i4.Future<void> clearCachedUser() =>
      (super.noSuchMethod(
            Invocation.method(#clearCachedUser, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> hasValidCache() =>
      (super.noSuchMethod(
            Invocation.method(#hasValidCache, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> updateCachedUser(_i2.UserModel? user) =>
      (super.noSuchMethod(
            Invocation.method(#updateCachedUser, [user]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
