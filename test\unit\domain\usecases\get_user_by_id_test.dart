import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/user_repository.dart';
import 'package:safestride/domain/usecases/get_user_by_id.dart';

import 'get_user_by_id_test.mocks.dart';

@GenerateMocks([UserRepository])
void main() {
  late GetUserByIdUseCase useCase;
  late MockUserRepository mockUserRepository;

  setUp(() {
    mockUserRepository = MockUserRepository();
    useCase = GetUserByIdUseCase(mockUserRepository);
  });

  group('GetUserByIdUseCase', () {
    const testUid = 'test-uid-123';
    final testUser = User(
      id: testUid,
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime(2024, 1, 1),
      lastLoginAt: DateTime(2024, 1, 2),
      preferences: UserPreferences(),
    );

    test('should return user when repository call succeeds', () async {
      // Arrange
      when(mockUserRepository.getUserById(testUid))
          .thenAnswer((_) async => testUser);

      // Act
      final result = await useCase(testUid);

      // Assert
      expect(result, equals(testUser));
      verify(mockUserRepository.getUserById(testUid)).called(1);
    });

    test('should return null when user not found', () async {
      // Arrange
      when(mockUserRepository.getUserById(testUid))
          .thenAnswer((_) async => null);

      // Act
      final result = await useCase(testUid);

      // Assert
      expect(result, isNull);
      verify(mockUserRepository.getUserById(testUid)).called(1);
    });

    test('should throw exception when repository throws exception', () async {
      // Arrange
      when(mockUserRepository.getUserById(testUid))
          .thenThrow(Exception('Repository error'));

      // Act & Assert
      expect(
        () async => await useCase(testUid),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Failed to get user'),
        )),
      );
      verify(mockUserRepository.getUserById(testUid)).called(1);
    });

    test('should handle empty uid parameter', () async {
      // Arrange
      const emptyUid = '';
      when(mockUserRepository.getUserById(emptyUid))
          .thenAnswer((_) async => null);

      // Act
      final result = await useCase(emptyUid);

      // Assert
      expect(result, isNull);
      verify(mockUserRepository.getUserById(emptyUid)).called(1);
    });
  });
}