import 'dart:math' as math;
import 'package:sqflite/sqflite.dart';
import '../../models/hazard_model.dart';
import '../../../domain/entities/hazard.dart';
import '../../../domain/entities/walkabout.dart';

/// Interface for local hazard data operations
abstract class HazardLocalDataSource {
  /// Create a new hazard in local storage
  Future<Hazard> createHazard(Hazard hazard);

  /// Get hazard by ID from local storage
  Future<Hazard?> getHazardById(String id);

  /// Get all hazards for a specific walkabout
  Future<List<Hazard>> getHazardsByWalkaboutId(String walkaboutId);

  /// Get hazards by severity for a specific walkabout
  Future<List<Hazard>> getHazardsBySeverity(
    String walkaboutId,
    HazardSeverity severity,
  );

  /// Get hazards by category for a specific walkabout
  Future<List<Hazard>> getHazardsByCategory(
    String walkaboutId,
    HazardCategory category,
  );

  /// Update an existing hazard in local storage
  Future<Hazard> updateHazard(Hazard hazard);

  /// Delete a hazard by ID from local storage
  Future<bool> deleteHazard(String id);

  /// Get hazards that need to be synced
  Future<List<Hazard>> getHazardsToSync();

  /// Update sync status for a hazard
  Future<Hazard> updateSyncStatus(String id, SyncStatus syncStatus);

  /// Search hazards by title or description
  Future<List<Hazard>> searchHazards(String walkaboutId, String query);

  /// Get hazards within a geographic area
  Future<List<Hazard>> getHazardsInArea(
    String walkaboutId,
    GeoPoint center,
    double radiusMeters,
  );

  /// Get hazard statistics for a walkabout
  Future<Map<String, dynamic>> getHazardStatistics(String walkaboutId);
}

/// Implementation of HazardLocalDataSource using SQLite
class HazardLocalDataSourceImpl implements HazardLocalDataSource {
  final Database _database;

  HazardLocalDataSourceImpl(this._database);

  /// Create hazards table in database
  static Future<void> createTable(Database db) async {
    await db.execute('''
      CREATE TABLE IF NOT EXISTS hazards(
        id TEXT PRIMARY KEY,
        walkabout_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT,
        severity TEXT NOT NULL,
        category TEXT NOT NULL,
        location_lat REAL,
        location_lng REAL,
        photos TEXT NOT NULL DEFAULT '[]',
        notes TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        sync_status TEXT NOT NULL DEFAULT 'local',
        FOREIGN KEY (walkabout_id) REFERENCES walkabouts (id) ON DELETE CASCADE
      )
      ''');

    // Create indexes for performance
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_hazards_walkabout_id ON hazards(walkabout_id)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_hazards_severity ON hazards(severity)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_hazards_category ON hazards(category)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_hazards_sync_status ON hazards(sync_status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_hazards_created_at ON hazards(created_at)',
    );
  }

  @override
  Future<Hazard> createHazard(Hazard hazard) async {
    try {
      // Convert domain entity to model
      final hazardModel = HazardModel.fromEntity(hazard);

      // Insert into database
      await _database.insert(
        'hazards',
        hazardModel.toMap(),
        conflictAlgorithm: ConflictAlgorithm.fail,
      );

      return hazardModel;
    } catch (e) {
      throw Exception('Failed to create hazard: ${e.toString()}');
    }
  }

  @override
  Future<Hazard?> getHazardById(String id) async {
    try {
      final result = await _database.query(
        'hazards',
        where: 'id = ?',
        whereArgs: [id],
      );

      if (result.isEmpty) {
        return null;
      }

      return HazardModel.fromMap(result.first);
    } catch (e) {
      throw Exception('Failed to get hazard by ID: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsByWalkaboutId(String walkaboutId) async {
    try {
      final result = await _database.query(
        'hazards',
        where: 'walkabout_id = ?',
        whereArgs: [walkaboutId],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => HazardModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to get hazards by walkabout ID: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsBySeverity(
    String walkaboutId,
    HazardSeverity severity,
  ) async {
    try {
      final result = await _database.query(
        'hazards',
        where: 'walkabout_id = ? AND severity = ?',
        whereArgs: [walkaboutId, severity.name],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => HazardModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to get hazards by severity: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsByCategory(
    String walkaboutId,
    HazardCategory category,
  ) async {
    try {
      final result = await _database.query(
        'hazards',
        where: 'walkabout_id = ? AND category = ?',
        whereArgs: [walkaboutId, category.name],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => HazardModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to get hazards by category: ${e.toString()}');
    }
  }

  @override
  Future<Hazard> updateHazard(Hazard hazard) async {
    try {
      // Convert domain entity to model
      final hazardModel = HazardModel.fromEntity(hazard);

      // Update in database
      final rowsAffected = await _database.update(
        'hazards',
        hazardModel.toMap(),
        where: 'id = ?',
        whereArgs: [hazard.id],
      );

      if (rowsAffected == 0) {
        throw Exception('Hazard with ID ${hazard.id} not found');
      }

      return hazardModel;
    } catch (e) {
      throw Exception('Failed to update hazard: ${e.toString()}');
    }
  }

  @override
  Future<bool> deleteHazard(String id) async {
    try {
      final rowsAffected = await _database.delete(
        'hazards',
        where: 'id = ?',
        whereArgs: [id],
      );

      return rowsAffected > 0;
    } catch (e) {
      throw Exception('Failed to delete hazard: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsToSync() async {
    try {
      final result = await _database.query(
        'hazards',
        where: 'sync_status != ?',
        whereArgs: ['synced'],
        orderBy: 'updated_at ASC',
      );

      return result.map((map) => HazardModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to get hazards to sync: ${e.toString()}');
    }
  }

  @override
  Future<Hazard> updateSyncStatus(String id, SyncStatus syncStatus) async {
    try {
      // Update only sync status
      final rowsAffected = await _database.update(
        'hazards',
        {'sync_status': syncStatus.name},
        where: 'id = ?',
        whereArgs: [id],
      );

      if (rowsAffected == 0) {
        throw Exception('Hazard with ID $id not found');
      }

      // Return updated hazard
      final updatedHazard = await getHazardById(id);
      if (updatedHazard == null) {
        throw Exception('Failed to retrieve updated hazard');
      }

      return updatedHazard;
    } catch (e) {
      throw Exception('Failed to update sync status: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> searchHazards(String walkaboutId, String query) async {
    try {
      final searchQuery = '%${query.toLowerCase()}%';
      final result = await _database.query(
        'hazards',
        where:
            'walkabout_id = ? AND (LOWER(title) LIKE ? OR LOWER(description) LIKE ? OR LOWER(notes) LIKE ?)',
        whereArgs: [walkaboutId, searchQuery, searchQuery, searchQuery],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => HazardModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to search hazards: ${e.toString()}');
    }
  }

  @override
  Future<List<Hazard>> getHazardsInArea(
    String walkaboutId,
    GeoPoint center,
    double radiusMeters,
  ) async {
    try {
      // Simple bounding box calculation (approximate)
      // For more precise calculations, consider using a geospatial library
      const double earthRadius = 6371000; // Earth radius in meters
      final double latDelta = (radiusMeters / earthRadius) * (180 / 3.14159);
      final double lngDelta =
          latDelta / math.cos(3.14159 * center.latitude / 180);

      final double minLat = center.latitude - latDelta;
      final double maxLat = center.latitude + latDelta;
      final double minLng = center.longitude - lngDelta;
      final double maxLng = center.longitude + lngDelta;

      final result = await _database.query(
        'hazards',
        where:
            'walkabout_id = ? AND location_lat BETWEEN ? AND ? AND location_lng BETWEEN ? AND ?',
        whereArgs: [walkaboutId, minLat, maxLat, minLng, maxLng],
        orderBy: 'created_at DESC',
      );

      return result.map((map) => HazardModel.fromMap(map)).toList();
    } catch (e) {
      throw Exception('Failed to get hazards in area: ${e.toString()}');
    }
  }

  @override
  Future<Map<String, dynamic>> getHazardStatistics(String walkaboutId) async {
    try {
      // Get total count
      final totalResult = await _database.rawQuery(
        'SELECT COUNT(*) as total FROM hazards WHERE walkabout_id = ?',
        [walkaboutId],
      );
      final int total = totalResult.first['total'] as int;

      // Get counts by severity
      final severityResult = await _database.rawQuery(
        'SELECT severity, COUNT(*) as count FROM hazards WHERE walkabout_id = ? GROUP BY severity',
        [walkaboutId],
      );

      // Get counts by category
      final categoryResult = await _database.rawQuery(
        'SELECT category, COUNT(*) as count FROM hazards WHERE walkabout_id = ? GROUP BY category',
        [walkaboutId],
      );

      return {
        'total': total,
        'bySeverity': {
          for (final row in severityResult)
            row['severity'] as String: row['count'] as int,
        },
        'byCategory': {
          for (final row in categoryResult)
            row['category'] as String: row['count'] as int,
        },
      };
    } catch (e) {
      throw Exception('Failed to get hazard statistics: ${e.toString()}');
    }
  }
}
