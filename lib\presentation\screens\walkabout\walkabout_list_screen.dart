import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../domain/entities/walkabout.dart';
import '../../providers/walkabout_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/walkabout/walkabout_card.dart';
import 'walkabout_creation_screen.dart';
import 'walkabout_detail_screen.dart';

/// Screen displaying list of walkabouts with dashboard functionality
///
/// Shows walkabouts organized by status with filtering and creation options.
/// Serves as the main dashboard for walkabout management.
class WalkaboutListScreen extends StatefulWidget {
  const WalkaboutListScreen({super.key});

  @override
  State<WalkaboutListScreen> createState() => _WalkaboutListScreenState();
}

class _WalkaboutListScreenState extends State<WalkaboutListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final List<WalkaboutStatus> _statusTabs = [
    WalkaboutStatus.draft,
    WalkaboutStatus.inProgress,
    WalkaboutStatus.completed,
    WalkaboutStatus.archived,
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _statusTabs.length, vsync: this);
    _loadWalkabouts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadWalkabouts() {
    final authProvider = context.read<AuthProvider>();
    final walkaboutProvider = context.read<WalkaboutProvider>();

    if (authProvider.currentUser != null) {
      walkaboutProvider.loadWalkabouts(authProvider.currentUser!.uid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Walkabouts'),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs:
              _statusTabs.map((status) {
                return Tab(
                  child: Consumer<WalkaboutProvider>(
                    builder: (context, provider, child) {
                      final count = provider.getWalkaboutsCountByStatus(status);
                      return Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          _getStatusIcon(status),
                          const SizedBox(width: 4.0),
                          Text(status.displayName),
                          if (count > 0) ...[
                            const SizedBox(width: 4.0),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6.0,
                                vertical: 2.0,
                              ),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary,
                                borderRadius: BorderRadius.circular(10.0),
                              ),
                              child: Text(
                                count.toString(),
                                style: TextStyle(
                                  color:
                                      Theme.of(context).colorScheme.onPrimary,
                                  fontSize: 12.0,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      );
                    },
                  ),
                );
              }).toList(),
        ),
        actions: [
          IconButton(
            onPressed: _loadWalkabouts,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Consumer2<WalkaboutProvider, AuthProvider>(
        builder: (context, walkaboutProvider, authProvider, child) {
          return LoadingOverlay(
            isLoading: walkaboutProvider.isLoading,
            child: TabBarView(
              controller: _tabController,
              children:
                  _statusTabs.map((status) {
                    return _buildWalkaboutList(walkaboutProvider, status);
                  }).toList(),
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewWalkabout,
        icon: const Icon(Icons.add),
        label: const Text('New Walkabout'),
      ),
    );
  }

  Widget _buildWalkaboutList(
    WalkaboutProvider provider,
    WalkaboutStatus status,
  ) {
    final walkabouts = provider.getWalkaboutsByStatus(status);

    if (provider.error != null) {
      return _buildErrorState(provider);
    }

    if (walkabouts.isEmpty) {
      return _buildEmptyState(status);
    }

    return RefreshIndicator(
      onRefresh: () async => _loadWalkabouts(),
      child: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: walkabouts.length,
        itemBuilder: (context, index) {
          final walkabout = walkabouts[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12.0),
            child: WalkaboutCard(
              walkabout: walkabout,
              onTap: () => _onWalkaboutTap(walkabout),
              onStatusChanged:
                  (newStatus) => _updateWalkaboutStatus(walkabout, newStatus),
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorState(WalkaboutProvider provider) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.0,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16.0),
          Text(
            'Error loading walkabouts',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8.0),
          Text(
            provider.error!,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24.0),
          ElevatedButton(
            onPressed: () {
              provider.clearError();
              _loadWalkabouts();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(WalkaboutStatus status) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _getStatusIcon(status, size: 64.0),
          const SizedBox(height: 16.0),
          Text(
            'No ${status.displayName.toLowerCase()} walkabouts',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8.0),
          Text(
            _getEmptyStateMessage(status),
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          if (status == WalkaboutStatus.draft) ...[
            const SizedBox(height: 24.0),
            ElevatedButton.icon(
              onPressed: _createNewWalkabout,
              icon: const Icon(Icons.add),
              label: const Text('Create Walkabout'),
            ),
          ],
        ],
      ),
    );
  }

  Widget _getStatusIcon(WalkaboutStatus status, {double size = 20.0}) {
    switch (status) {
      case WalkaboutStatus.draft:
        return Icon(Icons.edit_note, size: size);
      case WalkaboutStatus.inProgress:
        return Icon(Icons.play_circle, size: size);
      case WalkaboutStatus.completed:
        return Icon(Icons.check_circle, size: size);
      case WalkaboutStatus.archived:
        return Icon(Icons.archive, size: size);
    }
  }

  String _getEmptyStateMessage(WalkaboutStatus status) {
    switch (status) {
      case WalkaboutStatus.draft:
        return 'Create a new walkabout to get started with safety inspections.';
      case WalkaboutStatus.inProgress:
        return 'No active walkabouts. Start a draft walkabout to begin.';
      case WalkaboutStatus.completed:
        return 'Complete walkabouts will appear here.';
      case WalkaboutStatus.archived:
        return 'Archived walkabouts will appear here.';
    }
  }

  void _createNewWalkabout() async {
    final result = await Navigator.of(context).push<Walkabout>(
      MaterialPageRoute(builder: (context) => const WalkaboutCreationScreen()),
    );

    if (result != null) {
      // Refresh the list to show the new walkabout
      _loadWalkabouts();
    }
  }

  void _onWalkaboutTap(Walkabout walkabout) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => WalkaboutDetailScreen(walkabout: walkabout),
      ),
    );
  }

  void _updateWalkaboutStatus(
    Walkabout walkabout,
    WalkaboutStatus newStatus,
  ) async {
    final provider = context.read<WalkaboutProvider>();
    final updatedWalkabout = walkabout.copyWith(status: newStatus);

    await provider.updateWalkabout(updatedWalkabout);
  }
}
