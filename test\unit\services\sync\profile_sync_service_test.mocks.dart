// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in safestride/test/unit/services/sync/profile_sync_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:connectivity_plus/connectivity_plus.dart' as _i6;
import 'package:connectivity_plus_platform_interface/connectivity_plus_platform_interface.dart'
    as _i7;
import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/user.dart' as _i2;
import 'package:safestride/domain/repositories/user_repository.dart' as _i3;
import 'package:safestride/domain/usecases/sync_user.dart' as _i5;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUser_0 extends _i1.SmartFake implements _i2.User {
  _FakeUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [UserRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRepository extends _i1.Mock implements _i3.UserRepository {
  MockUserRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User?> getUserById(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#getUserById, [uid]),
            returnValue: _i4.Future<_i2.User?>.value(),
          )
          as _i4.Future<_i2.User?>);

  @override
  _i4.Future<_i2.User> updateUser(_i2.User? user) =>
      (super.noSuchMethod(
            Invocation.method(#updateUser, [user]),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(this, Invocation.method(#updateUser, [user])),
            ),
          )
          as _i4.Future<_i2.User>);

  @override
  _i4.Future<_i2.User> updateUserPreferences(
    String? uid,
    _i2.UserPreferences? preferences,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserPreferences, [uid, preferences]),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(
                this,
                Invocation.method(#updateUserPreferences, [uid, preferences]),
              ),
            ),
          )
          as _i4.Future<_i2.User>);

  @override
  _i4.Future<_i2.User> updateUserRole(String? uid, _i2.UserRole? role) =>
      (super.noSuchMethod(
            Invocation.method(#updateUserRole, [uid, role]),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(
                this,
                Invocation.method(#updateUserRole, [uid, role]),
              ),
            ),
          )
          as _i4.Future<_i2.User>);

  @override
  _i4.Future<_i2.User> syncUser(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#syncUser, [uid]),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(this, Invocation.method(#syncUser, [uid])),
            ),
          )
          as _i4.Future<_i2.User>);

  @override
  _i4.Future<_i2.User?> getCachedUser(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#getCachedUser, [uid]),
            returnValue: _i4.Future<_i2.User?>.value(),
          )
          as _i4.Future<_i2.User?>);

  @override
  _i4.Future<void> cacheUser(_i2.User? user) =>
      (super.noSuchMethod(
            Invocation.method(#cacheUser, [user]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

/// A class which mocks [SyncUserUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockSyncUserUseCase extends _i1.Mock implements _i5.SyncUserUseCase {
  MockSyncUserUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User> call(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#call, [uid]),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(this, Invocation.method(#call, [uid])),
            ),
          )
          as _i4.Future<_i2.User>);
}

/// A class which mocks [Connectivity].
///
/// See the documentation for Mockito's code generation for more information.
class MockConnectivity extends _i1.Mock implements _i6.Connectivity {
  MockConnectivity() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i7.ConnectivityResult> get onConnectivityChanged =>
      (super.noSuchMethod(
            Invocation.getter(#onConnectivityChanged),
            returnValue: _i4.Stream<_i7.ConnectivityResult>.empty(),
          )
          as _i4.Stream<_i7.ConnectivityResult>);

  @override
  _i4.Future<_i7.ConnectivityResult> checkConnectivity() =>
      (super.noSuchMethod(
            Invocation.method(#checkConnectivity, []),
            returnValue: _i4.Future<_i7.ConnectivityResult>.value(
              _i7.ConnectivityResult.bluetooth,
            ),
          )
          as _i4.Future<_i7.ConnectivityResult>);
}
