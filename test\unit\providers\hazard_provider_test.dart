import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/entities/hazard.dart';
import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/domain/usecases/create_hazard.dart';
import 'package:safestride/domain/usecases/update_hazard.dart';
import 'package:safestride/domain/repositories/hazard_repository.dart';
import 'package:safestride/services/camera/camera_service.dart';
import 'package:safestride/services/location/location_service.dart';
import 'package:safestride/services/voice/voice_input_service.dart';
import 'package:safestride/presentation/providers/hazard_provider.dart';

import 'hazard_provider_test.mocks.dart';

@GenerateMocks([
  CreateHazardUseCase,
  UpdateHazardUseCase,
  HazardRepository,
  CameraService,
  LocationService,
  VoiceInputService,
])
void main() {
  group('HazardProvider Tests', () {
    late HazardProvider provider;
    late MockCreateHazardUseCase mockCreateHazardUseCase;
    late MockUpdateHazardUseCase mockUpdateHazardUseCase;
    late MockHazardRepository mockHazardRepository;
    late MockCameraService mockCameraService;
    late MockLocationService mockLocationService;
    late MockVoiceInputService mockVoiceInputService;

    setUp(() {
      mockCreateHazardUseCase = MockCreateHazardUseCase();
      mockUpdateHazardUseCase = MockUpdateHazardUseCase();
      mockHazardRepository = MockHazardRepository();
      mockCameraService = MockCameraService();
      mockLocationService = MockLocationService();
      mockVoiceInputService = MockVoiceInputService();

      provider = HazardProvider(
        createHazardUseCase: mockCreateHazardUseCase,
        updateHazardUseCase: mockUpdateHazardUseCase,
        hazardRepository: mockHazardRepository,
        cameraService: mockCameraService,
        locationService: mockLocationService,
        voiceInputService: mockVoiceInputService,
      );
    });

    group('Initial State', () {
      test('should have correct initial values', () {
        expect(provider.hazards, isEmpty);
        expect(provider.currentHazard, isNull);
        expect(provider.isLoading, isFalse);
        expect(provider.error, isNull);
        expect(provider.selectedSeverity, equals(HazardSeverity.medium));
        expect(provider.selectedCategory, equals(HazardCategory.other));
        expect(provider.selectedPhotos, isEmpty);
        expect(provider.selectedLocation, isNull);
        expect(provider.isVoiceInputActive, isFalse);
        expect(provider.voiceInputText, isEmpty);
      });
    });

    group('Load Hazards', () {
      test('should load hazards successfully', () async {
        // Arrange
        const walkaboutId = 'walkabout_123';
        final hazards = [
          Hazard(
            id: 'hazard_1',
            walkaboutId: walkaboutId,
            title: 'Test Hazard 1',
            severity: HazardSeverity.high,
            category: HazardCategory.slipTripFall,
            photos: [],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            syncStatus: SyncStatus.local,
          ),
          Hazard(
            id: 'hazard_2',
            walkaboutId: walkaboutId,
            title: 'Test Hazard 2',
            severity: HazardSeverity.low,
            category: HazardCategory.electrical,
            photos: [],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            syncStatus: SyncStatus.local,
          ),
        ];

        when(mockHazardRepository.getHazardsByWalkaboutId(walkaboutId))
            .thenAnswer((_) async => hazards);

        // Act
        await provider.loadHazards(walkaboutId);

        // Assert
        expect(provider.hazards, equals(hazards));
        expect(provider.isLoading, isFalse);
        expect(provider.error, isNull);
        verify(mockHazardRepository.getHazardsByWalkaboutId(walkaboutId)).called(1);
      });

      test('should handle load hazards error', () async {
        // Arrange
        const walkaboutId = 'walkabout_123';
        const errorMessage = 'Failed to load hazards';

        when(mockHazardRepository.getHazardsByWalkaboutId(walkaboutId))
            .thenThrow(Exception(errorMessage));

        // Act
        await provider.loadHazards(walkaboutId);

        // Assert
        expect(provider.hazards, isEmpty);
        expect(provider.isLoading, isFalse);
        expect(provider.error, contains(errorMessage));
      });
    });

    group('Create Hazard', () {
      test('should create hazard successfully', () async {
        // Arrange
        const walkaboutId = 'walkabout_123';
        const title = 'New Hazard';
        const description = 'Hazard description';
        const severity = HazardSeverity.high;
        const category = HazardCategory.fire;

        final expectedHazard = Hazard(
          id: 'hazard_new',
          walkaboutId: walkaboutId,
          title: title,
          description: description,
          severity: severity,
          category: category,
          photos: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          syncStatus: SyncStatus.local,
        );

        when(mockCreateHazardUseCase.call(any))
            .thenAnswer((_) async => expectedHazard);

        // Act
        final result = await provider.createHazard(
          walkaboutId: walkaboutId,
          title: title,
          description: description,
          severity: severity,
          category: category,
        );

        // Assert
        expect(result, equals(expectedHazard));
        expect(provider.hazards, contains(expectedHazard));
        expect(provider.currentHazard, equals(expectedHazard));
        expect(provider.isLoading, isFalse);
        expect(provider.error, isNull);
        verify(mockCreateHazardUseCase.call(any)).called(1);
      });

      test('should handle create hazard error', () async {
        // Arrange
        const walkaboutId = 'walkabout_123';
        const title = 'New Hazard';
        const errorMessage = 'Failed to create hazard';

        when(mockCreateHazardUseCase.call(any))
            .thenThrow(Exception(errorMessage));

        // Act
        final result = await provider.createHazard(
          walkaboutId: walkaboutId,
          title: title,
          severity: HazardSeverity.medium,
          category: HazardCategory.other,
        );

        // Assert
        expect(result, isNull);
        expect(provider.hazards, isEmpty);
        expect(provider.isLoading, isFalse);
        expect(provider.error, contains(errorMessage));
      });
    });

    group('Update Hazard', () {
      test('should update hazard successfully', () async {
        // Arrange
        final originalHazard = Hazard(
          id: 'hazard_1',
          walkaboutId: 'walkabout_123',
          title: 'Original Title',
          severity: HazardSeverity.low,
          category: HazardCategory.other,
          photos: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          syncStatus: SyncStatus.local,
        );

        final updatedHazard = originalHazard.copyWith(
          title: 'Updated Title',
          severity: HazardSeverity.high,
        );

        // Add original hazard to provider
        provider.hazards.add(originalHazard);

        when(mockUpdateHazardUseCase.call(any))
            .thenAnswer((_) async => updatedHazard);

        // Act
        final result = await provider.updateHazard(
          id: originalHazard.id,
          title: 'Updated Title',
          severity: HazardSeverity.high,
        );

        // Assert
        expect(result, equals(updatedHazard));
        expect(provider.hazards.first, equals(updatedHazard));
        expect(provider.isLoading, isFalse);
        expect(provider.error, isNull);
        verify(mockUpdateHazardUseCase.call(any)).called(1);
      });
    });

    group('Photo Management', () {
      test('should capture photo successfully', () async {
        // Arrange
        const photoPath = '/path/to/photo.jpg';
        when(mockCameraService.capturePhoto())
            .thenAnswer((_) async => photoPath);

        // Act
        await provider.capturePhoto();

        // Assert
        expect(provider.selectedPhotos, contains(photoPath));
        expect(provider.isLoading, isFalse);
        expect(provider.error, isNull);
        verify(mockCameraService.capturePhoto()).called(1);
      });

      test('should handle capture photo error', () async {
        // Arrange
        const errorMessage = 'Camera error';
        when(mockCameraService.capturePhoto())
            .thenThrow(Exception(errorMessage));

        // Act
        await provider.capturePhoto();

        // Assert
        expect(provider.selectedPhotos, isEmpty);
        expect(provider.isLoading, isFalse);
        expect(provider.error, contains(errorMessage));
      });

      test('should remove photo from selection', () {
        // Arrange
        const photoPath = '/path/to/photo.jpg';
        provider.selectedPhotos.add(photoPath);

        // Act
        provider.removePhoto(photoPath);

        // Assert
        expect(provider.selectedPhotos, isEmpty);
      });

      test('should clear all photos', () {
        // Arrange
        provider.selectedPhotos.addAll(['/photo1.jpg', '/photo2.jpg']);

        // Act
        provider.clearPhotos();

        // Assert
        expect(provider.selectedPhotos, isEmpty);
      });
    });

    group('Location Management', () {
      test('should get current location successfully', () async {
        // Arrange
        const location = GeoPoint(latitude: 37.7749, longitude: -122.4194);
        when(mockLocationService.getCurrentLocation())
            .thenAnswer((_) async => location);

        // Act
        await provider.getCurrentLocation();

        // Assert
        expect(provider.selectedLocation, equals(location));
        expect(provider.isLoading, isFalse);
        expect(provider.error, isNull);
        verify(mockLocationService.getCurrentLocation()).called(1);
      });

      test('should handle location error', () async {
        // Arrange
        const errorMessage = 'Location error';
        when(mockLocationService.getCurrentLocation())
            .thenThrow(Exception(errorMessage));

        // Act
        await provider.getCurrentLocation();

        // Assert
        expect(provider.selectedLocation, isNull);
        expect(provider.isLoading, isFalse);
        expect(provider.error, contains(errorMessage));
      });

      test('should set selected location', () {
        // Arrange
        const location = GeoPoint(latitude: 37.7749, longitude: -122.4194);

        // Act
        provider.setSelectedLocation(location);

        // Assert
        expect(provider.selectedLocation, equals(location));
      });
    });

    group('Voice Input Management', () {
      test('should start voice input successfully', () async {
        // Arrange
        when(mockVoiceInputService.startListening(
          onResult: anyNamed('onResult'),
          onError: anyNamed('onError'),
        )).thenAnswer((_) async => true);

        // Act
        await provider.startVoiceInput();

        // Assert
        expect(provider.isVoiceInputActive, isTrue);
        verify(mockVoiceInputService.startListening(
          onResult: anyNamed('onResult'),
          onError: anyNamed('onError'),
        )).called(1);
      });

      test('should stop voice input', () async {
        // Arrange
        provider.isVoiceInputActive = true;

        // Act
        await provider.stopVoiceInput();

        // Assert
        expect(provider.isVoiceInputActive, isFalse);
        verify(mockVoiceInputService.stopListening()).called(1);
      });

      test('should cancel voice input', () async {
        // Arrange
        provider.isVoiceInputActive = true;
        provider.voiceInputText = 'Some text';

        // Act
        await provider.cancelVoiceInput();

        // Assert
        expect(provider.isVoiceInputActive, isFalse);
        expect(provider.voiceInputText, isEmpty);
        verify(mockVoiceInputService.cancelListening()).called(1);
      });
    });

    group('Form Management', () {
      test('should set selected severity', () {
        // Act
        provider.setSelectedSeverity(HazardSeverity.critical);

        // Assert
        expect(provider.selectedSeverity, equals(HazardSeverity.critical));
      });

      test('should set selected category', () {
        // Act
        provider.setSelectedCategory(HazardCategory.electrical);

        // Assert
        expect(provider.selectedCategory, equals(HazardCategory.electrical));
      });

      test('should clear form', () {
        // Arrange
        provider.setSelectedSeverity(HazardSeverity.critical);
        provider.setSelectedCategory(HazardCategory.electrical);
        provider.selectedPhotos.add('/photo.jpg');
        provider.setSelectedLocation(const GeoPoint(latitude: 1.0, longitude: 1.0));

        // Act
        provider.clearForm();

        // Assert
        expect(provider.selectedSeverity, equals(HazardSeverity.medium));
        expect(provider.selectedCategory, equals(HazardCategory.other));
        expect(provider.selectedPhotos, isEmpty);
        expect(provider.selectedLocation, isNull);
        expect(provider.voiceInputText, isEmpty);
        expect(provider.isVoiceInputActive, isFalse);
        expect(provider.currentHazard, isNull);
        expect(provider.error, isNull);
      });
    });

    group('Statistics', () {
      test('should calculate hazard statistics correctly', () {
        // Arrange
        const walkaboutId = 'walkabout_123';
        final hazards = [
          Hazard(
            id: 'hazard_1',
            walkaboutId: walkaboutId,
            title: 'Hazard 1',
            severity: HazardSeverity.high,
            category: HazardCategory.slipTripFall,
            photos: [],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            syncStatus: SyncStatus.local,
          ),
          Hazard(
            id: 'hazard_2',
            walkaboutId: walkaboutId,
            title: 'Hazard 2',
            severity: HazardSeverity.high,
            category: HazardCategory.electrical,
            photos: [],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            syncStatus: SyncStatus.local,
          ),
          Hazard(
            id: 'hazard_3',
            walkaboutId: walkaboutId,
            title: 'Hazard 3',
            severity: HazardSeverity.low,
            category: HazardCategory.slipTripFall,
            photos: [],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            syncStatus: SyncStatus.local,
          ),
        ];

        provider.hazards.addAll(hazards);

        // Act
        final statistics = provider.getHazardStatistics(walkaboutId);

        // Assert
        expect(statistics['total'], equals(3));
        expect(statistics['bySeverity'][HazardSeverity.high], equals(2));
        expect(statistics['bySeverity'][HazardSeverity.low], equals(1));
        expect(statistics['byCategory'][HazardCategory.slipTripFall], equals(2));
        expect(statistics['byCategory'][HazardCategory.electrical], equals(1));
      });
    });
  });
}
