import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import '../../models/user_model.dart';
import '../../../domain/entities/user.dart' as domain;

/// Interface for remote user data operations
abstract class UserRemoteDataSource {
  /// Get user by ID from remote source
  Future<domain.User?> getUserById(String uid);

  /// Update user in remote source
  Future<domain.User> updateUser(domain.User user);
}

/// Implementation of UserRemoteDataSource using Firebase
class UserRemoteDataSourceImpl implements UserRemoteDataSource {
  final FirebaseFirestore _firestore;
  final firebase_auth.FirebaseAuth _firebaseAuth;

  UserRemoteDataSourceImpl({
    FirebaseFirestore? firestore,
    firebase_auth.FirebaseAuth? firebaseAuth,
  }) : _firestore = firestore ?? FirebaseFirestore.instance,
       _firebaseAuth = firebaseAuth ?? firebase_auth.FirebaseAuth.instance;

  /// Collection reference for users
  CollectionReference get _usersCollection => _firestore.collection('users');

  @override
  Future<domain.User?> getUserById(String uid) async {
    try {
      // Get user document from Firestore
      final docSnapshot = await _usersCollection.doc(uid).get();
      
      if (!docSnapshot.exists) {
        // If user document doesn't exist, try to get from Firebase Auth
        final authUser = _firebaseAuth.currentUser;
        
        if (authUser != null && authUser.uid == uid) {
          // Create a basic user model from auth user
          final userModel = UserModel(
            uid: authUser.uid,
            id: authUser.uid,
            email: authUser.email ?? '',
            displayName: authUser.displayName ?? '',
            photoUrl: authUser.photoURL,
            organization: '',
            role: domain.UserRole.inspector,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            lastLoginAt: DateTime.now(),
          );
          
          // Save the user to Firestore
          await _usersCollection.doc(uid).set(userModel.toFirestore());
          
          return userModel;
        }
        
        return null;
      }
      
      // Convert document data to UserModel
      return UserModelFirestore.fromFirestore(docSnapshot);
    } catch (e) {
      throw Exception('Failed to get user from remote: ${e.toString()}');
    }
  }

  @override
  Future<domain.User> updateUser(domain.User user) async {
    try {
      // Convert domain User to UserModel
      final userModel = user is UserModel 
          ? user 
          : UserModel.fromEntity(user);
      
      // Update user document in Firestore with updated timestamp
      final updatedModel = userModel.copyWith(
        updatedAt: DateTime.now(),
      );
      
      // Update user document in Firestore
      await _usersCollection.doc(user.uid).update(updatedModel.toFirestore());
      
      // Get updated user
      final updatedUser = await getUserById(user.uid);
      
      if (updatedUser == null) {
        throw Exception('Failed to get updated user');
      }
      
      return updatedUser;
    } catch (e) {
      throw Exception('Failed to update user in remote: ${e.toString()}');
    }
  }
}