import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:safestride/data/datasources/firebase_auth_datasource.dart';
import 'package:safestride/data/models/user_model.dart';
import 'package:safestride/core/error/failures.dart';
import 'dart:async';

import 'firebase_auth_datasource_test.mocks.dart';

@GenerateMocks([
  FirebaseAuth,
  User,
  UserCredential,
  AuthCredential,
])
void main() {
  late FirebaseAuthDataSource dataSource;
  late MockFirebaseAuth mockFirebaseAuth;
  late MockUser mockUser;
  late MockUserCredential mockUserCredential;
  late StreamController<User?> authStreamController;

  setUp(() {
    mockFirebaseAuth = MockFirebaseAuth();
    mockUser = MockUser();
    mockUserCredential = MockUserCredential();
    authStreamController = StreamController<User?>.broadcast();
    dataSource = FirebaseAuthDataSource(mockFirebaseAuth);
  });

  tearDown(() {
    authStreamController.close();
  });

  const testEmail = '<EMAIL>';
  const testPassword = 'password123';
  const testDisplayName = 'Test User';
  const testUid = 'test-uid';

  group('FirebaseAuthDataSource', () {
    group('registerWithEmail', () {
      test('should register user successfully', () async {
        // Arrange
        when(mockFirebaseAuth.createUserWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).thenAnswer((_) async => mockUserCredential);
        
        when(mockUserCredential.user).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(testUid);
        when(mockUser.email).thenReturn(testEmail);
        when(mockUser.displayName).thenReturn(testDisplayName);
        when(mockUser.metadata).thenReturn(MockUserMetadata());
        
        when(mockUser.updateDisplayName(testDisplayName))
            .thenAnswer((_) async {});

        // Act
        final result = await dataSource.registerWithEmail(
          email: testEmail,
          password: testPassword,
          displayName: testDisplayName,
        );

        // Assert
        expect(result, isA<UserModel>());
        expect(result.email, testEmail);
        expect(result.uid, testUid);
        verify(mockFirebaseAuth.createUserWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).called(1);
        verify(mockUser.updateDisplayName(testDisplayName)).called(1);
      });

      test('should throw AuthFailure when email already in use', () async {
        // Arrange
        when(mockFirebaseAuth.createUserWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).thenThrow(FirebaseAuthException(
          code: 'email-already-in-use',
          message: 'Email already in use',
        ));

        // Act & Assert
        expect(
          () => dataSource.registerWithEmail(
            email: testEmail,
            password: testPassword,
            displayName: testDisplayName,
          ),
          throwsA(isA<AuthFailure>()),
        );
      });

      test('should throw AuthFailure when password is weak', () async {
        // Arrange
        when(mockFirebaseAuth.createUserWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).thenThrow(FirebaseAuthException(
          code: 'weak-password',
          message: 'Password is too weak',
        ));

        // Act & Assert
        expect(
          () => dataSource.registerWithEmail(
            email: testEmail,
            password: testPassword,
            displayName: testDisplayName,
          ),
          throwsA(isA<AuthFailure>()),
        );
      });

      test('should throw NetworkFailure when network error occurs', () async {
        // Arrange
        when(mockFirebaseAuth.createUserWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).thenThrow(FirebaseAuthException(
          code: 'network-request-failed',
          message: 'Network error',
        ));

        // Act & Assert
        expect(
          () => dataSource.registerWithEmail(
            email: testEmail,
            password: testPassword,
            displayName: testDisplayName,
          ),
          throwsA(isA<NetworkFailure>()),
        );
      });
    });

    group('loginWithEmail', () {
      test('should login user successfully', () async {
        // Arrange
        when(mockFirebaseAuth.signInWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).thenAnswer((_) async => mockUserCredential);
        
        when(mockUserCredential.user).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(testUid);
        when(mockUser.email).thenReturn(testEmail);
        when(mockUser.displayName).thenReturn(testDisplayName);
        when(mockUser.metadata).thenReturn(MockUserMetadata());

        // Act
        final result = await dataSource.loginWithEmail(
          email: testEmail,
          password: testPassword,
        );

        // Assert
        expect(result, isA<UserModel>());
        expect(result.email, testEmail);
        expect(result.uid, testUid);
        verify(mockFirebaseAuth.signInWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).called(1);
      });

      test('should throw AuthFailure when user not found', () async {
        // Arrange
        when(mockFirebaseAuth.signInWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).thenThrow(FirebaseAuthException(
          code: 'user-not-found',
          message: 'User not found',
        ));

        // Act & Assert
        expect(
          () => dataSource.loginWithEmail(
            email: testEmail,
            password: testPassword,
          ),
          throwsA(isA<AuthFailure>()),
        );
      });

      test('should throw AuthFailure when wrong password', () async {
        // Arrange
        when(mockFirebaseAuth.signInWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).thenThrow(FirebaseAuthException(
          code: 'wrong-password',
          message: 'Wrong password',
        ));

        // Act & Assert
        expect(
          () => dataSource.loginWithEmail(
            email: testEmail,
            password: testPassword,
          ),
          throwsA(isA<AuthFailure>()),
        );
      });

      test('should throw AuthFailure when too many requests', () async {
        // Arrange
        when(mockFirebaseAuth.signInWithEmailAndPassword(
          email: testEmail,
          password: testPassword,
        )).thenThrow(FirebaseAuthException(
          code: 'too-many-requests',
          message: 'Too many requests',
        ));

        // Act & Assert
        expect(
          () => dataSource.loginWithEmail(
            email: testEmail,
            password: testPassword,
          ),
          throwsA(isA<AuthFailure>()),
        );
      });
    });

    group('logout', () {
      test('should logout user successfully', () async {
        // Arrange
        when(mockFirebaseAuth.signOut()).thenAnswer((_) async {});

        // Act
        await dataSource.logout();

        // Assert
        verify(mockFirebaseAuth.signOut()).called(1);
      });

      test('should throw AuthFailure when logout fails', () async {
        // Arrange
        when(mockFirebaseAuth.signOut())
            .thenThrow(FirebaseAuthException(
          code: 'unknown',
          message: 'Logout failed',
        ));

        // Act & Assert
        expect(
          () => dataSource.logout(),
          throwsA(isA<AuthFailure>()),
        );
      });
    });

    group('resetPassword', () {
      test('should send password reset email successfully', () async {
        // Arrange
        when(mockFirebaseAuth.sendPasswordResetEmail(email: testEmail))
            .thenAnswer((_) async {});

        // Act
        await dataSource.resetPassword(email: testEmail);

        // Assert
        verify(mockFirebaseAuth.sendPasswordResetEmail(email: testEmail))
            .called(1);
      });

      test('should throw AuthFailure when user not found', () async {
        // Arrange
        when(mockFirebaseAuth.sendPasswordResetEmail(email: testEmail))
            .thenThrow(FirebaseAuthException(
          code: 'user-not-found',
          message: 'User not found',
        ));

        // Act & Assert
        expect(
          () => dataSource.resetPassword(email: testEmail),
          throwsA(isA<AuthFailure>()),
        );
      });

      test('should throw AuthFailure when too many requests', () async {
        // Arrange
        when(mockFirebaseAuth.sendPasswordResetEmail(email: testEmail))
            .thenThrow(FirebaseAuthException(
          code: 'too-many-requests',
          message: 'Too many requests',
        ));

        // Act & Assert
        expect(
          () => dataSource.resetPassword(email: testEmail),
          throwsA(isA<AuthFailure>()),
        );
      });
    });

    group('getCurrentUser', () {
      test('should return current user when logged in', () async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(mockUser);
        when(mockUser.uid).thenReturn(testUid);
        when(mockUser.email).thenReturn(testEmail);
        when(mockUser.displayName).thenReturn(testDisplayName);
        when(mockUser.metadata).thenReturn(MockUserMetadata());

        // Act
        final result = await dataSource.getCurrentUser();

        // Assert
        expect(result, isA<UserModel>());
        expect(result?.uid, testUid);
        expect(result?.email, testEmail);
      });

      test('should return null when no user logged in', () async {
        // Arrange
        when(mockFirebaseAuth.currentUser).thenReturn(null);

        // Act
        final result = await dataSource.getCurrentUser();

        // Assert
        expect(result, isNull);
      });
    });

    group('authStateStream', () {
      test('should provide auth state stream', () {
        // Arrange
        when(mockFirebaseAuth.authStateChanges())
            .thenAnswer((_) => authStreamController.stream);

        // Act
        final stream = dataSource.authStateStream;

        // Assert
        expect(stream, authStreamController.stream);
        verify(mockFirebaseAuth.authStateChanges()).called(1);
      });

      test('should emit user changes through auth state stream', () async {
        // Arrange
        when(mockFirebaseAuth.authStateChanges())
            .thenAnswer((_) => authStreamController.stream);
        when(mockUser.uid).thenReturn(testUid);
        when(mockUser.email).thenReturn(testEmail);
        when(mockUser.displayName).thenReturn(testDisplayName);
        when(mockUser.metadata).thenReturn(MockUserMetadata());

        final stream = dataSource.authStateStream;
        final streamValues = <UserModel?>[];
        
        // Listen to stream
        final subscription = stream.listen(streamValues.add);

        // Act
        authStreamController.add(mockUser);
        authStreamController.add(null);

        // Wait for stream to process
        await Future.delayed(Duration.zero);

        // Assert
        expect(streamValues, hasLength(2));
        expect(streamValues[0]?.uid, testUid);
        expect(streamValues[1], isNull);
        
        // Cleanup
        await subscription.cancel();
      });
    });
  });
}

class MockUserMetadata extends Mock implements UserMetadata {
  @override
  DateTime? get creationTime => DateTime.now();
  
  @override
  DateTime? get lastSignInTime => DateTime.now();
}