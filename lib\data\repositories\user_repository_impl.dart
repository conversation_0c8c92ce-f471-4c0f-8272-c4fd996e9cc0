import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/user_repository.dart';
import '../datasources/remote/user_remote_datasource.dart';
import '../datasources/local/user_local_datasource.dart';

/// Implementation of the UserRepository interface
class UserRepositoryImpl implements UserRepository {
  final UserRemoteDataSource remoteDataSource;
  final UserLocalDataSource localDataSource;

  const UserRepositoryImpl({
    required this.remoteDataSource,
    required this.localDataSource,
  });

  @override
  Future<User?> getUserById(String uid) async {
    try {
      // Try to get user from remote source
      final user = await remoteDataSource.getUserById(uid);
      
      // Cache user data locally for offline access
      if (user != null) {
        await localDataSource.cacheUser(user);
      }
      
      return user;
    } catch (e) {
      // If remote fails, try to get from local cache
      try {
        return await localDataSource.getCachedUser(uid);
      } catch (cacheError) {
        throw Exception('Failed to get user: ${e.toString()}');
      }
    }
  }

  @override
  Future<User> updateUser(User user) async {
    try {
      // Update user in remote source
      final updatedUser = await remoteDataSource.updateUser(user);
      
      // Update local cache
      await localDataSource.cacheUser(updatedUser);
      
      return updatedUser;
    } catch (e) {
      // If remote update fails, update local cache and mark for sync
      try {
        await localDataSource.cacheUser(user);
        await localDataSource.markForSync(user.uid);
        return user;
      } catch (cacheError) {
        throw Exception('Failed to update user: ${e.toString()}');
      }
    }
  }

  @override
  Future<User> updateUserPreferences(String uid, UserPreferences preferences) async {
    try {
      // Get current user
      final currentUser = await getUserById(uid);
      
      if (currentUser == null) {
        throw Exception('User not found');
      }
      
      // Create updated user with new preferences
      final updatedUser = currentUser.copyWith(preferences: preferences);
      
      // Update user
      return await updateUser(updatedUser);
    } catch (e) {
      throw Exception('Failed to update user preferences: ${e.toString()}');
    }
  }

  @override
  Future<User> updateUserRole(String uid, UserRole role) async {
    try {
      // Get current user
      final currentUser = await getUserById(uid);
      
      if (currentUser == null) {
        throw Exception('User not found');
      }
      
      // Create updated user with new role
      final updatedUser = currentUser.copyWith(role: role);
      
      // Update user
      return await updateUser(updatedUser);
    } catch (e) {
      throw Exception('Failed to update user role: ${e.toString()}');
    }
  }

  @override
  Future<User> syncUser(String uid) async {
    try {
      // Check if user needs syncing
      final needsSync = await localDataSource.needsSync(uid);
      
      if (!needsSync) {
        // If no sync needed, just get the user
        final user = await getUserById(uid);
        if (user == null) {
          throw Exception('User not found');
        }
        return user;
      }
      
      // Get cached user
      final cachedUser = await localDataSource.getCachedUser(uid);
      
      if (cachedUser == null) {
        throw Exception('Cached user not found');
      }
      
      // Update remote with cached data
      final syncedUser = await remoteDataSource.updateUser(cachedUser);
      
      // Update local cache with synced data
      await localDataSource.cacheUser(syncedUser);
      
      // Clear sync flag
      await localDataSource.clearSyncFlag(uid);
      
      return syncedUser;
    } catch (e) {
      throw Exception('Failed to sync user: ${e.toString()}');
    }
  }

  @override
  Future<User?> getCachedUser(String uid) async {
    try {
      return await localDataSource.getCachedUser(uid);
    } catch (e) {
      throw Exception('Failed to get cached user: ${e.toString()}');
    }
  }

  @override
  Future<void> cacheUser(User user) async {
    try {
      await localDataSource.cacheUser(user);
    } catch (e) {
      throw Exception('Failed to cache user: ${e.toString()}');
    }
  }
}