import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/repositories/auth_repository.dart';
import 'package:safestride/domain/usecases/logout.dart';

import 'logout_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  late LogoutUseCase useCase;
  late MockAuthRepository mockAuthRepository;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    useCase = LogoutUseCase(mockAuthRepository);
  });

  group('LogoutUseCase', () {
    test('should logout user successfully', () async {
      // Arrange
      when(mockAuthRepository.logout())
          .thenAnswer((_) async => {});

      // Act
      await useCase.call();

      // Assert
      verify(mockAuthRepository.logout()).called(1);
    });

    test('should clear cached credentials on logout', () async {
      // Arrange
      when(mockAuthRepository.logout())
          .thenAnswer((_) async => {});
      when(mockAuthRepository.clearCachedCredentials())
          .thenAnswer((_) async => {});

      // Act
      await useCase.call();

      // Assert
      verify(mockAuthRepository.logout()).called(1);
      verify(mockAuthRepository.clearCachedCredentials()).called(1);
    });

    test('should throw exception when logout fails', () async {
      // Arrange
      const errorMessage = 'Logout failed';
      when(mockAuthRepository.logout())
          .thenThrow(Exception(errorMessage));

      // Act & Assert
      expect(
        () => useCase.call(),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains(errorMessage),
        )),
      );
      verify(mockAuthRepository.logout()).called(1);
    });

    test('should still clear cached credentials even if remote logout fails', () async {
      // Arrange
      when(mockAuthRepository.logout())
          .thenThrow(Exception('Network error'));
      when(mockAuthRepository.clearCachedCredentials())
          .thenAnswer((_) async => {});

      // Act
      try {
        await useCase.call();
      } catch (e) {
        // Expected to throw
      }

      // Assert
      verify(mockAuthRepository.logout()).called(1);
      verify(mockAuthRepository.clearCachedCredentials()).called(1);
    });

    test('should handle cache clearing failure gracefully', () async {
      // Arrange
      when(mockAuthRepository.logout())
          .thenAnswer((_) async => {});
      when(mockAuthRepository.clearCachedCredentials())
          .thenThrow(Exception('Cache error'));

      // Act & Assert
      // Should not throw exception even if cache clearing fails
      await useCase.call();
      
      verify(mockAuthRepository.logout()).called(1);
      verify(mockAuthRepository.clearCachedCredentials()).called(1);
    });
  });
}