/// Base class for all failures in the application
abstract class Failure {
  final String message;
  final String? code;
  
  const Failure(this.message, {this.code});
  
  @override
  String toString() => 'Failure: $message${code != null ? ' (Code: $code)' : ''}';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Failure && other.message == message && other.code == code;
  }
  
  @override
  int get hashCode => message.hashCode ^ code.hashCode;
}

/// Authentication related failures
class AuthFailure extends Failure {
  const AuthFailure(String message, {String? code}) : super(message, code: code);
  
  factory AuthFailure.invalidCredentials() => 
      const AuthFailure('Invalid email or password', code: 'invalid-credentials');
  
  factory AuthFailure.userNotFound() => 
      const AuthFailure('User not found', code: 'user-not-found');
  
  factory AuthFailure.emailAlreadyInUse() => 
      const AuthFailure('Email is already in use', code: 'email-already-in-use');
  
  factory AuthFailure.weakPassword() => 
      const AuthFailure('Password is too weak', code: 'weak-password');
  
  factory AuthFailure.userDisabled() => 
      const AuthFailure('User account has been disabled', code: 'user-disabled');
  
  factory AuthFailure.tooManyRequests() => 
      const AuthFailure('Too many requests. Please try again later', code: 'too-many-requests');
  
  factory AuthFailure.operationNotAllowed() => 
      const AuthFailure('Operation not allowed', code: 'operation-not-allowed');
  
  factory AuthFailure.networkError() => 
      const AuthFailure('Network error. Please check your connection', code: 'network-error');
  
  factory AuthFailure.unknown(String message) => 
      AuthFailure('An unknown error occurred: $message', code: 'unknown');
}

/// Validation related failures
class ValidationFailure extends Failure {
  const ValidationFailure(String message, {String? code}) : super(message, code: code);
  
  factory ValidationFailure.invalidEmail() => 
      const ValidationFailure('Please enter a valid email address', code: 'invalid-email');
  
  factory ValidationFailure.passwordTooShort() => 
      const ValidationFailure('Password must be at least 6 characters long', code: 'password-too-short');
  
  factory ValidationFailure.passwordTooWeak() => 
      const ValidationFailure('Password must contain at least one uppercase letter, one lowercase letter, and one number', code: 'password-too-weak');
  
  factory ValidationFailure.emptyField(String fieldName) => 
      ValidationFailure('$fieldName cannot be empty', code: 'empty-field');
  
  factory ValidationFailure.invalidFormat(String fieldName) => 
      ValidationFailure('$fieldName has invalid format', code: 'invalid-format');
}

/// Network related failures
class NetworkFailure extends Failure {
  const NetworkFailure(String message, {String? code}) : super(message, code: code);
  
  factory NetworkFailure.noConnection() => 
      const NetworkFailure('No internet connection', code: 'no-connection');
  
  factory NetworkFailure.timeout() => 
      const NetworkFailure('Request timeout', code: 'timeout');
  
  factory NetworkFailure.serverError() => 
      const NetworkFailure('Server error. Please try again later', code: 'server-error');
  
  factory NetworkFailure.badRequest() => 
      const NetworkFailure('Bad request', code: 'bad-request');
  
  factory NetworkFailure.unauthorized() => 
      const NetworkFailure('Unauthorized access', code: 'unauthorized');
  
  factory NetworkFailure.forbidden() => 
      const NetworkFailure('Access forbidden', code: 'forbidden');
  
  factory NetworkFailure.notFound() => 
      const NetworkFailure('Resource not found', code: 'not-found');
}

/// Cache related failures
class CacheFailure extends Failure {
  const CacheFailure(String message, {String? code}) : super(message, code: code);
  
  factory CacheFailure.notFound() => 
      const CacheFailure('Data not found in cache', code: 'cache-not-found');
  
  factory CacheFailure.writeError() => 
      const CacheFailure('Failed to write to cache', code: 'cache-write-error');
  
  factory CacheFailure.readError() => 
      const CacheFailure('Failed to read from cache', code: 'cache-read-error');
  
  factory CacheFailure.corruptedData() => 
      const CacheFailure('Cached data is corrupted', code: 'cache-corrupted');
}

/// Storage related failures
class StorageFailure extends Failure {
  const StorageFailure(String message, {String? code}) : super(message, code: code);
  
  factory StorageFailure.uploadFailed() => 
      const StorageFailure('Failed to upload file', code: 'upload-failed');
  
  factory StorageFailure.downloadFailed() => 
      const StorageFailure('Failed to download file', code: 'download-failed');
  
  factory StorageFailure.fileNotFound() => 
      const StorageFailure('File not found', code: 'file-not-found');
  
  factory StorageFailure.insufficientStorage() => 
      const StorageFailure('Insufficient storage space', code: 'insufficient-storage');
  
  factory StorageFailure.permissionDenied() => 
      const StorageFailure('Storage permission denied', code: 'permission-denied');
}