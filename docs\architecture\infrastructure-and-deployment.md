# Infrastructure and Deployment

## Infrastructure as Code

- **Tool:** Firebase CLI and configuration files
- **Location:** `firebase/` directory
- **Approach:** Configuration-based deployment with Firebase hosting

## Deployment Strategy

- **Strategy:** Automated CI/CD with GitHub Actions
- **CI/CD Platform:** GitHub Actions
- **Pipeline Configuration:** `.github/workflows/main.yaml`

## Environments

- **Development:** Local development with Firebase emulators
- **Staging:** Firebase staging project for testing
- **Production:** Firebase production project for live app

## Environment Promotion Flow

```text
Development (Local) → Staging (Firebase Staging) → Production (Firebase Production)
```

## Rollback Strategy

- **Primary Method:** Firebase project rollback and app store version rollback
- **Trigger Conditions:** Critical bugs, data corruption, security issues
- **Recovery Time Objective:** 2 hours for critical issues
