/// Application configuration constants
class AppConfig {
  // Private constructor to prevent instantiation
  AppConfig._();

  // App information
  static const String appName = 'SafeStride';
  static const String appVersion = '1.0.0';
  static const String appBuildNumber = '1';
  static const String appDescription = 'Safety inspection and compliance management application';
  static const String appPackageName = 'com.melur.safestride';

  // Environment configuration
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );

  static bool get isDevelopment => environment == 'development';
  static bool get isStaging => environment == 'staging';
  static bool get isProduction => environment == 'production';

  // API configuration
  static const String baseUrl = String.fromEnvironment(
    'BASE_URL',
    defaultValue: 'https://api.safestride.com',
  );

  static const String apiVersion = 'v1';
  static const String apiPath = '/api/$apiVersion';
  static String get apiBaseUrl => '$baseUrl$apiPath';

  // Firebase configuration
  static const String firebaseProjectId = String.fromEnvironment(
    'FIREBASE_PROJECT_ID',
    defaultValue: 'safestride-dev',
  );

  static const String firebaseApiKey = String.fromEnvironment(
    'FIREBASE_API_KEY',
    defaultValue: '',
  );

  static const String firebaseAppId = String.fromEnvironment(
    'FIREBASE_APP_ID',
    defaultValue: '',
  );

  static const String firebaseMessagingSenderId = String.fromEnvironment(
    'FIREBASE_MESSAGING_SENDER_ID',
    defaultValue: '',
  );

  static const String firebaseStorageBucket = String.fromEnvironment(
    'FIREBASE_STORAGE_BUCKET',
    defaultValue: '',
  );

  // Google Services configuration
  static const String googleSignInClientId = String.fromEnvironment(
    'GOOGLE_SIGN_IN_CLIENT_ID',
    defaultValue: '',
  );

  static const String googleMapsApiKey = String.fromEnvironment(
    'GOOGLE_MAPS_API_KEY',
    defaultValue: '',
  );

  // Database configuration
  static const String databaseName = 'safestride.db';
  static const int databaseVersion = 1;

  // Cache configuration
  static const Duration cacheExpiration = Duration(hours: 24);
  static const Duration shortCacheExpiration = Duration(minutes: 15);
  static const Duration longCacheExpiration = Duration(days: 7);
  static const int maxCacheSize = 100 * 1024 * 1024; // 100MB

  // Network configuration
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  static const int maxRetries = 3;
  static const Duration retryDelay = Duration(seconds: 2);

  // Authentication configuration
  static const Duration tokenRefreshThreshold = Duration(minutes: 5);
  static const Duration sessionTimeout = Duration(hours: 24);
  static const int maxLoginAttempts = 5;
  static const Duration loginCooldown = Duration(minutes: 15);

  // File upload configuration
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const int maxImageSize = 5 * 1024 * 1024; // 5MB
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ];
  static const List<String> allowedDocumentTypes = [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  ];

  // Location configuration
  static const double locationAccuracy = 10.0; // meters
  static const Duration locationTimeout = Duration(seconds: 30);
  static const Duration locationUpdateInterval = Duration(seconds: 10);

  // Notification configuration
  static const String notificationChannelId = 'safestride_notifications';
  static const String notificationChannelName = 'SafeStride Notifications';
  static const String notificationChannelDescription = 'Notifications for SafeStride app';

  // Pagination configuration
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  static const int minPageSize = 5;

  // Validation configuration
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;
  static const int maxDescriptionLength = 500;
  static const int maxCommentLength = 1000;

  // UI configuration
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration shortAnimationDuration = Duration(milliseconds: 150);
  static const Duration longAnimationDuration = Duration(milliseconds: 500);
  static const Duration splashScreenDuration = Duration(seconds: 3);
  static const Duration snackBarDuration = Duration(seconds: 4);
  static const Duration toastDuration = Duration(seconds: 2);

  // Feature flags
  static const bool enableAnalytics = bool.fromEnvironment(
    'ENABLE_ANALYTICS',
    defaultValue: true,
  );

  static const bool enableCrashReporting = bool.fromEnvironment(
    'ENABLE_CRASH_REPORTING',
    defaultValue: true,
  );

  static const bool enablePerformanceMonitoring = bool.fromEnvironment(
    'ENABLE_PERFORMANCE_MONITORING',
    defaultValue: true,
  );

  static const bool enableOfflineMode = bool.fromEnvironment(
    'ENABLE_OFFLINE_MODE',
    defaultValue: true,
  );

  static const bool enableDarkMode = bool.fromEnvironment(
    'ENABLE_DARK_MODE',
    defaultValue: true,
  );

  static const bool enableBiometricAuth = bool.fromEnvironment(
    'ENABLE_BIOMETRIC_AUTH',
    defaultValue: true,
  );

  static const bool enablePushNotifications = bool.fromEnvironment(
    'ENABLE_PUSH_NOTIFICATIONS',
    defaultValue: true,
  );

  static const bool enableLocationServices = bool.fromEnvironment(
    'ENABLE_LOCATION_SERVICES',
    defaultValue: true,
  );

  static const bool enableCameraFeatures = bool.fromEnvironment(
    'ENABLE_CAMERA_FEATURES',
    defaultValue: true,
  );

  static const bool enableFileUpload = bool.fromEnvironment(
    'ENABLE_FILE_UPLOAD',
    defaultValue: true,
  );

  // Debug configuration
  static const bool enableDebugMode = bool.fromEnvironment(
    'ENABLE_DEBUG_MODE',
    defaultValue: false,
  );

  static const bool enableLogging = bool.fromEnvironment(
    'ENABLE_LOGGING',
    defaultValue: true,
  );

  static const bool enableNetworkLogging = bool.fromEnvironment(
    'ENABLE_NETWORK_LOGGING',
    defaultValue: false,
  );

  static const bool enablePerformanceLogging = bool.fromEnvironment(
    'ENABLE_PERFORMANCE_LOGGING',
    defaultValue: false,
  );

  // Security configuration
  static const bool enableCertificatePinning = bool.fromEnvironment(
    'ENABLE_CERTIFICATE_PINNING',
    defaultValue: true,
  );

  static const bool enableRootDetection = bool.fromEnvironment(
    'ENABLE_ROOT_DETECTION',
    defaultValue: true,
  );

  static const bool enableScreenshotPrevention = bool.fromEnvironment(
    'ENABLE_SCREENSHOT_PREVENTION',
    defaultValue: false,
  );

  static const bool enableAppLockOnBackground = bool.fromEnvironment(
    'ENABLE_APP_LOCK_ON_BACKGROUND',
    defaultValue: true,
  );

  // Compliance configuration
  static const bool enableGDPRCompliance = bool.fromEnvironment(
    'ENABLE_GDPR_COMPLIANCE',
    defaultValue: true,
  );

  static const bool enableCCPACompliance = bool.fromEnvironment(
    'ENABLE_CCPA_COMPLIANCE',
    defaultValue: true,
  );

  static const bool enableDataEncryption = bool.fromEnvironment(
    'ENABLE_DATA_ENCRYPTION',
    defaultValue: true,
  );

  // Support configuration
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '******-SAFESTRIDE';
  static const String supportWebsite = 'https://support.safestride.com';
  static const String privacyPolicyUrl = 'https://safestride.com/privacy';
  static const String termsOfServiceUrl = 'https://safestride.com/terms';
  static const String helpCenterUrl = 'https://help.safestride.com';

  // Social media configuration
  static const String facebookUrl = 'https://facebook.com/safestride';
  static const String twitterUrl = 'https://twitter.com/safestride';
  static const String linkedinUrl = 'https://linkedin.com/company/safestride';
  static const String youtubeUrl = 'https://youtube.com/safestride';

  // App store configuration
  static const String appStoreUrl = 'https://apps.apple.com/app/safestride';
  static const String playStoreUrl = 'https://play.google.com/store/apps/details?id=com.safestride.app';

  /// Get configuration summary for debugging
  static Map<String, dynamic> getConfigSummary() {
    return {
      'appName': appName,
      'appVersion': appVersion,
      'environment': environment,
      'isDevelopment': isDevelopment,
      'isStaging': isStaging,
      'isProduction': isProduction,
      'baseUrl': baseUrl,
      'apiBaseUrl': apiBaseUrl,
      'enableAnalytics': enableAnalytics,
      'enableCrashReporting': enableCrashReporting,
      'enableOfflineMode': enableOfflineMode,
      'enableDebugMode': enableDebugMode,
      'enableLogging': enableLogging,
    };
  }

  /// Validate configuration
  static bool validateConfig() {
    // Check required configuration
    if (firebaseProjectId.isEmpty) {
      return false;
    }

    if (isProduction) {
      // Additional validation for production
      if (firebaseApiKey.isEmpty ||
          firebaseAppId.isEmpty ||
          googleSignInClientId.isEmpty) {
        return false;
      }
    }

    return true;
  }
}