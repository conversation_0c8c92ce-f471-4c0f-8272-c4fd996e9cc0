import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/data/repositories/walkabout_repository_impl.dart';
import 'package:safestride/data/datasources/local/walkabout_local_datasource.dart';

import 'walkabout_repository_test.mocks.dart';

@GenerateMocks([WalkaboutLocalDataSource])
void main() {
  late WalkaboutRepositoryImpl repository;
  late MockWalkaboutLocalDataSource mockLocalDataSource;

  setUp(() {
    mockLocalDataSource = MockWalkaboutLocalDataSource();
    repository = WalkaboutRepositoryImpl(localDataSource: mockLocalDataSource);
  });

  group('WalkaboutRepositoryImpl', () {
    final testWalkabout = Walkabout(
      id: 'test-id',
      title: 'Test Walkabout',
      description: 'Test description',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      status: WalkaboutStatus.draft,
      location: GeoPoint(latitude: 37.7749, longitude: -122.4194),
      userId: 'test-user-id',
      isCompleted: false,
      syncStatus: SyncStatus.local,
    );

    group('createWalkabout', () {
      test('should create walkabout successfully', () async {
        // Arrange
        when(mockLocalDataSource.createWalkabout(any))
            .thenAnswer((_) async => testWalkabout);

        // Act
        final result = await repository.createWalkabout(testWalkabout);

        // Assert
        expect(result, equals(testWalkabout));
        verify(mockLocalDataSource.createWalkabout(testWalkabout)).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(mockLocalDataSource.createWalkabout(any))
            .thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.createWalkabout(testWalkabout),
          throwsA(isA<Exception>().having(
            (e) => e.toString(),
            'message',
            contains('Failed to create walkabout'),
          )),
        );
      });
    });

    group('getWalkaboutById', () {
      test('should return walkabout when found', () async {
        // Arrange
        when(mockLocalDataSource.getWalkaboutById('test-id'))
            .thenAnswer((_) async => testWalkabout);

        // Act
        final result = await repository.getWalkaboutById('test-id');

        // Assert
        expect(result, equals(testWalkabout));
        verify(mockLocalDataSource.getWalkaboutById('test-id')).called(1);
      });

      test('should return null when not found', () async {
        // Arrange
        when(mockLocalDataSource.getWalkaboutById('non-existent'))
            .thenAnswer((_) async => null);

        // Act
        final result = await repository.getWalkaboutById('non-existent');

        // Assert
        expect(result, isNull);
        verify(mockLocalDataSource.getWalkaboutById('non-existent')).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(mockLocalDataSource.getWalkaboutById(any))
            .thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getWalkaboutById('test-id'),
          throwsA(isA<Exception>().having(
            (e) => e.toString(),
            'message',
            contains('Failed to get walkabout by ID'),
          )),
        );
      });
    });

    group('getWalkaboutsByUserId', () {
      test('should return list of walkabouts', () async {
        // Arrange
        final walkabouts = [testWalkabout];
        when(mockLocalDataSource.getWalkaboutsByUserId('test-user-id'))
            .thenAnswer((_) async => walkabouts);

        // Act
        final result = await repository.getWalkaboutsByUserId('test-user-id');

        // Assert
        expect(result, equals(walkabouts));
        verify(mockLocalDataSource.getWalkaboutsByUserId('test-user-id')).called(1);
      });

      test('should return empty list when no walkabouts found', () async {
        // Arrange
        when(mockLocalDataSource.getWalkaboutsByUserId('test-user-id'))
            .thenAnswer((_) async => []);

        // Act
        final result = await repository.getWalkaboutsByUserId('test-user-id');

        // Assert
        expect(result, isEmpty);
        verify(mockLocalDataSource.getWalkaboutsByUserId('test-user-id')).called(1);
      });
    });

    group('updateWalkabout', () {
      test('should update walkabout and set sync status to local', () async {
        // Arrange
        final updatedWalkabout = testWalkabout.copyWith(
          title: 'Updated Title',
          syncStatus: SyncStatus.local,
        );
        
        when(mockLocalDataSource.updateWalkabout(any))
            .thenAnswer((_) async => updatedWalkabout);

        // Act
        final result = await repository.updateWalkabout(testWalkabout);

        // Assert
        expect(result.syncStatus, equals(SyncStatus.local));
        verify(mockLocalDataSource.updateWalkabout(any)).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(mockLocalDataSource.updateWalkabout(any))
            .thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.updateWalkabout(testWalkabout),
          throwsA(isA<Exception>().having(
            (e) => e.toString(),
            'message',
            contains('Failed to update walkabout'),
          )),
        );
      });
    });

    group('deleteWalkabout', () {
      test('should delete walkabout successfully', () async {
        // Arrange
        when(mockLocalDataSource.deleteWalkabout('test-id'))
            .thenAnswer((_) async => true);

        // Act
        final result = await repository.deleteWalkabout('test-id');

        // Assert
        expect(result, isTrue);
        verify(mockLocalDataSource.deleteWalkabout('test-id')).called(1);
      });

      test('should return false when walkabout not found', () async {
        // Arrange
        when(mockLocalDataSource.deleteWalkabout('non-existent'))
            .thenAnswer((_) async => false);

        // Act
        final result = await repository.deleteWalkabout('non-existent');

        // Assert
        expect(result, isFalse);
        verify(mockLocalDataSource.deleteWalkabout('non-existent')).called(1);
      });
    });

    group('updateSyncStatus', () {
      test('should update sync status successfully', () async {
        // Arrange
        final updatedWalkabout = testWalkabout.copyWith(
          syncStatus: SyncStatus.synced,
        );
        
        when(mockLocalDataSource.updateSyncStatus('test-id', SyncStatus.synced))
            .thenAnswer((_) async => updatedWalkabout);

        // Act
        final result = await repository.updateSyncStatus('test-id', SyncStatus.synced);

        // Assert
        expect(result.syncStatus, equals(SyncStatus.synced));
        verify(mockLocalDataSource.updateSyncStatus('test-id', SyncStatus.synced)).called(1);
      });
    });
  });
}
