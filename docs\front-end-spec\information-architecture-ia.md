# Information Architecture (IA)

## Site Map / Screen Inventory

```mermaid
graph TD
    A[Login/Register] --> B[Dashboard]
    B --> C[Active Walkabouts]
    B --> D[Reports & Analytics]
    B --> E[Team Management]
    B --> F[Settings]
    C --> C1[New Walkabout]
    C --> C2[Continue Walkabout]
    C --> C3[Hazard Documentation]
    D --> D1[Historical Data]
    D --> D2[Export Reports]
    D --> D3[Trends]
    E --> E1[Team Members]
    E --> E2[Roles]
    E --> E3[Assignments]
    F --> F1[Profile]
    F --> F2[Notifications]
    F --> F3[Offline Storage]
```

## Navigation Structure

**Primary Navigation:**
- Bottom navigation bar for core functions (Walkabouts, Reports, Team, Settings)
- Quick action FAB for new walkabout/hazard

**Secondary Navigation:**
- Swipe navigation between related screens
- Breadcrumb for deep navigation states

**Breadcrumb Strategy:**
- Show current location in walkabout flow
- Enable quick navigation to parent sections
