import '../entities/user.dart';

/// Repository interface for user profile management
abstract class UserRepository {
  /// Get user by ID
  /// 
  /// Returns: [User] object if found, null otherwise
  /// Throws: Exception if operation fails
  Future<User?> getUserById(String uid);

  /// Update user profile
  /// 
  /// Returns: Updated [User] object
  /// Throws: Exception if operation fails
  Future<User> updateUser(User user);

  /// Update user preferences
  /// 
  /// Returns: Updated [User] object with new preferences
  /// Throws: Exception if operation fails
  Future<User> updateUserPreferences(String uid, UserPreferences preferences);

  /// Update user role
  /// 
  /// Returns: Updated [User] object with new role
  /// Throws: Exception if operation fails
  Future<User> updateUserRole(String uid, UserRole role);

  /// Sync user data with remote server
  /// 
  /// Returns: Synced [User] object
  /// Throws: Exception if operation fails
  Future<User> syncUser(String uid);

  /// Get cached user data
  /// 
  /// Returns: [User] object from cache if available, null otherwise
  /// Throws: Exception if operation fails
  Future<User?> getCachedUser(String uid);

  /// Cache user data locally
  /// 
  /// Returns: void
  /// Throws: Exception if operation fails
  Future<void> cacheUser(User user);
}