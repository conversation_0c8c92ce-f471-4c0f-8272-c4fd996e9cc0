import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../domain/entities/user.dart';

/// Data model for User entity with JSON serialization
class UserModel extends User {
  const UserModel({
    required super.uid,
    super.id = '',
    required super.email,
    super.displayName,
    super.photoUrl,
    super.organization,
    required super.role,
    required super.createdAt,
    required super.updatedAt,
    super.lastLoginAt,
    super.preferences = const UserPreferences(),
    super.needsSync = false,
  });

  /// Create UserModel from User entity
  factory UserModel.fromEntity(User user) {
    return UserModel(
      uid: user.uid,
      id: user.id.isEmpty ? user.uid : user.id,
      email: user.email,
      displayName: user.displayName,
      photoUrl: user.photoUrl,
      organization: user.organization,
      role: user.role,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      lastLoginAt: user.lastLoginAt,
      preferences: user.preferences,
      needsSync: user.needsSync,
    );
  }

  /// Create UserModel from JSON map
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      uid: json['uid'] as String,
      id: json['id'] as String? ?? json['uid'] as String,
      email: json['email'] as String,
      displayName: json['displayName'] as String?,
      photoUrl: json['photoUrl'] as String?,
      organization: json['organization'] as String?,
      role: UserRole.values.firstWhere(
        (role) => role.name == json['role'],
        orElse: () => UserRole.inspector,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : DateTime.parse(json['createdAt'] as String),
      lastLoginAt: json['lastLoginAt'] != null
          ? DateTime.parse(json['lastLoginAt'] as String)
          : null,
      preferences: json['preferences'] != null
          ? UserPreferencesModel.fromJson(
              json['preferences'] as Map<String, dynamic>)
          : const UserPreferences(),
      needsSync: json['needsSync'] as bool? ?? false,
    );
  }

  /// Create UserModel from JSON string
  factory UserModel.fromJsonString(String jsonString) {
    final json = jsonDecode(jsonString) as Map<String, dynamic>;
    return UserModel.fromJson(json);
  }

  /// Convert UserModel to JSON map
  Map<String, dynamic> toJson() {
    return {
      'uid': uid,
      'id': id.isEmpty ? uid : id,
      'email': email,
      'displayName': displayName,
      'photoUrl': photoUrl,
      'organization': organization,
      'role': role.name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'lastLoginAt': lastLoginAt?.toIso8601String(),
      'preferences': UserPreferencesModel.fromEntity(preferences).toJson(),
      'needsSync': needsSync,
    };
  }

  /// Convert UserModel to JSON string
  String toJsonString() {
    return jsonEncode(toJson());
  }

  /// Convert to domain entity
  User toEntity() {
    return User(
      uid: uid,
      id: id,
      email: email,
      displayName: displayName,
      photoUrl: photoUrl,
      organization: organization,
      role: role,
      createdAt: createdAt,
      updatedAt: updatedAt,
      lastLoginAt: lastLoginAt,
      preferences: preferences,
      needsSync: needsSync,
    );
  }

  /// Convert to domain entity (alias for toEntity)
  User toDomain() {
    return toEntity();
  }

  @override
  UserModel copyWith({
    String? uid,
    String? id,
    String? email,
    String? displayName,
    String? photoUrl,
    String? organization,
    UserRole? role,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    UserPreferences? preferences,
    bool? needsSync,
  }) {
    return UserModel(
      uid: uid ?? this.uid,
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      organization: organization ?? this.organization,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
      needsSync: needsSync ?? this.needsSync,
    );
  }
}

/// Extension methods for UserModel
extension UserModelFirestore on UserModel {
  /// Create UserModel from Firestore document
  static UserModel fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    
    return UserModel(
      uid: data['uid'] ?? doc.id,
      id: doc.id,
      email: data['email'] ?? '',
      displayName: data['displayName'],
      photoUrl: data['photoUrl'],
      organization: data['organization'],
      role: _mapStringToUserRole(data['role'] ?? 'inspector'),
      createdAt: data['createdAt'] != null
          ? (data['createdAt'] as Timestamp).toDate()
          : DateTime.now(),
      updatedAt: data['updatedAt'] != null
          ? (data['updatedAt'] as Timestamp).toDate()
          : DateTime.now(),
      lastLoginAt: data['lastLoginAt'] != null
          ? (data['lastLoginAt'] as Timestamp).toDate()
          : null,
      preferences: data['preferences'] != null
          ? UserPreferencesModel.fromJson(data['preferences'])
          : const UserPreferences(),
      needsSync: false, // Always false when from Firestore
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'uid': uid,
      'email': email,
      'displayName': displayName,
      'photoUrl': photoUrl,
      'organization': organization,
      'role': role.name,
      'preferences': UserPreferencesModel.fromEntity(preferences).toJson(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(DateTime.now()),
      'lastLoginAt': lastLoginAt != null ? Timestamp.fromDate(lastLoginAt!) : null,
    };
  }
  
  /// Map string role to UserRole enum
  static UserRole _mapStringToUserRole(String role) {
    switch (role) {
      case 'admin':
        return UserRole.admin;
      case 'moderator':
        return UserRole.moderator;
      case 'manager':
        return UserRole.manager;
      case 'inspector':
      default:
        return UserRole.inspector;
    }
  }
}

/// Extension methods for UserModel with SQLite
extension UserModelSQLite on UserModel {
  /// Create UserModel from SQLite map
  static UserModel fromMap(Map<String, dynamic> map) {
    final preferencesMap = map['preferences'] != null
        ? jsonDecode(map['preferences']) as Map<String, dynamic>
        : <String, dynamic>{};
        
    return UserModel(
      uid: map['uid'] ?? '',
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      displayName: map['display_name'],
      photoUrl: map['photo_url'],
      organization: map['organization'],
      role: UserModelFirestore._mapStringToUserRole(map['role'] ?? 'inspector'),
      preferences: UserPreferencesModel(
        notificationsEnabled: preferencesMap['notificationsEnabled'] ?? true,
        language: preferencesMap['language'] ?? 'en',
        theme: preferencesMap['theme'] ?? 'system',
        offlineMode: preferencesMap['offlineMode'] ?? false,
      ),
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at'] ?? 0),
      updatedAt: DateTime.fromMillisecondsSinceEpoch(map['updated_at'] ?? 0),
      lastLoginAt: map['last_login_at'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['last_login_at'])
          : null,
      needsSync: map['needs_sync'] == 1,
    );
  }

  /// Convert to SQLite map
  Map<String, dynamic> toMap() {
    return {
      'uid': uid,
      'id': id.isEmpty ? uid : id,
      'email': email,
      'display_name': displayName,
      'photo_url': photoUrl,
      'organization': organization,
      'role': role.name,
      'preferences': jsonEncode(UserPreferencesModel.fromEntity(preferences).toJson()),
      'created_at': createdAt.millisecondsSinceEpoch,
      'updated_at': updatedAt.millisecondsSinceEpoch,
      'last_login_at': lastLoginAt?.millisecondsSinceEpoch,
      'needs_sync': needsSync ? 1 : 0,
      'cache_time': DateTime.now().millisecondsSinceEpoch,
    };
  }
}

/// Data model for UserPreferences with JSON serialization
class UserPreferencesModel extends UserPreferences {
  const UserPreferencesModel({
    super.notificationsEnabled = true,
    super.language = 'en',
    super.theme = 'system',
    super.offlineMode = false,
  });

  /// Create UserPreferencesModel from UserPreferences entity
  factory UserPreferencesModel.fromEntity(UserPreferences preferences) {
    return UserPreferencesModel(
      notificationsEnabled: preferences.notificationsEnabled,
      language: preferences.language,
      theme: preferences.theme,
      offlineMode: preferences.offlineMode,
    );
  }

  /// Create UserPreferencesModel from JSON map
  factory UserPreferencesModel.fromJson(Map<String, dynamic> json) {
    return UserPreferencesModel(
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      language: json['language'] as String? ?? 'en',
      theme: json['theme'] as String? ?? 'system',
      offlineMode: json['offlineMode'] as bool? ?? false,
    );
  }

  /// Convert UserPreferencesModel to JSON map
  Map<String, dynamic> toJson() {
    return {
      'notificationsEnabled': notificationsEnabled,
      'language': language,
      'theme': theme,
      'offlineMode': offlineMode,
    };
  }

  /// Convert to domain entity
  UserPreferences toEntity() {
    return UserPreferences(
      notificationsEnabled: notificationsEnabled,
      language: language,
      theme: theme,
      offlineMode: offlineMode,
    );
  }

  @override
  UserPreferencesModel copyWith({
    bool? notificationsEnabled,
    String? language,
    String? theme,
    bool? offlineMode,
  }) {
    return UserPreferencesModel(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      language: language ?? this.language,
      theme: theme ?? this.theme,
      offlineMode: offlineMode ?? this.offlineMode,
    );
  }
}