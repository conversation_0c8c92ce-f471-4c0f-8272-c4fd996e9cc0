import '../entities/walkabout.dart';
import '../repositories/walkabout_repository.dart';

/// Use case for creating a new walkabout
/// 
/// This use case encapsulates the business logic for walkabout creation
/// following Clean Architecture principles.
class CreateWalkaboutUseCase {
  final WalkaboutRepository repository;

  const CreateWalkaboutUseCase({required this.repository});

  /// Execute the create walkabout use case
  /// 
  /// Takes a [CreateWalkaboutParams] object containing the walkabout details
  /// Returns the created walkabout with generated ID and timestamps
  /// Throws [Exception] if creation fails or validation errors occur
  Future<Walkabout> call(CreateWalkaboutParams params) async {
    // Validate input parameters
    _validateParams(params);

    // Generate unique ID and timestamps
    final now = DateTime.now();
    final walkaboutId = _generateWalkaboutId();

    // Create walkabout entity
    final walkabout = Walkabout(
      id: walkaboutId,
      title: params.title,
      description: params.description,
      createdAt: now,
      updatedAt: now,
      status: params.status ?? WalkaboutStatus.draft,
      location: params.location,
      userId: params.userId,
      isCompleted: false,
      syncStatus: SyncStatus.local,
    );

    // Save walkabout through repository
    return await repository.createWalkabout(walkabout);
  }

  /// Validate input parameters
  void _validateParams(CreateWalkaboutParams params) {
    if (params.title.trim().isEmpty) {
      throw ArgumentError('Walkabout title cannot be empty');
    }

    if (params.title.length > 100) {
      throw ArgumentError('Walkabout title cannot exceed 100 characters');
    }

    if (params.userId.trim().isEmpty) {
      throw ArgumentError('User ID is required');
    }

    if (params.description != null && params.description!.length > 500) {
      throw ArgumentError('Walkabout description cannot exceed 500 characters');
    }

    // Validate location coordinates if provided
    if (params.location != null) {
      final lat = params.location!.latitude;
      final lng = params.location!.longitude;
      
      if (lat < -90 || lat > 90) {
        throw ArgumentError('Invalid latitude: must be between -90 and 90');
      }
      
      if (lng < -180 || lng > 180) {
        throw ArgumentError('Invalid longitude: must be between -180 and 180');
      }
    }
  }

  /// Generate a unique walkabout ID
  /// 
  /// Uses timestamp and random component for uniqueness
  String _generateWalkaboutId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'walkabout_${timestamp}_$random';
  }
}

/// Parameters for creating a walkabout
class CreateWalkaboutParams {
  final String title;
  final String? description;
  final String userId;
  final GeoPoint? location;
  final WalkaboutStatus? status;

  const CreateWalkaboutParams({
    required this.title,
    this.description,
    required this.userId,
    this.location,
    this.status,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CreateWalkaboutParams &&
        other.title == title &&
        other.description == description &&
        other.userId == userId &&
        other.location == location &&
        other.status == status;
  }

  @override
  int get hashCode {
    return Object.hash(title, description, userId, location, status);
  }

  @override
  String toString() {
    return 'CreateWalkaboutParams(title: $title, description: $description, '
        'userId: $userId, location: $location, status: $status)';
  }
}
