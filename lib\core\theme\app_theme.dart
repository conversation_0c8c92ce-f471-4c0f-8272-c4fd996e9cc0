import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../constants/app_colors.dart';
import '../constants/app_text_styles.dart';
import '../constants/app_dimensions.dart';

/// Application theme configuration
class AppTheme {
  // Private constructor to prevent instantiation
  AppTheme._();

  /// Light theme configuration
  static ThemeData get lightTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      colorScheme: _lightColorScheme,
      textTheme: _textTheme,
      appBarTheme: _lightAppBarTheme,
      elevatedButtonTheme: _elevatedButtonTheme,
      textButtonTheme: _textButtonTheme,
      outlinedButtonTheme: _outlinedButtonTheme,
      inputDecorationTheme: _inputDecorationTheme,
      cardTheme: _cardTheme,
      bottomNavigationBarTheme: _bottomNavigationBarTheme,
      navigationBarTheme: _navigationBarTheme,
      drawerTheme: _drawerTheme,
      listTileTheme: _listTileTheme,
      dividerTheme: _dividerTheme,
      chipTheme: _chipTheme,
      dialogTheme: _dialogTheme,
      snackBarTheme: _snackBarTheme,
      tabBarTheme: _tabBarTheme,
      switchTheme: _switchTheme,
      checkboxTheme: _checkboxTheme,
      radioTheme: _radioTheme,
      sliderTheme: _sliderTheme,
      progressIndicatorTheme: _progressIndicatorTheme,
      floatingActionButtonTheme: _floatingActionButtonTheme,
      expansionTileTheme: _expansionTileTheme,
      tooltipTheme: _tooltipTheme,
      popupMenuTheme: _popupMenuTheme,
      bottomSheetTheme: _bottomSheetTheme,
      bannerTheme: _bannerTheme,
      scaffoldBackgroundColor: AppColors.background,
      visualDensity: VisualDensity.adaptivePlatformDensity,
      splashFactory: InkRipple.splashFactory,
    );
  }

  /// Dark theme configuration
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: _darkColorScheme,
      textTheme: _darkTextTheme,
      appBarTheme: _darkAppBarTheme,
      elevatedButtonTheme: _elevatedButtonTheme,
      textButtonTheme: _textButtonTheme,
      outlinedButtonTheme: _outlinedButtonTheme,
      inputDecorationTheme: _darkInputDecorationTheme,
      cardTheme: _darkCardTheme,
      bottomNavigationBarTheme: _darkBottomNavigationBarTheme,
      navigationBarTheme: _darkNavigationBarTheme,
      drawerTheme: _darkDrawerTheme,
      listTileTheme: _darkListTileTheme,
      dividerTheme: _darkDividerTheme,
      chipTheme: _darkChipTheme,
      dialogTheme: _darkDialogTheme,
      snackBarTheme: _darkSnackBarTheme,
      tabBarTheme: _darkTabBarTheme,
      switchTheme: _switchTheme,
      checkboxTheme: _checkboxTheme,
      radioTheme: _radioTheme,
      sliderTheme: _sliderTheme,
      progressIndicatorTheme: _progressIndicatorTheme,
      floatingActionButtonTheme: _floatingActionButtonTheme,
      expansionTileTheme: _expansionTileTheme,
      tooltipTheme: _tooltipTheme,
      popupMenuTheme: _darkPopupMenuTheme,
      bottomSheetTheme: _darkBottomSheetTheme,
      bannerTheme: _bannerTheme,
      scaffoldBackgroundColor: const Color(0xFF121212),
      visualDensity: VisualDensity.adaptivePlatformDensity,
      splashFactory: InkRipple.splashFactory,
    );
  }

  // Color schemes
  static const ColorScheme _lightColorScheme = ColorScheme.light(
    primary: AppColors.primary,
    onPrimary: AppColors.textOnPrimary,
    primaryContainer: AppColors.primaryLight,
    onPrimaryContainer: AppColors.textPrimary,
    secondary: AppColors.secondary,
    onSecondary: AppColors.textOnSecondary,
    secondaryContainer: AppColors.secondaryLight,
    onSecondaryContainer: AppColors.textPrimary,
    tertiary: AppColors.info,
    onTertiary: AppColors.textOnPrimary,
    error: AppColors.error,
    onError: AppColors.textOnPrimary,
    errorContainer: Color(0xFFFFDAD6),
    onErrorContainer: Color(0xFF410002),
    background: AppColors.background,
    onBackground: AppColors.textPrimary,
    surface: AppColors.surface,
    onSurface: AppColors.textPrimary,
    surfaceVariant: AppColors.surfaceVariant,
    onSurfaceVariant: AppColors.textSecondary,
    outline: AppColors.border,
    outlineVariant: AppColors.borderLight,
    shadow: AppColors.shadow,
    scrim: AppColors.overlay,
    inverseSurface: Color(0xFF313033),
    onInverseSurface: Color(0xFFF4EFF4),
    inversePrimary: Color(0xFFBBDEFB),
  );

  static const ColorScheme _darkColorScheme = ColorScheme.dark(
    primary: Color(0xFFBBDEFB),
    onPrimary: Color(0xFF003258),
    primaryContainer: Color(0xFF004A77),
    onPrimaryContainer: Color(0xFFBBDEFB),
    secondary: Color(0xFFB2DFDB),
    onSecondary: Color(0xFF003A37),
    secondaryContainer: Color(0xFF005450),
    onSecondaryContainer: Color(0xFFB2DFDB),
    tertiary: Color(0xFF90CAF9),
    onTertiary: Color(0xFF003258),
    error: Color(0xFFFFB4AB),
    onError: Color(0xFF690005),
    errorContainer: Color(0xFF93000A),
    onErrorContainer: Color(0xFFFFDAD6),
    background: Color(0xFF121212),
    onBackground: Color(0xFFE6E1E5),
    surface: Color(0xFF1E1E1E),
    onSurface: Color(0xFFE6E1E5),
    surfaceVariant: Color(0xFF2A2A2A),
    onSurfaceVariant: Color(0xFFCAC4D0),
    outline: Color(0xFF938F99),
    outlineVariant: Color(0xFF49454F),
    shadow: Color(0xFF000000),
    scrim: Color(0xFF000000),
    inverseSurface: Color(0xFFE6E1E5),
    onInverseSurface: Color(0xFF313033),
    inversePrimary: Color(0xFF2196F3),
  );

  // Text themes
  static const TextTheme _textTheme = TextTheme(
    displayLarge: AppTextStyles.displayLarge,
    displayMedium: AppTextStyles.displayMedium,
    displaySmall: AppTextStyles.displaySmall,
    headlineLarge: AppTextStyles.headlineLarge,
    headlineMedium: AppTextStyles.headlineMedium,
    headlineSmall: AppTextStyles.headlineSmall,
    titleLarge: AppTextStyles.titleLarge,
    titleMedium: AppTextStyles.titleMedium,
    titleSmall: AppTextStyles.titleSmall,
    labelLarge: AppTextStyles.labelLarge,
    labelMedium: AppTextStyles.labelMedium,
    labelSmall: AppTextStyles.labelSmall,
    bodyLarge: AppTextStyles.bodyLarge,
    bodyMedium: AppTextStyles.bodyMedium,
    bodySmall: AppTextStyles.bodySmall,
  );

  static const TextTheme _darkTextTheme = TextTheme(
    displayLarge: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 57.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: -0.25,
      height: 1.12,
      color: Color(0xFFE6E1E5),
    ),
    displayMedium: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 45.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.0,
      height: 1.16,
      color: Color(0xFFE6E1E5),
    ),
    displaySmall: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 36.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.0,
      height: 1.22,
      color: Color(0xFFE6E1E5),
    ),
    headlineLarge: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 32.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.0,
      height: 1.25,
      color: Color(0xFFE6E1E5),
    ),
    headlineMedium: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 28.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.0,
      height: 1.29,
      color: Color(0xFFE6E1E5),
    ),
    headlineSmall: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 24.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.0,
      height: 1.33,
      color: Color(0xFFE6E1E5),
    ),
    titleLarge: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 22.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.0,
      height: 1.27,
      color: Color(0xFFE6E1E5),
    ),
    titleMedium: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 16.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.15,
      height: 1.50,
      color: Color(0xFFE6E1E5),
    ),
    titleSmall: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 14.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.10,
      height: 1.43,
      color: Color(0xFFE6E1E5),
    ),
    labelLarge: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 14.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.10,
      height: 1.43,
      color: Color(0xFFE6E1E5),
    ),
    labelMedium: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 12.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.50,
      height: 1.33,
      color: Color(0xFFE6E1E5),
    ),
    labelSmall: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 11.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.50,
      height: 1.45,
      color: Color(0xFFE6E1E5),
    ),
    bodyLarge: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 16.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.15,
      height: 1.50,
      color: Color(0xFFE6E1E5),
    ),
    bodyMedium: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 14.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.25,
      height: 1.43,
      color: Color(0xFFE6E1E5),
    ),
    bodySmall: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 12.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.40,
      height: 1.33,
      color: Color(0xFFCAC4D0),
    ),
  );

  // Component themes
  static const AppBarTheme _lightAppBarTheme = AppBarTheme(
    elevation: AppDimensions.appBarElevation,
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.textOnPrimary,
    titleTextStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 20.0,
      fontWeight: AppTextStyles.medium,
      color: AppColors.textOnPrimary,
    ),
    iconTheme: IconThemeData(
      color: AppColors.textOnPrimary,
      size: AppDimensions.appBarIconSize,
    ),
    actionsIconTheme: IconThemeData(
      color: AppColors.textOnPrimary,
      size: AppDimensions.appBarIconSize,
    ),
    systemOverlayStyle: SystemUiOverlayStyle.light,
  );

  static const AppBarTheme _darkAppBarTheme = AppBarTheme(
    elevation: AppDimensions.appBarElevation,
    backgroundColor: Color(0xFF1E1E1E),
    foregroundColor: Color(0xFFE6E1E5),
    titleTextStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 20.0,
      fontWeight: AppTextStyles.medium,
      color: Color(0xFFE6E1E5),
    ),
    iconTheme: IconThemeData(
      color: Color(0xFFE6E1E5),
      size: AppDimensions.appBarIconSize,
    ),
    actionsIconTheme: IconThemeData(
      color: Color(0xFFE6E1E5),
      size: AppDimensions.appBarIconSize,
    ),
    systemOverlayStyle: SystemUiOverlayStyle.light,
  );

  static final ElevatedButtonThemeData _elevatedButtonTheme =
      ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      minimumSize: const Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.buttonPadding),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      textStyle: AppTextStyles.buttonMedium,
      elevation: 2.0,
    ),
  );

  static final TextButtonThemeData _textButtonTheme = TextButtonThemeData(
    style: TextButton.styleFrom(
      minimumSize: const Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.buttonPadding),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      textStyle: AppTextStyles.buttonMedium,
    ),
  );

  static final OutlinedButtonThemeData _outlinedButtonTheme =
      OutlinedButtonThemeData(
    style: OutlinedButton.styleFrom(
      minimumSize: const Size(AppDimensions.buttonMinWidth, AppDimensions.buttonHeight),
      padding: const EdgeInsets.symmetric(horizontal: AppDimensions.buttonPadding),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.radiusM),
      ),
      side: const BorderSide(color: AppColors.border),
      textStyle: AppTextStyles.buttonMedium,
    ),
  );

  static const InputDecorationTheme _inputDecorationTheme =
      InputDecorationTheme(
    filled: true,
    fillColor: AppColors.surface,
    contentPadding: EdgeInsets.all(AppDimensions.inputPadding),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: AppColors.border),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: AppColors.border),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: AppColors.primary, width: AppDimensions.inputBorderWidthFocused),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: AppColors.error),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: AppColors.error, width: AppDimensions.inputBorderWidthFocused),
    ),
    labelStyle: AppTextStyles.inputLabel,
    hintStyle: AppTextStyles.inputHint,
    errorStyle: AppTextStyles.inputError,
  );

  static const InputDecorationTheme _darkInputDecorationTheme =
      InputDecorationTheme(
    filled: true,
    fillColor: Color(0xFF2A2A2A),
    contentPadding: EdgeInsets.all(AppDimensions.inputPadding),
    border: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: Color(0xFF49454F)),
    ),
    enabledBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: Color(0xFF49454F)),
    ),
    focusedBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: Color(0xFFBBDEFB), width: AppDimensions.inputBorderWidthFocused),
    ),
    errorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: Color(0xFFFFB4AB)),
    ),
    focusedErrorBorder: OutlineInputBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
      borderSide: BorderSide(color: Color(0xFFFFB4AB), width: AppDimensions.inputBorderWidthFocused),
    ),
    labelStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 12.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.40,
      height: 1.33,
      color: Color(0xFFCAC4D0),
    ),
    hintStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 16.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.15,
      height: 1.50,
      color: Color(0xFF938F99),
    ),
    errorStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 12.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.40,
      height: 1.33,
      color: Color(0xFFFFB4AB),
    ),
  );

  static const CardTheme _cardTheme = CardTheme(
    elevation: AppDimensions.cardElevation,
    margin: EdgeInsets.all(AppDimensions.cardMargin),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.cardRadius)),
    ),
  );

  static const CardTheme _darkCardTheme = CardTheme(
    elevation: AppDimensions.cardElevation,
    margin: EdgeInsets.all(AppDimensions.cardMargin),
    color: Color(0xFF2A2A2A),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.cardRadius)),
    ),
  );

  static const BottomNavigationBarThemeData _bottomNavigationBarTheme =
      BottomNavigationBarThemeData(
    elevation: AppDimensions.bottomNavElevation,
    selectedItemColor: AppColors.primary,
    unselectedItemColor: AppColors.textSecondary,
    selectedLabelStyle: AppTextStyles.labelSmall,
    unselectedLabelStyle: AppTextStyles.labelSmall,
    type: BottomNavigationBarType.fixed,
  );

  static const BottomNavigationBarThemeData _darkBottomNavigationBarTheme =
      BottomNavigationBarThemeData(
    elevation: AppDimensions.bottomNavElevation,
    backgroundColor: Color(0xFF1E1E1E),
    selectedItemColor: Color(0xFFBBDEFB),
    unselectedItemColor: Color(0xFF938F99),
    selectedLabelStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 11.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.50,
      height: 1.45,
      color: Color(0xFFBBDEFB),
    ),
    unselectedLabelStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 11.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.50,
      height: 1.45,
      color: Color(0xFF938F99),
    ),
    type: BottomNavigationBarType.fixed,
  );

  static const NavigationBarThemeData _navigationBarTheme =
      NavigationBarThemeData(
    elevation: AppDimensions.bottomNavElevation,
    labelTextStyle: MaterialStatePropertyAll(AppTextStyles.labelSmall),
    iconTheme: MaterialStatePropertyAll(
      IconThemeData(
        size: AppDimensions.bottomNavIconSize,
      ),
    ),
  );

  static const NavigationBarThemeData _darkNavigationBarTheme =
      NavigationBarThemeData(
    elevation: AppDimensions.bottomNavElevation,
    backgroundColor: Color(0xFF1E1E1E),
    labelTextStyle: MaterialStatePropertyAll(
      TextStyle(
        fontFamily: AppTextStyles.fontFamily,
        fontSize: 11.0,
        fontWeight: AppTextStyles.medium,
        letterSpacing: 0.50,
        height: 1.45,
        color: Color(0xFFE6E1E5),
      ),
    ),
    iconTheme: MaterialStatePropertyAll(
      IconThemeData(
        size: AppDimensions.bottomNavIconSize,
        color: Color(0xFFE6E1E5),
      ),
    ),
  );

  static const DrawerThemeData _drawerTheme = DrawerThemeData(
    backgroundColor: AppColors.surface,
    elevation: 16.0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topRight: Radius.circular(AppDimensions.radiusL),
        bottomRight: Radius.circular(AppDimensions.radiusL),
      ),
    ),
  );

  static const DrawerThemeData _darkDrawerTheme = DrawerThemeData(
    backgroundColor: Color(0xFF1E1E1E),
    elevation: 16.0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topRight: Radius.circular(AppDimensions.radiusL),
        bottomRight: Radius.circular(AppDimensions.radiusL),
      ),
    ),
  );

  static const ListTileThemeData _listTileTheme = ListTileThemeData(
    contentPadding: EdgeInsets.symmetric(
      horizontal: AppDimensions.listItemPadding,
      vertical: AppDimensions.paddingS,
    ),
    iconColor: AppColors.textSecondary,
    textColor: AppColors.textPrimary,
    titleTextStyle: AppTextStyles.bodyLarge,
    subtitleTextStyle: AppTextStyles.bodySmall,
  );

  static const ListTileThemeData _darkListTileTheme = ListTileThemeData(
    contentPadding: EdgeInsets.symmetric(
      horizontal: AppDimensions.listItemPadding,
      vertical: AppDimensions.paddingS,
    ),
    iconColor: Color(0xFF938F99),
    textColor: Color(0xFFE6E1E5),
    titleTextStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 16.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.15,
      height: 1.50,
      color: Color(0xFFE6E1E5),
    ),
    subtitleTextStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 12.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.40,
      height: 1.33,
      color: Color(0xFFCAC4D0),
    ),
  );

  static const DividerThemeData _dividerTheme = DividerThemeData(
    color: AppColors.border,
    thickness: AppDimensions.dividerThickness,
    indent: AppDimensions.dividerIndent,
    endIndent: AppDimensions.dividerIndent,
  );

  static const DividerThemeData _darkDividerTheme = DividerThemeData(
    color: Color(0xFF49454F),
    thickness: AppDimensions.dividerThickness,
    indent: AppDimensions.dividerIndent,
    endIndent: AppDimensions.dividerIndent,
  );

  static final ChipThemeData _chipTheme = ChipThemeData(
    backgroundColor: AppColors.surfaceVariant,
    selectedColor: AppColors.primary,
    disabledColor: AppColors.disabled,
    labelStyle: AppTextStyles.labelMedium,
    secondaryLabelStyle: AppTextStyles.labelMedium,
    padding: const EdgeInsets.symmetric(horizontal: AppDimensions.chipPadding),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.chipRadius),
    ),
  );

  static final ChipThemeData _darkChipTheme = ChipThemeData(
    backgroundColor: const Color(0xFF2A2A2A),
    selectedColor: const Color(0xFFBBDEFB),
    disabledColor: const Color(0xFF49454F),
    labelStyle: const TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 12.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.50,
      height: 1.33,
      color: Color(0xFFE6E1E5),
    ),
    secondaryLabelStyle: const TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 12.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.50,
      height: 1.33,
      color: Color(0xFFE6E1E5),
    ),
    padding: const EdgeInsets.symmetric(horizontal: AppDimensions.chipPadding),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.chipRadius),
    ),
  );

  static const DialogTheme _dialogTheme = DialogTheme(
    backgroundColor: AppColors.surface,
    elevation: AppDimensions.dialogElevation,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.dialogRadius)),
    ),
    titleTextStyle: AppTextStyles.headlineSmall,
    contentTextStyle: AppTextStyles.bodyMedium,
  );

  static const DialogTheme _darkDialogTheme = DialogTheme(
    backgroundColor: Color(0xFF2A2A2A),
    elevation: AppDimensions.dialogElevation,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.dialogRadius)),
    ),
    titleTextStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 24.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.0,
      height: 1.33,
      color: Color(0xFFE6E1E5),
    ),
    contentTextStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 14.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.25,
      height: 1.43,
      color: Color(0xFFE6E1E5),
    ),
  );

  static const SnackBarThemeData _snackBarTheme = SnackBarThemeData(
    backgroundColor: AppColors.textPrimary,
    contentTextStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 14.0,
      fontWeight: AppTextStyles.regular,
      color: AppColors.textOnPrimary,
    ),
    actionTextColor: AppColors.primary,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.snackbarRadius)),
    ),
    elevation: AppDimensions.snackbarElevation,
    behavior: SnackBarBehavior.floating,
  );

  static const SnackBarThemeData _darkSnackBarTheme = SnackBarThemeData(
    backgroundColor: Color(0xFF2A2A2A),
    contentTextStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 14.0,
      fontWeight: AppTextStyles.regular,
      color: Color(0xFFE6E1E5),
    ),
    actionTextColor: Color(0xFFBBDEFB),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.snackbarRadius)),
    ),
    elevation: AppDimensions.snackbarElevation,
    behavior: SnackBarBehavior.floating,
  );

  static const TabBarTheme _tabBarTheme = TabBarTheme(
    labelColor: AppColors.primary,
    unselectedLabelColor: AppColors.textSecondary,
    labelStyle: AppTextStyles.labelLarge,
    unselectedLabelStyle: AppTextStyles.labelLarge,
    indicator: UnderlineTabIndicator(
      borderSide: BorderSide(
        color: AppColors.primary,
        width: AppDimensions.tabIndicatorHeight,
      ),
    ),
  );

  static const TabBarTheme _darkTabBarTheme = TabBarTheme(
    labelColor: Color(0xFFBBDEFB),
    unselectedLabelColor: Color(0xFF938F99),
    labelStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 14.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.10,
      height: 1.43,
      color: Color(0xFFBBDEFB),
    ),
    unselectedLabelStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 14.0,
      fontWeight: AppTextStyles.medium,
      letterSpacing: 0.10,
      height: 1.43,
      color: Color(0xFF938F99),
    ),
    indicator: UnderlineTabIndicator(
      borderSide: BorderSide(
        color: Color(0xFFBBDEFB),
        width: AppDimensions.tabIndicatorHeight,
      ),
    ),
  );

  static final SwitchThemeData _switchTheme = SwitchThemeData(
    thumbColor: MaterialStateProperty.resolveWith<Color>(
      (Set<MaterialState> states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.primary;
        }
        return AppColors.textSecondary;
      },
    ),
    trackColor: MaterialStateProperty.resolveWith<Color>(
      (Set<MaterialState> states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.primaryLight;
        }
        return AppColors.borderLight;
      },
    ),
  );

  static final CheckboxThemeData _checkboxTheme = CheckboxThemeData(
    fillColor: MaterialStateProperty.resolveWith<Color>(
      (Set<MaterialState> states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.primary;
        }
        return Colors.transparent;
      },
    ),
    checkColor: MaterialStateProperty.all(AppColors.textOnPrimary),
    side: const BorderSide(color: AppColors.border, width: AppDimensions.checkboxBorderWidth),
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(AppDimensions.checkboxRadius),
    ),
  );

  static final RadioThemeData _radioTheme = RadioThemeData(
    fillColor: MaterialStateProperty.resolveWith<Color>(
      (Set<MaterialState> states) {
        if (states.contains(MaterialState.selected)) {
          return AppColors.primary;
        }
        return AppColors.border;
      },
    ),
  );

  static final SliderThemeData _sliderTheme = SliderThemeData(
    activeTrackColor: AppColors.primary,
    inactiveTrackColor: AppColors.borderLight,
    thumbColor: AppColors.primary,
    overlayColor: AppColors.primaryLight.withOpacity(0.12),
    valueIndicatorColor: AppColors.primary,
    valueIndicatorTextStyle: AppTextStyles.labelSmall.copyWith(
      color: AppColors.textOnPrimary,
    ),
  );

  static const ProgressIndicatorThemeData _progressIndicatorTheme =
      ProgressIndicatorThemeData(
    color: AppColors.primary,
    linearTrackColor: AppColors.borderLight,
    circularTrackColor: AppColors.borderLight,
  );

  static const FloatingActionButtonThemeData _floatingActionButtonTheme =
      FloatingActionButtonThemeData(
    backgroundColor: AppColors.primary,
    foregroundColor: AppColors.textOnPrimary,
    elevation: AppDimensions.fabElevation,
    shape: CircleBorder(),
  );

  static const ExpansionTileThemeData _expansionTileTheme =
      ExpansionTileThemeData(
    backgroundColor: AppColors.surfaceVariant,
    collapsedBackgroundColor: AppColors.surface,
    iconColor: AppColors.textSecondary,
    collapsedIconColor: AppColors.textSecondary,
    textColor: AppColors.textPrimary,
    collapsedTextColor: AppColors.textPrimary,
  );

  static const TooltipThemeData _tooltipTheme = TooltipThemeData(
    decoration: BoxDecoration(
      color: AppColors.textPrimary,
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusS)),
    ),
    textStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 12.0,
      fontWeight: AppTextStyles.regular,
      color: AppColors.textOnPrimary,
    ),
    padding: EdgeInsets.symmetric(
      horizontal: AppDimensions.paddingS,
      vertical: AppDimensions.paddingXS,
    ),
  );

  static const PopupMenuThemeData _popupMenuTheme = PopupMenuThemeData(
    color: AppColors.surface,
    elevation: 8.0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
    ),
    textStyle: AppTextStyles.bodyMedium,
  );

  static const PopupMenuThemeData _darkPopupMenuTheme = PopupMenuThemeData(
    color: Color(0xFF2A2A2A),
    elevation: 8.0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.all(Radius.circular(AppDimensions.radiusM)),
    ),
    textStyle: TextStyle(
      fontFamily: AppTextStyles.fontFamily,
      fontSize: 14.0,
      fontWeight: AppTextStyles.regular,
      letterSpacing: 0.25,
      height: 1.43,
      color: Color(0xFFE6E1E5),
    ),
  );

  static const BottomSheetThemeData _bottomSheetTheme = BottomSheetThemeData(
    backgroundColor: AppColors.surface,
    elevation: 16.0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(AppDimensions.radiusL),
        topRight: Radius.circular(AppDimensions.radiusL),
      ),
    ),
  );

  static const BottomSheetThemeData _darkBottomSheetTheme = BottomSheetThemeData(
    backgroundColor: Color(0xFF2A2A2A),
    elevation: 16.0,
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(AppDimensions.radiusL),
        topRight: Radius.circular(AppDimensions.radiusL),
      ),
    ),
  );

  static const MaterialBannerThemeData _bannerTheme = MaterialBannerThemeData(
    backgroundColor: AppColors.surfaceVariant,
    contentTextStyle: AppTextStyles.bodyMedium,
    padding: EdgeInsets.all(AppDimensions.paddingM),
  );
}