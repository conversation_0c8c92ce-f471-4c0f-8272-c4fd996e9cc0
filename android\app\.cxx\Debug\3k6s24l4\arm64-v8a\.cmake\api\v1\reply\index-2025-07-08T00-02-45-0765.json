{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "D:/src/AndroidSDK/cmake/3.22.1/bin/cmake.exe", "cpack": "D:/src/AndroidSDK/cmake/3.22.1/bin/cpack.exe", "ctest": "D:/src/AndroidSDK/cmake/3.22.1/bin/ctest.exe", "root": "D:/src/AndroidSDK/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-5ce53a8509ef2f25d18b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-c9368143baf658c120a7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-5b2f3fff3a3a14104457.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-c9368143baf658c120a7.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-5b2f3fff3a3a14104457.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-5ce53a8509ef2f25d18b.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}