import 'package:flutter/material.dart';
import 'app_colors.dart';

/// Application text style constants
class AppTextStyles {
  // Private constructor to prevent instantiation
  AppTextStyles._();

  // Font family
  static const String fontFamily = 'Roboto';

  // Font weights
  static const FontWeight light = FontWeight.w300;
  static const FontWeight regular = FontWeight.w400;
  static const FontWeight medium = FontWeight.w500;
  static const FontWeight semiBold = FontWeight.w600;
  static const FontWeight bold = FontWeight.w700;
  static const FontWeight extraBold = FontWeight.w800;

  // Display styles
  static const TextStyle displayLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 57.0,
    fontWeight: regular,
    letterSpacing: -0.25,
    height: 1.12,
    color: AppColors.textPrimary,
  );

  static const TextStyle displayMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 45.0,
    fontWeight: regular,
    letterSpacing: 0.0,
    height: 1.16,
    color: AppColors.textPrimary,
  );

  static const TextStyle displaySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 36.0,
    fontWeight: regular,
    letterSpacing: 0.0,
    height: 1.22,
    color: AppColors.textPrimary,
  );

  // Headline styles
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 32.0,
    fontWeight: regular,
    letterSpacing: 0.0,
    height: 1.25,
    color: AppColors.textPrimary,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 28.0,
    fontWeight: regular,
    letterSpacing: 0.0,
    height: 1.29,
    color: AppColors.textPrimary,
  );

  static const TextStyle headlineSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 24.0,
    fontWeight: regular,
    letterSpacing: 0.0,
    height: 1.33,
    color: AppColors.textPrimary,
  );

  // Title styles
  static const TextStyle titleLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 22.0,
    fontWeight: regular,
    letterSpacing: 0.0,
    height: 1.27,
    color: AppColors.textPrimary,
  );

  static const TextStyle titleMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16.0,
    fontWeight: medium,
    letterSpacing: 0.15,
    height: 1.50,
    color: AppColors.textPrimary,
  );

  static const TextStyle titleSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: medium,
    letterSpacing: 0.10,
    height: 1.43,
    color: AppColors.textPrimary,
  );

  // Label styles
  static const TextStyle labelLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: medium,
    letterSpacing: 0.10,
    height: 1.43,
    color: AppColors.textPrimary,
  );

  static const TextStyle labelMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.0,
    fontWeight: medium,
    letterSpacing: 0.50,
    height: 1.33,
    color: AppColors.textPrimary,
  );

  static const TextStyle labelSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 11.0,
    fontWeight: medium,
    letterSpacing: 0.50,
    height: 1.45,
    color: AppColors.textPrimary,
  );

  // Body styles
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16.0,
    fontWeight: regular,
    letterSpacing: 0.15,
    height: 1.50,
    color: AppColors.textPrimary,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: regular,
    letterSpacing: 0.25,
    height: 1.43,
    color: AppColors.textPrimary,
  );

  static const TextStyle bodySmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.0,
    fontWeight: regular,
    letterSpacing: 0.40,
    height: 1.33,
    color: AppColors.textSecondary,
  );

  // Button styles
  static const TextStyle buttonLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16.0,
    fontWeight: medium,
    letterSpacing: 0.15,
    height: 1.25,
    color: AppColors.textOnPrimary,
  );

  static const TextStyle buttonMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: medium,
    letterSpacing: 0.10,
    height: 1.43,
    color: AppColors.textOnPrimary,
  );

  static const TextStyle buttonSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.0,
    fontWeight: medium,
    letterSpacing: 0.50,
    height: 1.33,
    color: AppColors.textOnPrimary,
  );

  // Input field styles
  static const TextStyle inputText = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16.0,
    fontWeight: regular,
    letterSpacing: 0.15,
    height: 1.50,
    color: AppColors.textPrimary,
  );

  static const TextStyle inputLabel = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.0,
    fontWeight: regular,
    letterSpacing: 0.40,
    height: 1.33,
    color: AppColors.textSecondary,
  );

  static const TextStyle inputHint = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16.0,
    fontWeight: regular,
    letterSpacing: 0.15,
    height: 1.50,
    color: AppColors.textHint,
  );

  static const TextStyle inputError = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.0,
    fontWeight: regular,
    letterSpacing: 0.40,
    height: 1.33,
    color: AppColors.error,
  );

  // Caption and overline
  static const TextStyle caption = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.0,
    fontWeight: regular,
    letterSpacing: 0.40,
    height: 1.33,
    color: AppColors.textSecondary,
  );

  static const TextStyle overline = TextStyle(
    fontFamily: fontFamily,
    fontSize: 10.0,
    fontWeight: regular,
    letterSpacing: 1.50,
    height: 1.60,
    color: AppColors.textSecondary,
  );

  // Link styles
  static const TextStyle link = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: regular,
    letterSpacing: 0.25,
    height: 1.43,
    color: AppColors.primary,
    decoration: TextDecoration.underline,
  );

  static const TextStyle linkSmall = TextStyle(
    fontFamily: fontFamily,
    fontSize: 12.0,
    fontWeight: regular,
    letterSpacing: 0.40,
    height: 1.33,
    color: AppColors.primary,
    decoration: TextDecoration.underline,
  );

  // Status text styles
  static const TextStyle success = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: medium,
    letterSpacing: 0.25,
    height: 1.43,
    color: AppColors.success,
  );

  static const TextStyle warning = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: medium,
    letterSpacing: 0.25,
    height: 1.43,
    color: AppColors.warning,
  );

  static const TextStyle error = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: medium,
    letterSpacing: 0.25,
    height: 1.43,
    color: AppColors.error,
  );

  static const TextStyle info = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14.0,
    fontWeight: medium,
    letterSpacing: 0.25,
    height: 1.43,
    color: AppColors.info,
  );

  // Utility methods
  static TextStyle withColor(TextStyle style, Color color) {
    return style.copyWith(color: color);
  }

  static TextStyle withWeight(TextStyle style, FontWeight weight) {
    return style.copyWith(fontWeight: weight);
  }

  static TextStyle withSize(TextStyle style, double size) {
    return style.copyWith(fontSize: size);
  }

  static TextStyle withDecoration(TextStyle style, TextDecoration decoration) {
    return style.copyWith(decoration: decoration);
  }

  static TextStyle withHeight(TextStyle style, double height) {
    return style.copyWith(height: height);
  }

  static TextStyle withLetterSpacing(TextStyle style, double letterSpacing) {
    return style.copyWith(letterSpacing: letterSpacing);
  }
}