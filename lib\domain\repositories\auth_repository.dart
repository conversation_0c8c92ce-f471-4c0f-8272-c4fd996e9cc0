import '../entities/user.dart';

/// Abstract repository interface for authentication operations
abstract class AuthRepository {
  /// Register a new user with email and password
  /// Returns the created user or throws an exception
  Future<User> registerWithEmail({
    required String email,
    required String password,
    String? displayName,
    String? organization,
  });

  /// Login user with email and password
  /// Returns the authenticated user or throws an exception
  Future<User> loginWithEmail({
    required String email,
    required String password,
  });

  /// Login user with SSO (Google)
  /// Returns the authenticated user or throws an exception
  Future<User> loginWithSSO();

  /// Logout the current user
  /// Clears authentication state and cached credentials
  Future<void> logout();

  /// Reset password for the given email
  /// Sends password reset email
  Future<void> resetPassword({required String email});

  /// Get the currently authenticated user
  /// Returns null if no user is authenticated
  Future<User?> getCurrentUser();

  /// Check if user is currently authenticated
  /// Returns true if user is logged in
  Future<bool> isAuthenticated();

  /// Stream of authentication state changes
  /// Emits user when authenticated, null when not authenticated
  Stream<User?> get authStateChanges;


  /// Get cached user for offline authentication
  /// Returns cached user or null if not available
  Future<User?> getCachedUser();

  /// Clear cached credentials
  /// Removes all locally stored authentication data
  Future<void> clearCachedCredentials();
}