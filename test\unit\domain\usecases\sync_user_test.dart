import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/user_repository.dart';
import 'package:safestride/domain/usecases/sync_user.dart';

import 'sync_user_test.mocks.dart';

@GenerateMocks([UserRepository])
void main() {
  late SyncUserUseCase useCase;
  late MockUserRepository mockUserRepository;

  setUp(() {
    mockUserRepository = MockUserRepository();
    useCase = SyncUserUseCase(mockUserRepository);
  });

  group('SyncUserUseCase', () {
    const testUid = 'test-uid-123';
    final testUser = User(
      id: testUid,
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime(2024, 1, 1),
      lastLoginAt: DateTime(2024, 1, 2),
      preferences: UserPreferences(),
    );

    test('should return synced user when sync succeeds', () async {
      // Arrange
      final syncedUser = testUser.copyWith(
        updatedAt: DateTime(2024, 1, 3),
        displayName: 'Updated Test User',
      );
      
      when(mockUserRepository.syncUser(testUid))
          .thenAnswer((_) async => syncedUser);

      // Act
      final result = await useCase(testUid);

      // Assert
      expect(result, equals(syncedUser));
      expect(result.displayName, equals('Updated Test User'));
      verify(mockUserRepository.syncUser(testUid)).called(1);
    });

    test('should throw exception when repository throws exception', () async {
      // Arrange
      when(mockUserRepository.syncUser(testUid))
          .thenThrow(Exception('Network error'));

      // Act & Assert
      expect(
        () async => await useCase(testUid),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Failed to sync user'),
        )),
      );
      verify(mockUserRepository.syncUser(testUid)).called(1);
    });

    test('should handle sync with updated preferences', () async {
      // Arrange
      final updatedPreferences = UserPreferences(
        language: 'es',
        theme: 'dark',
        notifications: false,
      );
      final syncedUser = testUser.copyWith(
        preferences: updatedPreferences,
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.syncUser(testUid))
          .thenAnswer((_) async => syncedUser);

      // Act
      final result = await useCase(testUid);

      // Assert
      expect(result.preferences.language, equals('es'));
      expect(result.preferences.theme, equals('dark'));
      expect(result.preferences.notifications, equals(false));
      verify(mockUserRepository.syncUser(testUid)).called(1);
    });

    test('should handle sync with role change', () async {
      // Arrange
      final syncedUser = testUser.copyWith(
        role: UserRole.manager,
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.syncUser(testUid))
          .thenAnswer((_) async => syncedUser);

      // Act
      final result = await useCase(testUid);

      // Assert
      expect(result.role, equals(UserRole.manager));
      verify(mockUserRepository.syncUser(testUid)).called(1);
    });

    test('should handle sync with organization change', () async {
      // Arrange
      final syncedUser = testUser.copyWith(
        organization: 'New Organization',
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.syncUser(testUid))
          .thenAnswer((_) async => syncedUser);

      // Act
      final result = await useCase(testUid);

      // Assert
      expect(result.organization, equals('New Organization'));
      verify(mockUserRepository.syncUser(testUid)).called(1);
    });

    test('should handle sync with photo URL update', () async {
      // Arrange
      final syncedUser = testUser.copyWith(
        photoUrl: 'https://example.com/new-photo.jpg',
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.syncUser(testUid))
          .thenAnswer((_) async => syncedUser);

      // Act
      final result = await useCase(testUid);

      // Assert
      expect(result.photoUrl, equals('https://example.com/new-photo.jpg'));
      verify(mockUserRepository.syncUser(testUid)).called(1);
    });

    test('should handle empty uid parameter', () async {
      // Arrange
      const emptyUid = '';
      when(mockUserRepository.syncUser(emptyUid))
          .thenThrow(Exception('User not found'));

      // Act & Assert
      expect(
        () async => await useCase(emptyUid),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Failed to sync user'),
        )),
      );
      verify(mockUserRepository.syncUser(emptyUid)).called(1);
    });

    test('should handle network timeout during sync', () async {
      // Arrange
      when(mockUserRepository.syncUser(testUid))
          .thenThrow(Exception('Connection timeout'));

      // Act & Assert
      expect(
        () async => await useCase(testUid),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          allOf([
            contains('Failed to sync user'),
            contains('Connection timeout'),
          ]),
        )),
      );
      verify(mockUserRepository.syncUser(testUid)).called(1);
    });

    test('should handle sync with all fields updated', () async {
      // Arrange
      final completelyUpdatedUser = User(
        id: testUid,
        email: '<EMAIL>',
        displayName: 'Updated Display Name',
        photoUrl: 'https://example.com/updated-photo.jpg',
        organization: 'Updated Organization',
        role: UserRole.admin,
        preferences: UserPreferences(
          language: 'fr',
          theme: 'dark',
          notifications: false,
        ),
        createdAt: testUser.createdAt,
        updatedAt: DateTime(2024, 1, 3),
        lastLoginAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.syncUser(testUid))
          .thenAnswer((_) async => completelyUpdatedUser);

      // Act
      final result = await useCase(testUid);

      // Assert
      expect(result.email, equals('<EMAIL>'));
      expect(result.displayName, equals('Updated Display Name'));
      expect(result.photoUrl, equals('https://example.com/updated-photo.jpg'));
      expect(result.organization, equals('Updated Organization'));
      expect(result.role, equals(UserRole.admin));
      expect(result.preferences.language, equals('fr'));
      expect(result.preferences.theme, equals('dark'));
      expect(result.preferences.notifications, equals(false));
      expect(result.updatedAt, isNotNull);
      verify(mockUserRepository.syncUser(testUid)).called(1);
    });

    test('should preserve created date during sync', () async {
      // Arrange
      final originalCreatedAt = DateTime(2024, 1, 1);
      final syncedUser = testUser.copyWith(
        displayName: 'Synced User',
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.syncUser(testUid))
          .thenAnswer((_) async => syncedUser);

      // Act
      final result = await useCase(testUid);

      // Assert
      expect(result.createdAt, equals(originalCreatedAt));
      expect(result.updatedAt, isNot(equals(originalCreatedAt)));
      verify(mockUserRepository.syncUser(testUid)).called(1);
    });
  });
}