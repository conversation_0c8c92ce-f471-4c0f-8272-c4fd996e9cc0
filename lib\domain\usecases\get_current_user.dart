import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Use case for getting the currently authenticated user
class GetCurrentUserUseCase {
  final AuthRepository _authRepository;

  const GetCurrentUserUseCase(this._authRepository);

  /// Execute getting current user
  /// 
  /// Returns: [User] object if user is authenticated, null otherwise
  /// Throws: Exception if operation fails
  Future<User?> call() async {
    try {
      // Try to get current user from remote authentication
      final user = await _authRepository.getCurrentUser();
      
      if (user != null) {
        return user;
      }
      
      // If no remote user, try to get cached user for offline access
      return await _authRepository.getCachedUser();
    } catch (e) {
      // If remote fails, try cached user
      try {
        return await _authRepository.getCachedUser();
      } catch (cacheError) {
        throw Exception('Failed to get current user: ${e.toString()}');
      }
    }
  }

  /// Get authentication state stream
  /// 
  /// Returns: Stream that emits user when authenticated, null when not
  Stream<User?> get authStateStream => _authRepository.authStateChanges;
}