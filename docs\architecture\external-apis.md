# External APIs

## Firebase Services

- **Purpose:** Backend-as-a-Service for authentication, data storage, and file management
- **Documentation:** https://firebase.google.com/docs
- **Authentication:** Firebase Auth with API keys and service accounts
- **Rate Limits:** Standard Firebase quotas (generous for mobile apps)

**Key Services Used:**
- Firestore - Real-time NoSQL database
- Firebase Storage - File and image storage
- Firebase Auth - User authentication
- Firebase Analytics - Usage tracking

**Integration Notes:** All Firebase services are integrated through official Flutter plugins with automatic offline support

## Google Maps API

- **Purpose:** Map visualization and location services
- **Documentation:** https://developers.google.com/maps/documentation
- **Authentication:** API key authentication
- **Rate Limits:** Based on Google Cloud billing plan

**Key Features Used:**
- Interactive map display
- Marker placement for hazard locations
- Location search and geocoding
