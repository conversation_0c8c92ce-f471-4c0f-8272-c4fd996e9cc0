import 'package:flutter/material.dart';
import '../../../domain/entities/walkabout.dart';

/// Widget for selecting walkabout location
/// 
/// Provides interface for location selection with current location
/// and manual coordinate entry options.
class LocationPickerWidget extends StatefulWidget {
  final GeoPoint? selectedLocation;
  final ValueChanged<GeoPoint?> onLocationSelected;

  const LocationPickerWidget({
    super.key,
    required this.selectedLocation,
    required this.onLocationSelected,
  });

  @override
  State<LocationPickerWidget> createState() => _LocationPickerWidgetState();
}

class _LocationPickerWidgetState extends State<LocationPickerWidget> {
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();
  bool _isManualEntry = false;

  @override
  void initState() {
    super.initState();
    _updateControllers();
  }

  @override
  void didUpdateWidget(LocationPickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedLocation != widget.selectedLocation) {
      _updateControllers();
    }
  }

  @override
  void dispose() {
    _latitudeController.dispose();
    _longitudeController.dispose();
    super.dispose();
  }

  void _updateControllers() {
    if (widget.selectedLocation != null) {
      _latitudeController.text = widget.selectedLocation!.latitude.toString();
      _longitudeController.text = widget.selectedLocation!.longitude.toString();
    } else {
      _latitudeController.clear();
      _longitudeController.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.location_on,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8.0),
                Text(
                  'Location',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                Switch(
                  value: _isManualEntry,
                  onChanged: (value) {
                    setState(() {
                      _isManualEntry = value;
                      if (!value) {
                        _latitudeController.clear();
                        _longitudeController.clear();
                        widget.onLocationSelected(null);
                      }
                    });
                  },
                ),
                const SizedBox(width: 8.0),
                const Text('Manual'),
              ],
            ),
            const SizedBox(height: 16.0),

            if (!_isManualEntry) ...[
              // Current location section
              _buildCurrentLocationSection(),
            ] else ...[
              // Manual entry section
              _buildManualEntrySection(),
            ],

            // Selected location display
            if (widget.selectedLocation != null) ...[
              const SizedBox(height: 16.0),
              _buildSelectedLocationDisplay(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentLocationSection() {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Column(
            children: [
              Icon(
                Icons.my_location,
                size: 48.0,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: 8.0),
              Text(
                'Use Current Location',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const SizedBox(height: 4.0),
              Text(
                'Tap to get your current GPS location',
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        const SizedBox(height: 12.0),
        ElevatedButton.icon(
          onPressed: _getCurrentLocation,
          icon: const Icon(Icons.gps_fixed),
          label: const Text('Get Current Location'),
        ),
      ],
    );
  }

  Widget _buildManualEntrySection() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _latitudeController,
                decoration: const InputDecoration(
                  labelText: 'Latitude',
                  hintText: 'e.g., 37.7749',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                  signed: true,
                ),
                onChanged: _onCoordinateChanged,
              ),
            ),
            const SizedBox(width: 12.0),
            Expanded(
              child: TextFormField(
                controller: _longitudeController,
                decoration: const InputDecoration(
                  labelText: 'Longitude',
                  hintText: 'e.g., -122.4194',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                  signed: true,
                ),
                onChanged: _onCoordinateChanged,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8.0),
        Text(
          'Enter coordinates manually (latitude: -90 to 90, longitude: -180 to 180)',
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildSelectedLocationDisplay() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 8.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Location Selected',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                Text(
                  'Lat: ${widget.selectedLocation!.latitude.toStringAsFixed(6)}, '
                  'Lng: ${widget.selectedLocation!.longitude.toStringAsFixed(6)}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => widget.onLocationSelected(null),
            icon: const Icon(Icons.clear),
            tooltip: 'Clear location',
          ),
        ],
      ),
    );
  }

  void _getCurrentLocation() async {
    // TODO: Implement actual GPS location retrieval
    // For now, simulate getting current location
    await Future.delayed(const Duration(seconds: 1));
    
    // Simulate a location (San Francisco coordinates)
    const location = GeoPoint(latitude: 37.7749, longitude: -122.4194);
    widget.onLocationSelected(location);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Current location retrieved')),
      );
    }
  }

  void _onCoordinateChanged(String value) {
    final latitude = double.tryParse(_latitudeController.text);
    final longitude = double.tryParse(_longitudeController.text);

    if (latitude != null && longitude != null) {
      // Validate coordinates
      if (latitude >= -90 && latitude <= 90 && 
          longitude >= -180 && longitude <= 180) {
        widget.onLocationSelected(GeoPoint(
          latitude: latitude,
          longitude: longitude,
        ));
      }
    } else {
      widget.onLocationSelected(null);
    }
  }
}
