# Generate SHA-1 and SHA-256 hashes for SafeStride
$text = 'SafeStride'
$bytes = [System.Text.Encoding]::UTF8.GetBytes($text)

# Generate SHA-1
$sha1 = [System.Security.Cryptography.SHA1]::Create()
$sha1Hash = $sha1.ComputeHash($bytes)
$sha1String = [System.BitConverter]::ToString($sha1Hash) -replace '-',''

# Generate SHA-256
$sha256 = [System.Security.Cryptography.SHA256]::Create()
$sha256Hash = $sha256.ComputeHash($bytes)
$sha256String = [System.BitConverter]::ToString($sha256Hash) -replace '-',''

Write-Host "Text: $text"
Write-Host "SHA-1:  $($sha1String.ToLower())"
Write-Host "SHA-256: $($sha256String.ToLower())"

# Clean up
$sha1.Dispose()
$sha256.Dispose()