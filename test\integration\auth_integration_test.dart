import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:provider/provider.dart';
import 'package:safestride/main.dart' as app;
import 'package:safestride/presentation/providers/auth_provider.dart';
import 'package:safestride/presentation/screens/auth/login_screen.dart';
import 'package:safestride/presentation/screens/auth/register_screen.dart';
import 'package:safestride/presentation/screens/auth/forgot_password_screen.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Authentication Integration Tests', () {
    testWidgets('complete authentication flow - register, login, logout', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Should start at login screen or navigate to it
      if (find.text('Welcome Back').evaluate().isEmpty) {
        // Navigate to login if not already there
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();
      }

      // Navigate to register screen
      await tester.tap(find.text("Don't have an account? Sign Up"));
      await tester.pumpAndSettle();

      // Verify we're on register screen
      expect(find.text('Create Account'), findsOneWidget);
      expect(find.text('Join SafeStride today'), findsOneWidget);

      // Fill out registration form
      await tester.enterText(find.byType(TextFormField).at(0), 'Integration Test User');
      await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(2), 'testpassword123');
      await tester.enterText(find.byType(TextFormField).at(3), 'testpassword123');

      // Submit registration
      await tester.tap(find.text('Create Account').last);
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should either be authenticated or show error
      // If registration succeeds, we should be logged in
      // If it fails (user already exists), we should see an error
      
      // Navigate back to login screen
      if (find.text('Already have an account? Sign In').evaluate().isNotEmpty) {
        await tester.tap(find.text('Already have an account? Sign In'));
        await tester.pumpAndSettle();
      }

      // Verify we're on login screen
      expect(find.text('Welcome Back'), findsOneWidget);
      expect(find.text('Sign in to your account'), findsOneWidget);

      // Test login with valid credentials
      await tester.enterText(find.byType(TextFormField).at(0), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(1), 'testpassword123');

      // Submit login
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should either be authenticated or show error
      // Check if we're authenticated by looking for logout option or main app content
      if (find.byIcon(Icons.logout).evaluate().isNotEmpty) {
        // We're logged in, test logout
        await tester.tap(find.byIcon(Icons.logout));
        await tester.pumpAndSettle();
        
        // Should be back to login screen
        expect(find.text('Welcome Back'), findsOneWidget);
      }
    });

    testWidgets('login form validation', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login screen if not already there
      if (find.text('Welcome Back').evaluate().isEmpty) {
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();
      }

      // Test empty form submission
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Should show validation errors
      expect(find.text('Please enter your email'), findsOneWidget);
      expect(find.text('Please enter your password'), findsOneWidget);

      // Test invalid email format
      await tester.enterText(find.byType(TextFormField).at(0), 'invalid-email');
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      expect(find.text('Please enter a valid email'), findsOneWidget);

      // Test valid email but empty password
      await tester.enterText(find.byType(TextFormField).at(0), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(1), '');
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      expect(find.text('Please enter your password'), findsOneWidget);
    });

    testWidgets('register form validation', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to register screen
      if (find.text("Don't have an account? Sign Up").evaluate().isNotEmpty) {
        await tester.tap(find.text("Don't have an account? Sign Up"));
        await tester.pumpAndSettle();
      }

      // Test empty form submission
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      // Should show validation errors
      expect(find.text('Please enter your full name'), findsOneWidget);
      expect(find.text('Please enter your email'), findsOneWidget);
      expect(find.text('Please enter a password'), findsOneWidget);

      // Test password mismatch
      await tester.enterText(find.byType(TextFormField).at(0), 'Test User');
      await tester.enterText(find.byType(TextFormField).at(1), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(2), 'password123');
      await tester.enterText(find.byType(TextFormField).at(3), 'differentpassword');
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      expect(find.text('Passwords do not match'), findsOneWidget);

      // Test short password
      await tester.enterText(find.byType(TextFormField).at(2), '123');
      await tester.enterText(find.byType(TextFormField).at(3), '123');
      await tester.tap(find.text('Create Account').last);
      await tester.pump();

      expect(find.text('Password must be at least 6 characters'), findsOneWidget);
    });

    testWidgets('forgot password flow', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login screen if not already there
      if (find.text('Welcome Back').evaluate().isEmpty) {
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();
      }

      // Navigate to forgot password screen
      await tester.tap(find.text('Forgot Password?'));
      await tester.pumpAndSettle();

      // Verify we're on forgot password screen
      expect(find.text('Reset Password'), findsOneWidget);
      expect(find.text('Enter your email address and we\'ll send you a link to reset your password'), findsOneWidget);

      // Test empty email submission
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      expect(find.text('Please enter your email'), findsOneWidget);

      // Test invalid email format
      await tester.enterText(find.byType(TextFormField), 'invalid-email');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      expect(find.text('Please enter a valid email'), findsOneWidget);

      // Test valid email
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should show success message or error
      // The actual behavior depends on Firebase configuration

      // Navigate back to login
      await tester.tap(find.text('Back to Sign In'));
      await tester.pumpAndSettle();

      // Should be back on login screen
      expect(find.text('Welcome Back'), findsOneWidget);
    });

    testWidgets('navigation between auth screens', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Start from login screen
      if (find.text('Welcome Back').evaluate().isEmpty) {
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();
      }

      // Navigate to register screen
      await tester.tap(find.text("Don't have an account? Sign Up"));
      await tester.pumpAndSettle();
      expect(find.text('Create Account'), findsOneWidget);

      // Navigate back to login screen
      await tester.tap(find.text('Already have an account? Sign In'));
      await tester.pumpAndSettle();
      expect(find.text('Welcome Back'), findsOneWidget);

      // Navigate to forgot password screen
      await tester.tap(find.text('Forgot Password?'));
      await tester.pumpAndSettle();
      expect(find.text('Reset Password'), findsOneWidget);

      // Navigate back to login screen
      await tester.tap(find.text('Back to Sign In'));
      await tester.pumpAndSettle();
      expect(find.text('Welcome Back'), findsOneWidget);
    });

    testWidgets('password visibility toggle', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login screen if not already there
      if (find.text('Welcome Back').evaluate().isEmpty) {
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();
      }

      // Enter password
      await tester.enterText(find.byType(TextFormField).at(1), 'testpassword');

      // Find password field
      final passwordField = find.byType(TextFormField).at(1);
      
      // Initially password should be obscured (visibility icon should be present)
      expect(find.byIcon(Icons.visibility), findsOneWidget);

      // Tap visibility toggle
      await tester.tap(find.byIcon(Icons.visibility));
      await tester.pump();

      // Password should now be visible (visibility_off icon should be present)
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);

      // Tap visibility toggle again
      await tester.tap(find.byIcon(Icons.visibility_off));
      await tester.pump();

      // Password should be obscured again (visibility icon should be present)
      expect(find.byIcon(Icons.visibility), findsOneWidget);
      expect(find.byIcon(Icons.visibility), findsOneWidget);
    });

    testWidgets('error handling and recovery', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to login screen if not already there
      if (find.text('Welcome Back').evaluate().isEmpty) {
        await tester.tap(find.text('Sign In'));
        await tester.pumpAndSettle();
      }

      // Try to login with invalid credentials
      await tester.enterText(find.byType(TextFormField).at(0), '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).at(1), 'wrongpassword');
      await tester.tap(find.text('Sign In'));
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should show error message
      // The exact error message depends on Firebase configuration
      
      // Clear the error by typing in a field
      await tester.enterText(find.byType(TextFormField).at(0), '<EMAIL>');
      await tester.pump();

      // Error should be cleared (this depends on implementation)
      // The error clearing behavior should be tested based on the actual implementation
    });
  });
}