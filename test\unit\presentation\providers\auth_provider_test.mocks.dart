// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in safestride/test/unit/presentation/providers/auth_provider_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/user.dart' as _i2;
import 'package:safestride/domain/usecases/get_current_user.dart' as _i9;
import 'package:safestride/domain/usecases/login_with_email.dart' as _i5;
import 'package:safestride/domain/usecases/login_with_sso.dart' as _i6;
import 'package:safestride/domain/usecases/logout.dart' as _i7;
import 'package:safestride/domain/usecases/register_with_email.dart' as _i3;
import 'package:safestride/domain/usecases/reset_password.dart' as _i8;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUser_0 extends _i1.SmartFake implements _i2.User {
  _FakeUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [RegisterWithEmailUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockRegisterWithEmailUseCase extends _i1.Mock
    implements _i3.RegisterWithEmailUseCase {
  MockRegisterWithEmailUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User> call({
    required String? email,
    required String? password,
    String? displayName,
    String? organization,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#call, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
              #organization: organization,
            }),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(
                this,
                Invocation.method(#call, [], {
                  #email: email,
                  #password: password,
                  #displayName: displayName,
                  #organization: organization,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.User>);
}

/// A class which mocks [LoginWithEmailUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginWithEmailUseCase extends _i1.Mock
    implements _i5.LoginWithEmailUseCase {
  MockLoginWithEmailUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User> call({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#call, [], {#email: email, #password: password}),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(
                this,
                Invocation.method(#call, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.User>);
}

/// A class which mocks [LoginWithSSOUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLoginWithSSOUseCase extends _i1.Mock
    implements _i6.LoginWithSSOUseCase {
  MockLoginWithSSOUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User> call() =>
      (super.noSuchMethod(
            Invocation.method(#call, []),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(this, Invocation.method(#call, [])),
            ),
          )
          as _i4.Future<_i2.User>);
}

/// A class which mocks [LogoutUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockLogoutUseCase extends _i1.Mock implements _i7.LogoutUseCase {
  MockLogoutUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> call() =>
      (super.noSuchMethod(
            Invocation.method(#call, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

/// A class which mocks [ResetPasswordUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockResetPasswordUseCase extends _i1.Mock
    implements _i8.ResetPasswordUseCase {
  MockResetPasswordUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<void> call({required String? email}) =>
      (super.noSuchMethod(
            Invocation.method(#call, [], {#email: email}),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}

/// A class which mocks [GetCurrentUserUseCase].
///
/// See the documentation for Mockito's code generation for more information.
class MockGetCurrentUserUseCase extends _i1.Mock
    implements _i9.GetCurrentUserUseCase {
  MockGetCurrentUserUseCase() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.User?> get authStateStream =>
      (super.noSuchMethod(
            Invocation.getter(#authStateStream),
            returnValue: _i4.Stream<_i2.User?>.empty(),
          )
          as _i4.Stream<_i2.User?>);

  @override
  _i4.Future<_i2.User?> call() =>
      (super.noSuchMethod(
            Invocation.method(#call, []),
            returnValue: _i4.Future<_i2.User?>.value(),
          )
          as _i4.Future<_i2.User?>);
}
