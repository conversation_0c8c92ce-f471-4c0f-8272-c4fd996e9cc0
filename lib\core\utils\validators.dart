/// A utility class for common validation logic.
class Validators {
  /// Validates an email address using a regular expression.
  ///
  /// Returns `true` if the email is valid, `false` otherwise.
  static bool isValidEmail(String email) {
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email.trim());
  }

  /// Validates a password to ensure it meets the minimum length requirement.
  ///
  /// Returns `true` if the password is valid, `false` otherwise.
  static bool isValidPassword(String password) {
    return password.length >= 6;
  }
}