import '../entities/user.dart';
import '../repositories/user_repository.dart';

/// Use case for updating a user's role
class UpdateUserRoleUseCase {
  final UserRepository _userRepository;

  const UpdateUserRoleUseCase(this._userRepository);

  /// Execute updating user role
  /// 
  /// Returns: Updated [User] object with new role
  /// Throws: Exception if operation fails
  Future<User> call({
    required String uid,
    required UserRole role,
  }) async {
    try {
      return await _userRepository.updateUserRole(uid, role);
    } catch (e) {
      throw Exception('Failed to update user role: ${e.toString()}');
    }
  }
}