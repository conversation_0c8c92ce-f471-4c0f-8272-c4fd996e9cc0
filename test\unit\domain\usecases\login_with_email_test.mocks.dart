// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in safestride/test/unit/domain/usecases/login_with_email_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/user.dart' as _i2;
import 'package:safestride/domain/repositories/auth_repository.dart' as _i3;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUser_0 extends _i1.SmartFake implements _i2.User {
  _FakeUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [AuthRepository].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthRepository extends _i1.Mock implements _i3.AuthRepository {
  MockAuthRepository() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Stream<_i2.User?> get authStateChanges =>
      (super.noSuchMethod(
            Invocation.getter(#authStateChanges),
            returnValue: _i4.Stream<_i2.User?>.empty(),
          )
          as _i4.Stream<_i2.User?>);

  @override
  _i4.Future<_i2.User> registerWithEmail({
    required String? email,
    required String? password,
    String? displayName,
    String? organization,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerWithEmail, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
              #organization: organization,
            }),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(
                this,
                Invocation.method(#registerWithEmail, [], {
                  #email: email,
                  #password: password,
                  #displayName: displayName,
                  #organization: organization,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.User>);

  @override
  _i4.Future<_i2.User> loginWithEmail({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginWithEmail, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(
                this,
                Invocation.method(#loginWithEmail, [], {
                  #email: email,
                  #password: password,
                }),
              ),
            ),
          )
          as _i4.Future<_i2.User>);

  @override
  _i4.Future<_i2.User> loginWithSSO() =>
      (super.noSuchMethod(
            Invocation.method(#loginWithSSO, []),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(this, Invocation.method(#loginWithSSO, [])),
            ),
          )
          as _i4.Future<_i2.User>);

  @override
  _i4.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> resetPassword({required String? email}) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [], {#email: email}),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<_i2.User?> getCurrentUser() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentUser, []),
            returnValue: _i4.Future<_i2.User?>.value(),
          )
          as _i4.Future<_i2.User?>);

  @override
  _i4.Future<bool> isAuthenticated() =>
      (super.noSuchMethod(
            Invocation.method(#isAuthenticated, []),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<_i2.User?> getCachedUser() =>
      (super.noSuchMethod(
            Invocation.method(#getCachedUser, []),
            returnValue: _i4.Future<_i2.User?>.value(),
          )
          as _i4.Future<_i2.User?>);

  @override
  _i4.Future<void> clearCachedCredentials() =>
      (super.noSuchMethod(
            Invocation.method(#clearCachedCredentials, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);
}
