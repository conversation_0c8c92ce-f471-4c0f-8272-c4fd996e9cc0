import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';
import 'package:safestride/presentation/providers/auth_provider.dart';
import 'package:safestride/presentation/screens/auth/register_screen.dart';

import 'register_screen_test.mocks.dart';

@GenerateMocks([AuthProvider])
void main() {
  late MockAuthProvider mockAuthProvider;

  setUp(() {
    mockAuthProvider = MockAuthProvider();
    when(mockAuthProvider.state).thenReturn(AuthState.error);
    when(mockAuthProvider.currentUser).thenReturn(null);
    when(mockAuthProvider.errorMessage).thenReturn('Test error');
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: ChangeNotifierProvider<AuthProvider>.value(
        value: mockAuthProvider,
        child: const RegisterScreen(),
      ),
    );
  }

  group('RegisterScreen Clear Error Tests', () {
    testWidgets('should clear error when typing in display name field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Start typing in display name field (first field)
      await tester.enterText(find.byType(TextFormField).at(0), 'J');
      await tester.pump();

      // Assert
      verify(mockAuthProvider.clearError()).called(1);
    });

    testWidgets('should clear error when typing in email field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Start typing in email field (second field)
      await tester.enterText(find.byType(TextFormField).at(1), 'test@');
      await tester.pump();

      // Assert
      verify(mockAuthProvider.clearError()).called(1);
    });

    testWidgets('should clear error when typing in password field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Start typing in password field (third field)
      await tester.enterText(find.byType(TextFormField).at(2), 'p');
      await tester.pump();

      // Assert
      verify(mockAuthProvider.clearError()).called(1);
    });

    testWidgets('should clear error when typing in confirm password field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Start typing in confirm password field (fourth field)
      await tester.enterText(find.byType(TextFormField).at(3), 'p');
      await tester.pump();

      // Assert
      verify(mockAuthProvider.clearError()).called(1);
    });
  });
}