import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/data/models/hazard_model.dart';
import 'package:safestride/domain/entities/hazard.dart';
import 'package:safestride/domain/entities/walkabout.dart';

void main() {
  group('HazardModel Tests', () {
    late Hazard testHazard;
    late DateTime testDateTime;
    late GeoPoint testLocation;

    setUp(() {
      testDateTime = DateTime(2024, 1, 1, 12, 0, 0);
      testLocation = const GeoPoint(latitude: 37.7749, longitude: -122.4194);
      
      testHazard = Hazard(
        id: 'hazard_123',
        walkaboutId: 'walkabout_456',
        title: 'Test Hazard',
        description: 'Test hazard description',
        severity: HazardSeverity.medium,
        category: HazardCategory.slipTripFall,
        location: testLocation,
        photos: ['photo1.jpg', 'photo2.jpg'],
        notes: 'Test notes',
        createdAt: testDateTime,
        updatedAt: testDateTime,
        syncStatus: SyncStatus.local,
      );
    });

    test('should create HazardModel from entity', () {
      final hazardModel = HazardModel.fromEntity(testHazard);

      expect(hazardModel.id, equals(testHazard.id));
      expect(hazardModel.walkaboutId, equals(testHazard.walkaboutId));
      expect(hazardModel.title, equals(testHazard.title));
      expect(hazardModel.description, equals(testHazard.description));
      expect(hazardModel.severity, equals(testHazard.severity));
      expect(hazardModel.category, equals(testHazard.category));
      expect(hazardModel.location, equals(testHazard.location));
      expect(hazardModel.photos, equals(testHazard.photos));
      expect(hazardModel.notes, equals(testHazard.notes));
      expect(hazardModel.createdAt, equals(testHazard.createdAt));
      expect(hazardModel.updatedAt, equals(testHazard.updatedAt));
      expect(hazardModel.syncStatus, equals(testHazard.syncStatus));
    });

    test('should convert to SQLite map correctly', () {
      final hazardModel = HazardModel.fromEntity(testHazard);
      final map = hazardModel.toMap();

      expect(map['id'], equals('hazard_123'));
      expect(map['walkabout_id'], equals('walkabout_456'));
      expect(map['title'], equals('Test Hazard'));
      expect(map['description'], equals('Test hazard description'));
      expect(map['severity'], equals('medium'));
      expect(map['category'], equals('slipTripFall'));
      expect(map['location_lat'], equals(37.7749));
      expect(map['location_lng'], equals(-122.4194));
      expect(map['photos'], equals('["photo1.jpg","photo2.jpg"]'));
      expect(map['notes'], equals('Test notes'));
      expect(map['created_at'], equals(testDateTime.millisecondsSinceEpoch));
      expect(map['updated_at'], equals(testDateTime.millisecondsSinceEpoch));
      expect(map['sync_status'], equals('local'));
    });

    test('should create HazardModel from SQLite map', () {
      final map = {
        'id': 'hazard_123',
        'walkabout_id': 'walkabout_456',
        'title': 'Test Hazard',
        'description': 'Test hazard description',
        'severity': 'medium',
        'category': 'slipTripFall',
        'location_lat': 37.7749,
        'location_lng': -122.4194,
        'photos': '["photo1.jpg","photo2.jpg"]',
        'notes': 'Test notes',
        'created_at': testDateTime.millisecondsSinceEpoch,
        'updated_at': testDateTime.millisecondsSinceEpoch,
        'sync_status': 'local',
      };

      final hazardModel = HazardModel.fromMap(map);

      expect(hazardModel.id, equals('hazard_123'));
      expect(hazardModel.walkaboutId, equals('walkabout_456'));
      expect(hazardModel.title, equals('Test Hazard'));
      expect(hazardModel.description, equals('Test hazard description'));
      expect(hazardModel.severity, equals(HazardSeverity.medium));
      expect(hazardModel.category, equals(HazardCategory.slipTripFall));
      expect(hazardModel.location?.latitude, equals(37.7749));
      expect(hazardModel.location?.longitude, equals(-122.4194));
      expect(hazardModel.photos, equals(['photo1.jpg', 'photo2.jpg']));
      expect(hazardModel.notes, equals('Test notes'));
      expect(hazardModel.createdAt, equals(testDateTime));
      expect(hazardModel.updatedAt, equals(testDateTime));
      expect(hazardModel.syncStatus, equals(SyncStatus.local));
    });

    test('should handle null location in SQLite map', () {
      final map = {
        'id': 'hazard_123',
        'walkabout_id': 'walkabout_456',
        'title': 'Test Hazard',
        'description': null,
        'severity': 'low',
        'category': 'other',
        'location_lat': null,
        'location_lng': null,
        'photos': '[]',
        'notes': null,
        'created_at': testDateTime.millisecondsSinceEpoch,
        'updated_at': testDateTime.millisecondsSinceEpoch,
        'sync_status': 'local',
      };

      final hazardModel = HazardModel.fromMap(map);

      expect(hazardModel.description, isNull);
      expect(hazardModel.location, isNull);
      expect(hazardModel.photos, isEmpty);
      expect(hazardModel.notes, isNull);
    });

    test('should convert to JSON correctly', () {
      final hazardModel = HazardModel.fromEntity(testHazard);
      final json = hazardModel.toJson();

      expect(json['id'], equals('hazard_123'));
      expect(json['walkaboutId'], equals('walkabout_456'));
      expect(json['title'], equals('Test Hazard'));
      expect(json['description'], equals('Test hazard description'));
      expect(json['severity'], equals('medium'));
      expect(json['category'], equals('slipTripFall'));
      expect(json['location']['latitude'], equals(37.7749));
      expect(json['location']['longitude'], equals(-122.4194));
      expect(json['photos'], equals(['photo1.jpg', 'photo2.jpg']));
      expect(json['notes'], equals('Test notes'));
      expect(json['createdAt'], equals(testDateTime.toIso8601String()));
      expect(json['updatedAt'], equals(testDateTime.toIso8601String()));
      expect(json['syncStatus'], equals('local'));
    });

    test('should create HazardModel from JSON', () {
      final json = {
        'id': 'hazard_123',
        'walkaboutId': 'walkabout_456',
        'title': 'Test Hazard',
        'description': 'Test hazard description',
        'severity': 'medium',
        'category': 'slipTripFall',
        'location': {
          'latitude': 37.7749,
          'longitude': -122.4194,
        },
        'photos': ['photo1.jpg', 'photo2.jpg'],
        'notes': 'Test notes',
        'createdAt': testDateTime.toIso8601String(),
        'updatedAt': testDateTime.toIso8601String(),
        'syncStatus': 'local',
      };

      final hazardModel = HazardModel.fromJson(json);

      expect(hazardModel.id, equals('hazard_123'));
      expect(hazardModel.walkaboutId, equals('walkabout_456'));
      expect(hazardModel.title, equals('Test Hazard'));
      expect(hazardModel.description, equals('Test hazard description'));
      expect(hazardModel.severity, equals(HazardSeverity.medium));
      expect(hazardModel.category, equals(HazardCategory.slipTripFall));
      expect(hazardModel.location?.latitude, equals(37.7749));
      expect(hazardModel.location?.longitude, equals(-122.4194));
      expect(hazardModel.photos, equals(['photo1.jpg', 'photo2.jpg']));
      expect(hazardModel.notes, equals('Test notes'));
      expect(hazardModel.createdAt, equals(testDateTime));
      expect(hazardModel.updatedAt, equals(testDateTime));
      expect(hazardModel.syncStatus, equals(SyncStatus.local));
    });

    test('should throw ArgumentError for invalid severity', () {
      expect(
        () => HazardModel.fromMap({
          'id': 'hazard_123',
          'walkabout_id': 'walkabout_456',
          'title': 'Test Hazard',
          'severity': 'invalid',
          'category': 'other',
          'photos': '[]',
          'created_at': testDateTime.millisecondsSinceEpoch,
          'updated_at': testDateTime.millisecondsSinceEpoch,
          'sync_status': 'local',
        }),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('Invalid hazard severity'),
        )),
      );
    });

    test('should throw ArgumentError for invalid category', () {
      expect(
        () => HazardModel.fromMap({
          'id': 'hazard_123',
          'walkabout_id': 'walkabout_456',
          'title': 'Test Hazard',
          'severity': 'low',
          'category': 'invalid',
          'photos': '[]',
          'created_at': testDateTime.millisecondsSinceEpoch,
          'updated_at': testDateTime.millisecondsSinceEpoch,
          'sync_status': 'local',
        }),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('Invalid hazard category'),
        )),
      );
    });

    test('should throw ArgumentError for invalid sync status', () {
      expect(
        () => HazardModel.fromMap({
          'id': 'hazard_123',
          'walkabout_id': 'walkabout_456',
          'title': 'Test Hazard',
          'severity': 'low',
          'category': 'other',
          'photos': '[]',
          'created_at': testDateTime.millisecondsSinceEpoch,
          'updated_at': testDateTime.millisecondsSinceEpoch,
          'sync_status': 'invalid',
        }),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('Invalid sync status'),
        )),
      );
    });
  });
}
