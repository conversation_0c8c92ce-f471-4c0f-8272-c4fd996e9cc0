{"buildFiles": ["D:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\safestride.bmad\\android\\app\\.cxx\\Debug\\3k6s24l4\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\safestride.bmad\\android\\app\\.cxx\\Debug\\3k6s24l4\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}