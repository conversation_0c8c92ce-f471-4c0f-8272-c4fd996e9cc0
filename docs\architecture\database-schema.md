# Database Schema

## Local SQLite Schema

```sql
-- Walkabouts table
CREATE TABLE walkabouts (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    status TEXT NOT NULL,
    location_lat REAL,
    location_lng REAL,
    user_id TEXT NOT NULL,
    is_completed INTEGER NOT NULL DEFAULT 0,
    sync_status TEXT NOT NULL DEFAULT 'local'
);

-- Hazards table
CREATE TABLE hazards (
    id TEXT PRIMARY KEY,
    walkabout_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    severity TEXT NOT NULL,
    category TEXT NOT NULL,
    location_lat REAL,
    location_lng REAL,
    photos TEXT, -- JSON array of photo paths
    notes TEXT,
    created_at INTEGER NOT NULL,
    updated_at INTEGER NOT NULL,
    sync_status TEXT NOT NULL DEFAULT 'local',
    FOREIGN KEY (walkabout_id) REFERENCES walkabouts (id)
);

-- Indexes for performance
CREATE INDEX idx_walkabouts_user_id ON walkabouts(user_id);
CREATE INDEX idx_walkabouts_status ON walkabouts(status);
CREATE INDEX idx_hazards_walkabout_id ON hazards(walkabout_id);
CREATE INDEX idx_hazards_severity ON hazards(severity);
```

## Firestore Schema

```javascript
// Collection: walkabouts
{
  "id": "string",
  "title": "string",
  "description": "string",
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "status": "string",
  "location": {
    "latitude": "number",
    "longitude": "number"
  },
  "userId": "string",
  "isCompleted": "boolean"
}

// Collection: hazards
{
  "id": "string",
  "walkaboutId": "string",
  "title": "string",
  "description": "string",
  "severity": "string",
  "category": "string",
  "location": {
    "latitude": "number",
    "longitude": "number"
  },
  "photoUrls": ["string"],
  "notes": "string",
  "createdAt": "timestamp",
  "updatedAt": "timestamp"
}
```
