import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';
import 'package:safestride/domain/entities/hazard.dart';
import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/presentation/providers/hazard_provider.dart';
import 'package:safestride/presentation/screens/hazard/hazard_documentation_screen.dart';

import 'hazard_documentation_screen_test.mocks.dart';

@GenerateMocks([HazardProvider])
void main() {
  group('HazardDocumentationScreen Widget Tests', () {
    late MockHazardProvider mockHazardProvider;

    setUp(() {
      mockHazardProvider = MockHazardProvider();

      // Setup default mock behavior
      when(mockHazardProvider.isLoading).thenReturn(false);
      when(mockHazardProvider.error).thenReturn(null);
      when(
        mockHazardProvider.selectedSeverity,
      ).thenReturn(HazardSeverity.medium);
      when(
        mockHazardProvider.selectedCategory,
      ).thenReturn(HazardCategory.other);
      when(mockHazardProvider.selectedPhotos).thenReturn([]);
      when(mockHazardProvider.selectedLocation).thenReturn(null);
      when(mockHazardProvider.isVoiceInputActive).thenReturn(false);
      when(mockHazardProvider.voiceInputText).thenReturn('');
    });

    Widget createTestWidget({Hazard? existingHazard}) {
      return MaterialApp(
        home: ChangeNotifierProvider<HazardProvider>.value(
          value: mockHazardProvider,
          child: HazardDocumentationScreen(
            walkaboutId: 'test_walkabout',
            existingHazard: existingHazard,
          ),
        ),
      );
    }

    group('Initial State', () {
      testWidgets('should display correct title for new hazard', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Document Hazard'), findsOneWidget);
        expect(find.text('Save'), findsOneWidget);
      });

      testWidgets('should display correct title for editing hazard', (
        tester,
      ) async {
        // Arrange
        final existingHazard = Hazard(
          id: 'hazard_1',
          walkaboutId: 'test_walkabout',
          title: 'Existing Hazard',
          severity: HazardSeverity.high,
          category: HazardCategory.fire,
          photos: [],
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          syncStatus: SyncStatus.local,
        );

        // Act
        await tester.pumpWidget(
          createTestWidget(existingHazard: existingHazard),
        );

        // Assert
        expect(find.text('Edit Hazard'), findsOneWidget);
        expect(find.text('Update'), findsOneWidget);
      });

      testWidgets('should display all required form fields', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(
          find.byType(TextFormField),
          findsNWidgets(3),
        ); // Title, Description, Notes
        expect(find.text('Hazard Title *'), findsOneWidget);
        expect(find.text('Description'), findsOneWidget);
        expect(find.text('Additional Notes'), findsOneWidget);
        expect(find.text('Severity Level *'), findsOneWidget);
        expect(find.text('Category *'), findsOneWidget);
        expect(find.text('Location'), findsOneWidget);
        expect(find.text('Photos'), findsOneWidget);
      });
    });

    group('Form Validation', () {
      testWidgets('should show validation error for empty title', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Find and tap save button
        final saveButton = find.text('Save Hazard');
        await tester.tap(saveButton);
        await tester.pump();

        // Assert
        expect(find.text('Please enter a hazard title'), findsOneWidget);
      });

      testWidgets('should show validation error for long title', (
        tester,
      ) async {
        // Arrange
        final longTitle = 'a' * 101;

        // Act
        await tester.pumpWidget(createTestWidget());

        // Enter long title
        await tester.enterText(find.byType(TextFormField).first, longTitle);

        // Find and tap save button
        final saveButton = find.text('Save Hazard');
        await tester.tap(saveButton);
        await tester.pump();

        // Assert
        expect(find.text('Title cannot exceed 100 characters'), findsOneWidget);
      });
    });

    group('Severity Selection', () {
      testWidgets('should display all severity options', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        for (final severity in HazardSeverity.values) {
          expect(find.text(severity.displayName), findsOneWidget);
        }
      });

      testWidgets('should update severity when chip is tapped', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Tap on high severity chip
        await tester.tap(find.text('High'));
        await tester.pump();

        // Assert
        verify(
          mockHazardProvider.setSelectedSeverity(HazardSeverity.high),
        ).called(1);
      });
    });

    group('Category Selection', () {
      testWidgets('should display category dropdown', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(
          find.byType(DropdownButtonFormField<HazardCategory>),
          findsOneWidget,
        );
      });

      testWidgets('should update category when dropdown value changes', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Tap dropdown to open it
        await tester.tap(find.byType(DropdownButtonFormField<HazardCategory>));
        await tester.pumpAndSettle();

        // Tap on electrical category
        await tester.tap(find.text('Electrical').last);
        await tester.pumpAndSettle();

        // Assert
        verify(
          mockHazardProvider.setSelectedCategory(HazardCategory.electrical),
        ).called(1);
      });
    });

    group('Photo Management', () {
      testWidgets('should display photo action buttons', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.camera_alt), findsOneWidget);
        expect(find.byIcon(Icons.photo_library), findsOneWidget);
      });

      testWidgets('should call capture photo when camera button is tapped', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Tap camera button
        await tester.tap(find.byIcon(Icons.camera_alt));
        await tester.pump();

        // Assert
        verify(mockHazardProvider.capturePhoto()).called(1);
      });

      testWidgets(
        'should call select from gallery when gallery button is tapped',
        (tester) async {
          // Act
          await tester.pumpWidget(createTestWidget());

          // Tap gallery button
          await tester.tap(find.byIcon(Icons.photo_library));
          await tester.pump();

          // Assert
          verify(mockHazardProvider.selectPhotoFromGallery()).called(1);
        },
      );

      testWidgets('should display empty photo state when no photos', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('No photos added'), findsOneWidget);
        expect(find.byIcon(Icons.add_a_photo), findsOneWidget);
      });
    });

    group('Location Management', () {
      testWidgets('should display get current location button', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Get Current'), findsOneWidget);
        expect(find.byIcon(Icons.my_location), findsOneWidget);
      });

      testWidgets('should call get current location when button is tapped', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Tap get current location button
        await tester.tap(find.text('Get Current'));
        await tester.pump();

        // Assert
        verify(mockHazardProvider.getCurrentLocation()).called(1);
      });

      testWidgets('should display no location message when location is null', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('No location selected'), findsOneWidget);
        expect(find.byIcon(Icons.location_off), findsOneWidget);
      });

      testWidgets('should display location coordinates when location is set', (
        tester,
      ) async {
        // Arrange
        const location = GeoPoint(latitude: 37.7749, longitude: -122.4194);
        when(mockHazardProvider.selectedLocation).thenReturn(location);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.textContaining('Lat: 37.774900'), findsOneWidget);
        expect(find.textContaining('Lng: -122.419400'), findsOneWidget);
        expect(find.byIcon(Icons.location_on), findsOneWidget);
      });
    });

    group('Voice Input', () {
      testWidgets('should display voice input button', (tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.mic_none), findsOneWidget);
      });

      testWidgets('should call start voice input when mic button is tapped', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Find mic button in description field area
        final micButton = find.byIcon(Icons.mic_none);
        await tester.tap(micButton);
        await tester.pump();

        // Assert
        verify(mockHazardProvider.startVoiceInput()).called(1);
      });

      testWidgets('should display active voice input state', (tester) async {
        // Arrange
        when(mockHazardProvider.isVoiceInputActive).thenReturn(true);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byIcon(Icons.mic), findsOneWidget);
        expect(find.byType(CircularProgressIndicator), findsOneWidget);
      });

      testWidgets('should display voice input text when available', (
        tester,
      ) async {
        // Arrange
        const voiceText = 'This is voice input text';
        when(mockHazardProvider.voiceInputText).thenReturn(voiceText);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text(voiceText), findsOneWidget);
        expect(find.text('Voice Input'), findsOneWidget);
        expect(find.text('Use Text'), findsOneWidget);
      });
    });

    group('Error Handling', () {
      testWidgets('should display error message when error occurs', (
        tester,
      ) async {
        // Arrange
        const errorMessage = 'Something went wrong';
        when(mockHazardProvider.error).thenReturn(errorMessage);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text(errorMessage), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });
    });

    group('Loading State', () {
      testWidgets('should display loading overlay when loading', (
        tester,
      ) async {
        // Arrange
        when(mockHazardProvider.isLoading).thenReturn(true);

        // Act
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(1));
      });
    });

    group('Form Actions', () {
      testWidgets('should clear form when clear button is tapped', (
        tester,
      ) async {
        // Act
        await tester.pumpWidget(createTestWidget());

        // Tap clear button
        await tester.tap(find.text('Clear'));
        await tester.pump();

        // Assert
        verify(mockHazardProvider.clearForm()).called(1);
      });

      testWidgets(
        'should call create hazard when save button is tapped with valid form',
        (tester) async {
          // Arrange
          when(
            mockHazardProvider.createHazard(
              walkaboutId: anyNamed('walkaboutId'),
              title: anyNamed('title'),
              description: anyNamed('description'),
              severity: anyNamed('severity'),
              category: anyNamed('category'),
              location: anyNamed('location'),
              photos: anyNamed('photos'),
              notes: anyNamed('notes'),
            ),
          ).thenAnswer(
            (_) async => Hazard(
              id: 'new_hazard',
              walkaboutId: 'test_walkabout',
              title: 'Test Hazard',
              severity: HazardSeverity.medium,
              category: HazardCategory.other,
              photos: [],
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              syncStatus: SyncStatus.local,
            ),
          );

          // Act
          await tester.pumpWidget(createTestWidget());

          // Enter valid title
          await tester.enterText(
            find.byType(TextFormField).first,
            'Test Hazard',
          );

          // Tap save button
          await tester.tap(find.text('Save Hazard'));
          await tester.pump();

          // Assert
          verify(
            mockHazardProvider.createHazard(
              walkaboutId: 'test_walkabout',
              title: 'Test Hazard',
              description: null,
              severity: HazardSeverity.medium,
              category: HazardCategory.other,
              location: null,
              photos: [],
              notes: null,
            ),
          ).called(1);
        },
      );
    });
  });
}
