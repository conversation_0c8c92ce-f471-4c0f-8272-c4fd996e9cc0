import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Use case for logging in a user with SSO (Google)
class LoginWithSSOUseCase {
  final AuthRepository _authRepository;

  const LoginWithSSOUseCase(this._authRepository);

  /// Execute the SSO login process
  /// 
  /// Returns: [User] object if SSO login is successful
  /// Throws: Exception if SSO login fails or is cancelled
  Future<User> call() async {
    try {
      final user = await _authRepository.loginWithSSO();
      
      // Note: Credentials are already cached in the repository implementation
      return user;
    } catch (e) {
      throw Exception('SSO login failed: ${e.toString()}');
    }
  }
}