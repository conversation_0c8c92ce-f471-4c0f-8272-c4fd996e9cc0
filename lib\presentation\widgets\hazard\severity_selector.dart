import 'package:flutter/material.dart';
import '../../../domain/entities/hazard.dart';

/// Widget for selecting hazard severity level
/// 
/// Provides a visual interface for choosing hazard severity with
/// color-coded chips and clear labeling.
class SeveritySelector extends StatelessWidget {
  final HazardSeverity selectedSeverity;
  final ValueChanged<HazardSeverity> onSeverityChanged;
  final bool isRequired;
  final String? label;

  const SeveritySelector({
    super.key,
    required this.selectedSeverity,
    required this.onSeverityChanged,
    this.isRequired = true,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label ?? (isRequired ? 'Severity Level *' : 'Severity Level'),
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8.0),
        Wrap(
          spacing: 8.0,
          runSpacing: 4.0,
          children: HazardSeverity.values.map((severity) {
            final isSelected = selectedSeverity == severity;
            return ChoiceChip(
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: Color(severity.colorValue),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 6),
                  Text(severity.displayName),
                ],
              ),
              selected: isSelected,
              onSelected: (selected) {
                if (selected) {
                  onSeverityChanged(severity);
                }
              },
              backgroundColor: Color(severity.colorValue).withOpacity(0.1),
              selectedColor: Color(severity.colorValue).withOpacity(0.3),
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.black87,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            );
          }).toList(),
        ),
        const SizedBox(height: 4.0),
        Text(
          selectedSeverity.description,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }
}

/// Compact version of severity selector for use in lists
class CompactSeveritySelector extends StatelessWidget {
  final HazardSeverity selectedSeverity;
  final ValueChanged<HazardSeverity> onSeverityChanged;

  const CompactSeveritySelector({
    super.key,
    required this.selectedSeverity,
    required this.onSeverityChanged,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<HazardSeverity>(
      value: selectedSeverity,
      decoration: const InputDecoration(
        labelText: 'Severity',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: HazardSeverity.values.map((severity) {
        return DropdownMenuItem(
          value: severity,
          child: Row(
            children: [
              Container(
                width: 12,
                height: 12,
                decoration: BoxDecoration(
                  color: Color(severity.colorValue),
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 8),
              Text(severity.displayName),
            ],
          ),
        );
      }).toList(),
      onChanged: (severity) {
        if (severity != null) {
          onSeverityChanged(severity);
        }
      },
    );
  }
}

/// Severity indicator badge for display purposes
class SeverityBadge extends StatelessWidget {
  final HazardSeverity severity;
  final bool showLabel;
  final double size;

  const SeverityBadge({
    super.key,
    required this.severity,
    this.showLabel = true,
    this.size = 16,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: showLabel ? 8 : 4,
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: Color(severity.colorValue),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            _getSeverityIcon(severity),
            color: Colors.white,
            size: size,
          ),
          if (showLabel) ...[
            const SizedBox(width: 4),
            Text(
              severity.displayName,
              style: TextStyle(
                color: Colors.white,
                fontSize: size * 0.75,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getSeverityIcon(HazardSeverity severity) {
    switch (severity) {
      case HazardSeverity.low:
        return Icons.info_outline;
      case HazardSeverity.medium:
        return Icons.warning_amber;
      case HazardSeverity.high:
        return Icons.warning;
      case HazardSeverity.critical:
        return Icons.dangerous;
    }
  }
}

/// Severity level explanation widget
class SeverityLegend extends StatelessWidget {
  const SeverityLegend({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Severity Levels',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: 12),
            ...HazardSeverity.values.map((severity) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8.0),
                child: Row(
                  children: [
                    SeverityBadge(severity: severity, showLabel: false),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            severity.displayName,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                          Text(
                            severity.description,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }
}
