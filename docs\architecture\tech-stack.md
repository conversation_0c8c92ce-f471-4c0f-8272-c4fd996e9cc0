# Tech Stack

## Cloud Infrastructure

- **Provider:** Google Cloud Platform (via Firebase)
- **Key Services:** Firestore, Firebase Storage, Firebase Auth, Firebase Analytics
- **Deployment Regions:** Multi-region (automatic via Firebase)

## Technology Stack Table

| Category           | Technology         | Version     | Purpose     | Rationale      |
| :----------------- | :----------------- | :---------- | :---------- | :------------- |
| **Language**       | Dart               | 3.2.0       | Primary development language | Flutter's native language, strong typing, excellent tooling |
| **Framework**      | Flutter            | 3.16.0      | Cross-platform mobile framework | Single codebase for iOS/Android, excellent performance |
| **Backend**        | Firebase           | Latest      | Backend-as-a-Service | Real-time sync, authentication, file storage, analytics |
| **Database**       | Firestore          | Latest      | Cloud NoSQL database | Real-time sync, offline support, scalable |
| **Local Database** | SQLite (sqflite)   | 2.3.0       | Local data persistence | Offline-first capabilities, mature Flutter integration |
| **State Management** | Provider         | 6.1.1       | State management | Reactive UI, dependency injection, Flutter recommended |
| **Authentication** | Firebase Auth      | 4.15.0      | User authentication | Secure, multiple providers, integrates with Firebase |
| **File Storage**   | Firebase Storage   | 11.6.0      | Cloud file storage | Image/document storage, CDN, security rules |
| **Image Handling** | image_picker       | 1.0.4       | Camera/gallery access | Native camera integration |
| **Location**       | geolocator         | 10.1.0      | GPS location services | Accurate location tracking for hazard mapping |
| **Maps**           | google_maps_flutter | 2.5.0      | Map visualization | Interactive maps for hazard locations |
| **Testing**        | flutter_test       | Built-in    | Unit and widget testing | Comprehensive testing framework |
| **Build Tool**     | Flutter CLI        | 3.16.0      | Build and deployment | Standard Flutter toolchain |
| **Analytics**      | Firebase Analytics | 10.7.0      | Usage analytics | User behavior insights, crash reporting |
