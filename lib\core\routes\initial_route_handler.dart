import 'package:flutter/material.dart';
import '../../presentation/providers/auth_provider.dart';
import 'package:provider/provider.dart';
import 'app_routes.dart';

/// A widget that handles the initial route based on authentication state
/// 
/// This widget checks if the user is authenticated and redirects to the appropriate
/// screen. If the user is authenticated, it redirects to the dashboard. If the user
/// is not authenticated, it redirects to the login screen.
class InitialRouteHandler extends StatelessWidget {
  const InitialRouteHandler({super.key});

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    // Show loading while initializing
    if (authProvider.state == AuthState.initial || authProvider.isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    // Redirect based on authentication state
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (authProvider.isAuthenticated) {
        AppRoutes.navigateToDashboard(context);
      } else {
        AppRoutes.navigateToLogin(context);
      }
    });
    
    // Return an empty container while redirecting
    return Container();
  }
}