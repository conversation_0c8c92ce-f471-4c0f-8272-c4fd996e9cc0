import 'dart:async';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/sync_user.dart';
import '../../domain/repositories/user_repository.dart';

/// Service for syncing user profile data
class ProfileSyncService {
  final UserRepository _userRepository;
  final SyncUserUseCase _syncUserUseCase;
  final Connectivity _connectivity;

  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  String? _currentUserId;
  Timer? _syncTimer;

  bool _isConnected = false;
  bool _isSyncing = false;

  // Queue of user IDs that need syncing
  final Set<String> _syncQueue = {};

  ProfileSyncService({
    required UserRepository userRepository,
    required SyncUserUseCase syncUserUseCase,
    Connectivity? connectivity,
  }) : _userRepository = userRepository,
       _syncUserUseCase = syncUserUseCase,
       _connectivity = connectivity ?? Connectivity();

  /// Initialize the sync service
  void initialize(String userId) {
    _currentUserId = userId;
    _setupConnectivityListener();
    _startPeriodicSync();
  }

  /// Add user to sync queue
  Future<void> queueForSync(String userId) async {
    _syncQueue.add(userId);

    // Try to sync immediately if connected
    if (_isConnected && !_isSyncing) {
      _processSyncQueue();
    }
  }

  /// Manually trigger synchronization
  Future<User?> syncNow(String userId) async {
    if (_isSyncing) {
      return null;
    }

    try {
      _isSyncing = true;
      return await _syncUserUseCase.call(userId);
    } catch (e) {
      // Add to queue if sync fails
      _syncQueue.add(userId);
      return null;
    } finally {
      _isSyncing = false;

      // Process queue in case other users need syncing
      if (_isConnected && _syncQueue.isNotEmpty) {
        _processSyncQueue();
      }
    }
  }

  /// Process the sync queue
  Future<void> _processSyncQueue() async {
    if (_isSyncing || _syncQueue.isEmpty || !_isConnected) {
      return;
    }

    _isSyncing = true;

    try {
      // Create a copy of queue to iterate
      final queueCopy = {..._syncQueue};

      for (final userId in queueCopy) {
        try {
          await _syncUserUseCase.call(userId);
          _syncQueue.remove(userId);
        } catch (e) {
          // Keep in queue if sync fails
        }
      }
    } finally {
      _isSyncing = false;

      // If queue still has items and we're connected, try again later
      if (_syncQueue.isNotEmpty && _isConnected) {
        Future.delayed(const Duration(minutes: 5), _processSyncQueue);
      }
    }
  }

  /// Check if user needs sync
  Future<bool> needsSync(String userId) async {
    return _syncQueue.contains(userId) ||
        await _userRepository
            .getCachedUser(userId)
            .then((user) => user?.needsSync ?? false, onError: (_) => false);
  }

  /// Setup connectivity listener
  void _setupConnectivityListener() {
    // Initial connectivity check
    _connectivity.checkConnectivity().then(_updateConnectivityStatus);

    // Listen for connectivity changes
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectivityStatus,
    );
  }

  /// Update connectivity status and process sync queue if needed
  void _updateConnectivityStatus(ConnectivityResult result) {
    final wasConnected = _isConnected;
    _isConnected = result != ConnectivityResult.none;

    // If we just got connected and have items in the queue, process them
    if (!wasConnected && _isConnected && _syncQueue.isNotEmpty) {
      _processSyncQueue();
    }

    // If current user is set, check if it needs sync
    if (_isConnected && _currentUserId != null) {
      _checkCurrentUserSync();
    }
  }

  /// Check if current user needs sync
  Future<void> _checkCurrentUserSync() async {
    if (_currentUserId == null) return;

    try {
      final needsSync = await _userRepository
          .getCachedUser(_currentUserId!)
          .then((user) => user?.needsSync ?? false, onError: (_) => false);

      if (needsSync) {
        _syncQueue.add(_currentUserId!);
        _processSyncQueue();
      }
    } catch (_) {
      // Ignore errors in background checks
    }
  }

  /// Start periodic sync (every 30 minutes)
  void _startPeriodicSync() {
    _syncTimer?.cancel();
    _syncTimer = Timer.periodic(const Duration(minutes: 30), (_) {
      if (_currentUserId != null && _isConnected) {
        _checkCurrentUserSync();
      }
    });
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _syncTimer?.cancel();
    _syncQueue.clear();
  }
}
