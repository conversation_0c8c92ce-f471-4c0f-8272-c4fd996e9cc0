import 'package:flutter/foundation.dart';
import '../../domain/entities/walkabout.dart';
import '../../domain/usecases/create_walkabout.dart';
import '../../domain/repositories/walkabout_repository.dart';

/// Provider for walkabout state management
/// 
/// Handles walkabout creation, retrieval, and state updates
/// following Clean Architecture and Provider pattern principles.
class WalkaboutProvider extends ChangeNotifier {
  final CreateWalkaboutUseCase createWalkaboutUseCase;
  final WalkaboutRepository walkaboutRepository;

  // State variables
  List<Walkabout> _walkabouts = [];
  Walkabout? _currentWalkabout;
  bool _isLoading = false;
  String? _error;
  WalkaboutStatus _selectedStatus = WalkaboutStatus.draft;

  WalkaboutProvider({
    required this.createWalkaboutUseCase,
    required this.walkaboutRepository,
  });

  // Getters
  List<Walkabout> get walkabouts => List.unmodifiable(_walkabouts);
  Walkabout? get currentWalkabout => _currentWalkabout;
  bool get isLoading => _isLoading;
  String? get error => _error;
  WalkaboutStatus get selectedStatus => _selectedStatus;

  /// Get walkabouts filtered by status
  List<Walkabout> getWalkaboutsByStatus(WalkaboutStatus status) {
    return _walkabouts.where((walkabout) => walkabout.status == status).toList();
  }

  /// Get walkabouts count by status
  int getWalkaboutsCountByStatus(WalkaboutStatus status) {
    return _walkabouts.where((walkabout) => walkabout.status == status).length;
  }

  /// Create a new walkabout
  Future<Walkabout?> createWalkabout({
    required String title,
    String? description,
    required String userId,
    GeoPoint? location,
    WalkaboutStatus? status,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final params = CreateWalkaboutParams(
        title: title,
        description: description,
        userId: userId,
        location: location,
        status: status,
      );

      final walkabout = await createWalkaboutUseCase.call(params);
      
      // Add to local list and sort by creation date
      _walkabouts.add(walkabout);
      _walkabouts.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      
      _currentWalkabout = walkabout;
      
      _setLoading(false);
      notifyListeners();
      
      return walkabout;
    } catch (e) {
      _setError('Failed to create walkabout: ${e.toString()}');
      _setLoading(false);
      return null;
    }
  }

  /// Load walkabouts for a specific user
  Future<void> loadWalkabouts(String userId) async {
    _setLoading(true);
    _clearError();

    try {
      final walkabouts = await walkaboutRepository.getWalkaboutsByUserId(userId);
      _walkabouts = walkabouts;
      
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load walkabouts: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// Load walkabouts by status for a specific user
  Future<void> loadWalkaboutsByStatus(String userId, WalkaboutStatus status) async {
    _setLoading(true);
    _clearError();

    try {
      final walkabouts = await walkaboutRepository.getWalkaboutsByStatus(userId, status);
      _walkabouts = walkabouts;
      _selectedStatus = status;
      
      _setLoading(false);
      notifyListeners();
    } catch (e) {
      _setError('Failed to load walkabouts by status: ${e.toString()}');
      _setLoading(false);
    }
  }

  /// Get walkabout by ID
  Future<Walkabout?> getWalkaboutById(String id) async {
    _setLoading(true);
    _clearError();

    try {
      final walkabout = await walkaboutRepository.getWalkaboutById(id);
      _currentWalkabout = walkabout;
      
      _setLoading(false);
      notifyListeners();
      
      return walkabout;
    } catch (e) {
      _setError('Failed to get walkabout: ${e.toString()}');
      _setLoading(false);
      return null;
    }
  }

  /// Update walkabout
  Future<Walkabout?> updateWalkabout(Walkabout walkabout) async {
    _setLoading(true);
    _clearError();

    try {
      final updatedWalkabout = await walkaboutRepository.updateWalkabout(walkabout);
      
      // Update in local list
      final index = _walkabouts.indexWhere((w) => w.id == walkabout.id);
      if (index != -1) {
        _walkabouts[index] = updatedWalkabout;
      }
      
      // Update current walkabout if it's the same
      if (_currentWalkabout?.id == walkabout.id) {
        _currentWalkabout = updatedWalkabout;
      }
      
      _setLoading(false);
      notifyListeners();
      
      return updatedWalkabout;
    } catch (e) {
      _setError('Failed to update walkabout: ${e.toString()}');
      _setLoading(false);
      return null;
    }
  }

  /// Delete walkabout
  Future<bool> deleteWalkabout(String id) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await walkaboutRepository.deleteWalkabout(id);
      
      if (success) {
        // Remove from local list
        _walkabouts.removeWhere((walkabout) => walkabout.id == id);
        
        // Clear current walkabout if it's the deleted one
        if (_currentWalkabout?.id == id) {
          _currentWalkabout = null;
        }
      }
      
      _setLoading(false);
      notifyListeners();
      
      return success;
    } catch (e) {
      _setError('Failed to delete walkabout: ${e.toString()}');
      _setLoading(false);
      return false;
    }
  }

  /// Set current walkabout
  void setCurrentWalkabout(Walkabout? walkabout) {
    _currentWalkabout = walkabout;
    notifyListeners();
  }

  /// Set selected status filter
  void setSelectedStatus(WalkaboutStatus status) {
    _selectedStatus = status;
    notifyListeners();
  }

  /// Clear error
  void clearError() {
    _clearError();
    notifyListeners();
  }

  /// Clear all walkabouts
  void clearWalkabouts() {
    _walkabouts.clear();
    _currentWalkabout = null;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
  }

  void _setError(String error) {
    _error = error;
  }

  void _clearError() {
    _error = null;
  }
}
