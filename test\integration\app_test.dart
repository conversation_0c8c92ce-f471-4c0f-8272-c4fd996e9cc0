import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:safestride/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('SafeStride App Integration Tests', () {
    testWidgets('app launches and displays welcome screen', (tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Verify app title
      expect(find.text('SafeStride'), findsOneWidget);
      
      // Verify welcome message
      expect(find.text('Welcome to SafeStride'), findsOneWidget);
      
      // Verify subtitle
      expect(find.text('Workplace Safety Management'), findsOneWidget);
      
      // Verify security icon
      expect(find.byIcon(Icons.security), findsOneWidget);
    });
  });
}