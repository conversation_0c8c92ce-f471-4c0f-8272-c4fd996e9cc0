import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../domain/entities/walkabout.dart';
import '../../../domain/entities/hazard.dart';
import '../../providers/walkabout_provider.dart';
import '../../providers/hazard_provider.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/hazard/severity_selector.dart';
import '../hazard/hazard_documentation_screen.dart';

/// Screen displaying walkabout details with hazard management
/// 
/// Provides comprehensive view of walkabout information including
/// hazard list, statistics, and documentation capabilities.
class WalkaboutDetailScreen extends StatefulWidget {
  final Walkabout walkabout;

  const WalkaboutDetailScreen({
    super.key,
    required this.walkabout,
  });

  @override
  State<WalkaboutDetailScreen> createState() => _WalkaboutDetailScreenState();
}

class _WalkaboutDetailScreenState extends State<WalkaboutDetailScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadHazards();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _loadHazards() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final hazardProvider = context.read<HazardProvider>();
      hazardProvider.loadHazards(widget.walkabout.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.walkabout.title),
        actions: [
          IconButton(
            onPressed: _showWalkaboutActions,
            icon: const Icon(Icons.more_vert),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Overview', icon: Icon(Icons.dashboard)),
            Tab(text: 'Hazards', icon: Icon(Icons.warning)),
            Tab(text: 'Details', icon: Icon(Icons.info)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(),
          _buildHazardsTab(),
          _buildDetailsTab(),
        ],
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  Widget _buildOverviewTab() {
    return Consumer<HazardProvider>(
      builder: (context, hazardProvider, child) {
        final hazards = hazardProvider.getHazardsByWalkabout(widget.walkabout.id);
        final statistics = hazardProvider.getHazardStatistics(widget.walkabout.id);

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Walkabout status card
              _buildStatusCard(),
              const SizedBox(height: 16.0),

              // Hazard statistics
              _buildHazardStatistics(statistics),
              const SizedBox(height: 16.0),

              // Recent hazards
              if (hazards.isNotEmpty) ...[
                _buildRecentHazards(hazards),
                const SizedBox(height: 16.0),
              ],

              // Quick actions
              _buildQuickActions(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHazardsTab() {
    return Consumer<HazardProvider>(
      builder: (context, hazardProvider, child) {
        final hazards = hazardProvider.getHazardsByWalkabout(widget.walkabout.id);

        return LoadingOverlay(
          isLoading: hazardProvider.isLoading,
          child: Column(
            children: [
              // Hazard filters
              _buildHazardFilters(hazardProvider),
              
              // Hazard list
              Expanded(
                child: hazards.isEmpty
                    ? _buildEmptyHazardState()
                    : ListView.builder(
                        padding: const EdgeInsets.all(16.0),
                        itemCount: hazards.length,
                        itemBuilder: (context, index) {
                          final hazard = hazards[index];
                          return _buildHazardCard(hazard);
                        },
                      ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWalkaboutInfo(),
          const SizedBox(height: 16.0),
          _buildLocationInfo(),
          const SizedBox(height: 16.0),
          _buildTimestamps(),
        ],
      ),
    );
  }

  Widget _buildStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Status',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                Chip(
                  label: Text(widget.walkabout.status.displayName),
                  backgroundColor: _getStatusColor(widget.walkabout.status),
                ),
              ],
            ),
            if (widget.walkabout.description != null) ...[
              const SizedBox(height: 8.0),
              Text(widget.walkabout.description!),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHazardStatistics(Map<String, dynamic> statistics) {
    final total = statistics['total'] as int;
    final bySeverity = statistics['bySeverity'] as Map<HazardSeverity, int>;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Hazard Summary',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem('Total', total.toString(), Colors.blue),
                ...HazardSeverity.values.map((severity) {
                  final count = bySeverity[severity] ?? 0;
                  return _buildStatItem(
                    severity.displayName,
                    count.toString(),
                    Color(severity.colorValue),
                  );
                }).toList(),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(8.0),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8.0),
          ),
          child: Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ),
        const SizedBox(height: 4.0),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildRecentHazards(List<Hazard> hazards) {
    final recentHazards = hazards.take(3).toList();

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Hazards',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                TextButton(
                  onPressed: () => _tabController.animateTo(1),
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            ...recentHazards.map((hazard) => _buildCompactHazardTile(hazard)).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactHazardTile(Hazard hazard) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      leading: SeverityBadge(severity: hazard.severity, showLabel: false),
      title: Text(hazard.title),
      subtitle: Text(hazard.category.displayName),
      trailing: Text(
        '${hazard.photos.length} photos',
        style: const TextStyle(fontSize: 12),
      ),
      onTap: () => _editHazard(hazard),
    );
  }

  Widget _buildQuickActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12.0),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _documentNewHazard,
                    icon: const Icon(Icons.add),
                    label: const Text('Add Hazard'),
                  ),
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _generateReport,
                    icon: const Icon(Icons.description),
                    label: const Text('Generate Report'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHazardFilters(HazardProvider provider) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'Search hazards...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (query) {
                // TODO: Implement search functionality
              },
            ),
          ),
          const SizedBox(width: 8.0),
          IconButton(
            onPressed: _showFilterOptions,
            icon: const Icon(Icons.filter_list),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyHazardState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.warning_amber_outlined,
            size: 64,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16.0),
          Text(
            'No hazards documented yet',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            'Start documenting hazards to improve safety',
            style: TextStyle(
              color: Colors.grey.shade500,
            ),
          ),
          const SizedBox(height: 24.0),
          ElevatedButton.icon(
            onPressed: _documentNewHazard,
            icon: const Icon(Icons.add),
            label: const Text('Document First Hazard'),
          ),
        ],
      ),
    );
  }

  Widget _buildHazardCard(Hazard hazard) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8.0),
      child: ListTile(
        leading: SeverityBadge(severity: hazard.severity),
        title: Text(hazard.title),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(hazard.category.displayName),
            if (hazard.description != null)
              Text(
                hazard.description!,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(fontSize: 12),
              ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (hazard.photos.isNotEmpty)
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.photo_camera, size: 16),
                  const SizedBox(width: 4),
                  Text('${hazard.photos.length}'),
                ],
              ),
            if (hazard.location != null)
              const Icon(Icons.location_on, size: 16, color: Colors.green),
          ],
        ),
        onTap: () => _editHazard(hazard),
      ),
    );
  }

  Widget _buildWalkaboutInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Walkabout Information',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12.0),
            _buildInfoRow('Title', widget.walkabout.title),
            if (widget.walkabout.description != null)
              _buildInfoRow('Description', widget.walkabout.description!),
            _buildInfoRow('Status', widget.walkabout.status.displayName),
            _buildInfoRow('Sync Status', widget.walkabout.syncStatus.displayName),
          ],
        ),
      ),
    );
  }

  Widget _buildLocationInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Location Information',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12.0),
            if (widget.walkabout.location != null) ...[
              _buildInfoRow(
                'Coordinates',
                '${widget.walkabout.location!.latitude.toStringAsFixed(6)}, '
                '${widget.walkabout.location!.longitude.toStringAsFixed(6)}',
              ),
            ] else
              const Text('No location information available'),
          ],
        ),
      ),
    );
  }

  Widget _buildTimestamps() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Timestamps',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 12.0),
            _buildInfoRow('Created', _formatDateTime(widget.walkabout.createdAt)),
            _buildInfoRow('Updated', _formatDateTime(widget.walkabout.updatedAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _documentNewHazard,
      tooltip: 'Document Hazard',
      child: const Icon(Icons.add),
    );
  }

  Color _getStatusColor(WalkaboutStatus status) {
    switch (status) {
      case WalkaboutStatus.draft:
        return Colors.grey.shade200;
      case WalkaboutStatus.inProgress:
        return Colors.blue.shade200;
      case WalkaboutStatus.completed:
        return Colors.green.shade200;
      case WalkaboutStatus.archived:
        return Colors.orange.shade200;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} '
        '${dateTime.hour.toString().padLeft(2, '0')}:'
        '${dateTime.minute.toString().padLeft(2, '0')}';
  }

  // Event handlers
  void _documentNewHazard() async {
    final result = await Navigator.of(context).push<Hazard>(
      MaterialPageRoute(
        builder: (context) => HazardDocumentationScreen(
          walkaboutId: widget.walkabout.id,
        ),
      ),
    );

    if (result != null) {
      _loadHazards();
    }
  }

  void _editHazard(Hazard hazard) async {
    final result = await Navigator.of(context).push<Hazard>(
      MaterialPageRoute(
        builder: (context) => HazardDocumentationScreen(
          walkaboutId: widget.walkabout.id,
          existingHazard: hazard,
        ),
      ),
    );

    if (result != null) {
      _loadHazards();
    }
  }

  void _showWalkaboutActions() {
    // TODO: Implement walkabout actions menu
  }

  void _showFilterOptions() {
    // TODO: Implement filter options
  }

  void _generateReport() {
    // TODO: Implement report generation
  }
}
