import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../domain/entities/user.dart';
import '../../providers/user_profile_provider.dart';
import '../../widgets/app_bar_widget.dart';
import '../../widgets/loading_indicator.dart';

class ProfileEditScreen extends StatefulWidget {
  const ProfileEditScreen({Key? key}) : super(key: key);

  @override
  State<ProfileEditScreen> createState() => _ProfileEditScreenState();
}

class _ProfileEditScreenState extends State<ProfileEditScreen> {
  final _formKey = GlobalKey<FormState>();
  final _displayNameController = TextEditingController();
  final _organizationController = TextEditingController();
  
  // Preferences
  bool _notificationsEnabled = true;
  String _selectedLanguage = 'English';
  String _selectedTheme = 'Light';
  bool _offlineMode = false;

  final List<String> _languages = ['English', 'Spanish', 'French', 'German'];
  final List<String> _themes = ['Light', 'Dark', 'System'];

  @override
  void initState() {
    super.initState();
    // Initialize form with current user data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeFormData();
    });
  }

  void _initializeFormData() {
    final userProfileProvider = Provider.of<UserProfileProvider>(context, listen: false);
    final user = userProfileProvider.user;
    
    if (user != null) {
      _displayNameController.text = user.displayName ?? '';
      _organizationController.text = user.organization ?? '';
      
      // Initialize preferences
      setState(() {
        _notificationsEnabled = user.preferences.notificationsEnabled;
        _selectedLanguage = user.preferences.language;
        _selectedTheme = user.preferences.theme;
        _offlineMode = user.preferences.offlineMode;
      });
    }
  }

  @override
  void dispose() {
    _displayNameController.dispose();
    _organizationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Edit Profile')),
      body: Consumer<UserProfileProvider>(
        builder: (context, userProfileProvider, _) {
          if (userProfileProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          final user = userProfileProvider.user;
          if (user == null) {
            return const Center(child: Text('User not found'));
          }

          return _buildForm(context, userProfileProvider);
        },
      ),
    );
  }

  Widget _buildForm(BuildContext context, UserProfileProvider userProfileProvider) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileSection(),
            const SizedBox(height: 24),
            _buildPreferencesSection(),
            const SizedBox(height: 24),
            if (userProfileProvider.errorMessage != null)
              Padding(
                padding: const EdgeInsets.only(bottom: 16.0),
                child: Text(
                  userProfileProvider.errorMessage!,
                  style: const TextStyle(color: Colors.red),
                ),
              ),
            _buildActionButtons(context, userProfileProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Basic Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            const SizedBox(height: 16),
            TextFormField(
              controller: _displayNameController,
              decoration: const InputDecoration(
                labelText: 'Display Name',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your display name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _organizationController,
              decoration: const InputDecoration(
                labelText: 'Organization',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.business),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your organization';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Preferences',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('Notifications'),
              subtitle: Text(_notificationsEnabled ? 'Enabled' : 'Disabled'),
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
              secondary: Icon(
                _notificationsEnabled ? Icons.notifications_active : Icons.notifications_off,
                color: Theme.of(context).primaryColor,
              ),
            ),
            const Divider(),
            ListTile(
              leading: Icon(Icons.language, color: Theme.of(context).primaryColor),
              title: const Text('Language'),
              trailing: DropdownButton<String>(
                value: _selectedLanguage,
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    setState(() {
                      _selectedLanguage = newValue;
                    });
                  }
                },
                items: _languages.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ),
            const Divider(),
            ListTile(
              leading: Icon(Icons.palette, color: Theme.of(context).primaryColor),
              title: const Text('Theme'),
              trailing: DropdownButton<String>(
                value: _selectedTheme,
                onChanged: (String? newValue) {
                  if (newValue != null) {
                    setState(() {
                      _selectedTheme = newValue;
                    });
                  }
                },
                items: _themes.map<DropdownMenuItem<String>>((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
              ),
            ),
            const Divider(),
            SwitchListTile(
              title: const Text('Offline Mode'),
              subtitle: Text(_offlineMode ? 'Enabled' : 'Disabled'),
              value: _offlineMode,
              onChanged: (value) {
                setState(() {
                  _offlineMode = value;
                });
              },
              secondary: Icon(
                _offlineMode ? Icons.cloud_off : Icons.cloud_done,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, UserProfileProvider userProfileProvider) {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: userProfileProvider.isLoading
                ? null
                : () => _saveProfile(context, userProfileProvider),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: userProfileProvider.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ),
      ],
    );
  }

  Future<void> _saveProfile(BuildContext context, UserProfileProvider userProfileProvider) async {
    if (_formKey.currentState!.validate()) {
      // Save profile information
      await userProfileProvider.updateProfile(
        displayName: _displayNameController.text,
        organization: _organizationController.text,
      );

      // Save preferences
      await userProfileProvider.updatePreferences(
        notificationsEnabled: _notificationsEnabled,
        language: _selectedLanguage,
        theme: _selectedTheme,
        offlineMode: _offlineMode,
      );

      if (userProfileProvider.state != UserProfileState.error && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Profile updated successfully')),
        );
        Navigator.pop(context);
      }
    }
  }
}