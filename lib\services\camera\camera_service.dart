import 'dart:io';
import 'package:image_picker/image_picker.dart';
import 'package:flutter/foundation.dart';

/// Service for handling camera operations and photo management
///
/// This service provides functionality for capturing photos, selecting from gallery,
/// compressing images, and managing photo storage for hazard documentation.
abstract class CameraService {
  /// Capture photo from camera
  Future<String?> capturePhoto();

  /// Select photo from gallery
  Future<String?> selectFromGallery();

  /// Select multiple photos from gallery
  Future<List<String>> selectMultipleFromGallery();

  /// Compress image to target size (default 200KB)
  Future<String> compressImage(String imagePath, {int targetSizeKB = 200});

  /// Get image file size in bytes
  Future<int> getImageSize(String imagePath);

  /// Delete image file
  Future<bool> deleteImage(String imagePath);

  /// Check if camera is available
  Future<bool> isCameraAvailable();

  /// Check camera permissions
  Future<bool> checkCameraPermission();

  /// Request camera permissions
  Future<bool> requestCameraPermission();
}

/// Implementation of CameraService using image_picker plugin
class CameraServiceImpl implements CameraService {
  final ImagePicker _picker = ImagePicker();

  @override
  Future<String?> capturePhoto() async {
    try {
      // Check camera availability and permissions
      if (!await isCameraAvailable()) {
        throw CameraException('Camera not available on this device');
      }

      if (!await checkCameraPermission()) {
        final permissionGranted = await requestCameraPermission();
        if (!permissionGranted) {
          throw CameraException('Camera permission denied');
        }
      }

      // Capture photo
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (photo == null) {
        return null; // User cancelled
      }

      // Compress the image
      final compressedPath = await compressImage(photo.path);

      // Delete original if different from compressed
      if (compressedPath != photo.path) {
        await File(photo.path).delete();
      }

      return compressedPath;
    } catch (e) {
      throw CameraException('Failed to capture photo: ${e.toString()}');
    }
  }

  @override
  Future<String?> selectFromGallery() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (photo == null) {
        return null; // User cancelled
      }

      // Compress the image
      final compressedPath = await compressImage(photo.path);

      // Delete original if different from compressed
      if (compressedPath != photo.path) {
        await File(photo.path).delete();
      }

      return compressedPath;
    } catch (e) {
      throw CameraException(
        'Failed to select photo from gallery: ${e.toString()}',
      );
    }
  }

  @override
  Future<List<String>> selectMultipleFromGallery() async {
    try {
      final List<XFile> photos = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (photos.isEmpty) {
        return []; // User cancelled or no photos selected
      }

      final List<String> compressedPaths = [];

      for (final photo in photos) {
        try {
          // Compress each image
          final compressedPath = await compressImage(photo.path);
          compressedPaths.add(compressedPath);

          // Delete original if different from compressed
          if (compressedPath != photo.path) {
            await File(photo.path).delete();
          }
        } catch (e) {
          // Log error but continue with other photos
          debugPrint('Failed to compress photo ${photo.path}: $e');
        }
      }

      return compressedPaths;
    } catch (e) {
      throw CameraException(
        'Failed to select multiple photos: ${e.toString()}',
      );
    }
  }

  @override
  Future<String> compressImage(
    String imagePath, {
    int targetSizeKB = 200,
  }) async {
    try {
      final File imageFile = File(imagePath);

      // Check if file exists
      if (!await imageFile.exists()) {
        throw CameraException('Image file not found: $imagePath');
      }

      // Get current file size
      final int currentSize = await imageFile.length();
      final int targetSizeBytes = targetSizeKB * 1024;

      // If already smaller than target, return original path
      if (currentSize <= targetSizeBytes) {
        return imagePath;
      }

      // For now, just return the original path
      // TODO: Implement proper image compression using image package
      debugPrint(
        'Image compression needed but not implemented yet. Size: ${currentSize}B, Target: ${targetSizeBytes}B',
      );

      return imagePath;
    } catch (e) {
      throw CameraException('Failed to compress image: ${e.toString()}');
    }
  }

  @override
  Future<int> getImageSize(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      if (!await imageFile.exists()) {
        throw CameraException('Image file not found: $imagePath');
      }
      return await imageFile.length();
    } catch (e) {
      throw CameraException('Failed to get image size: ${e.toString()}');
    }
  }

  @override
  Future<bool> deleteImage(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      if (await imageFile.exists()) {
        await imageFile.delete();
        return true;
      }
      return false;
    } catch (e) {
      throw CameraException('Failed to delete image: ${e.toString()}');
    }
  }

  @override
  Future<bool> isCameraAvailable() async {
    try {
      // For now, assume camera is available on mobile devices
      // In a real implementation, you might check platform capabilities
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> checkCameraPermission() async {
    // Note: image_picker handles permissions internally
    // This is a placeholder for more advanced permission checking
    return true;
  }

  @override
  Future<bool> requestCameraPermission() async {
    // Note: image_picker handles permission requests internally
    // This is a placeholder for more advanced permission handling
    return true;
  }
}

/// Exception thrown by camera operations
class CameraException implements Exception {
  final String message;

  const CameraException(this.message);

  @override
  String toString() => 'CameraException: $message';
}
