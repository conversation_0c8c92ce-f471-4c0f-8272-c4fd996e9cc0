import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/user_repository.dart';
import 'package:safestride/domain/usecases/update_user_preferences.dart';

import 'update_user_preferences_test.mocks.dart';

@GenerateMocks([UserRepository])
void main() {
  late UpdateUserPreferencesUseCase useCase;
  late MockUserRepository mockUserRepository;

  setUp(() {
    mockUserRepository = MockUserRepository();
    useCase = UpdateUserPreferencesUseCase(mockUserRepository);
  });

  group('UpdateUserPreferencesUseCase', () {
    const testUid = 'test-uid-123';
    final testUser = User(
      id: testUid,
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime(2024, 1, 1),
      lastLoginAt: DateTime(2024, 1, 2),
      preferences: UserPreferences(),
    );

    final newPreferences = UserPreferences(
      notificationsEnabled: false,
      language: 'es',
      theme: 'dark',
      offlineMode: true,
    );

    final updatedUser = testUser.copyWith(
      preferences: newPreferences,
      updatedAt: DateTime(2024, 1, 3),
    );

    test('should return updated user when repository call succeeds', () async {
      // Arrange
      when(mockUserRepository.updateUserPreferences(testUid, newPreferences))
          .thenAnswer((_) async => updatedUser);

      // Act
      final result = await useCase(
        uid: testUid,
        preferences: newPreferences,
      );

      // Assert
      expect(result, equals(updatedUser));
      expect(result.preferences, equals(newPreferences));
      verify(mockUserRepository.updateUserPreferences(testUid, newPreferences)).called(1);
    });

    test('should throw exception when repository throws exception', () async {
      // Arrange
      when(mockUserRepository.updateUserPreferences(testUid, newPreferences))
          .thenThrow(Exception('Repository error'));

      // Act & Assert
      expect(
        () async => await useCase(
          uid: testUid,
          preferences: newPreferences,
        ),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Failed to update user preferences'),
        )),
      );
      verify(mockUserRepository.updateUserPreferences(testUid, newPreferences)).called(1);
    });

    test('should handle default preferences', () async {
      // Arrange
      final defaultPreferences = UserPreferences();
      final userWithDefaultPrefs = testUser.copyWith(
        preferences: defaultPreferences,
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.updateUserPreferences(testUid, defaultPreferences))
          .thenAnswer((_) async => userWithDefaultPrefs);

      // Act
      final result = await useCase(
        uid: testUid,
        preferences: defaultPreferences,
      );

      // Assert
      expect(result, equals(userWithDefaultPrefs));
      expect(result.preferences.notificationsEnabled, isTrue);
      expect(result.preferences.language, equals('en'));
      expect(result.preferences.theme, equals('light'));
      expect(result.preferences.offlineMode, isFalse);
      verify(mockUserRepository.updateUserPreferences(testUid, defaultPreferences)).called(1);
    });

    test('should handle partial preference updates', () async {
      // Arrange
      final partialPreferences = UserPreferences(
        notificationsEnabled: false,
        // Other fields use defaults
      );
      final userWithPartialPrefs = testUser.copyWith(
        preferences: partialPreferences,
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.updateUserPreferences(testUid, partialPreferences))
          .thenAnswer((_) async => userWithPartialPrefs);

      // Act
      final result = await useCase(
        uid: testUid,
        preferences: partialPreferences,
      );

      // Assert
      expect(result, equals(userWithPartialPrefs));
      expect(result.preferences.notificationsEnabled, isFalse);
      expect(result.preferences.language, equals('en')); // Default
      verify(mockUserRepository.updateUserPreferences(testUid, partialPreferences)).called(1);
    });

    test('should handle empty uid parameter', () async {
      // Arrange
      const emptyUid = '';
      when(mockUserRepository.updateUserPreferences(emptyUid, newPreferences))
          .thenThrow(Exception('User not found'));

      // Act & Assert
      expect(
        () async => await useCase(
          uid: emptyUid,
          preferences: newPreferences,
        ),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Failed to update user preferences'),
        )),
      );
      verify(mockUserRepository.updateUserPreferences(emptyUid, newPreferences)).called(1);
    });

    test('should validate all supported language options', () async {
      // Arrange
      for (final language in UserPreferences.languageOptions) {
        final languagePreferences = UserPreferences(language: language);
        final userWithLanguage = testUser.copyWith(
          preferences: languagePreferences,
          updatedAt: DateTime(2024, 1, 3),
        );
        
        when(mockUserRepository.updateUserPreferences(testUid, languagePreferences))
            .thenAnswer((_) async => userWithLanguage);

        // Act
        final result = await useCase(
          uid: testUid,
          preferences: languagePreferences,
        );

        // Assert
        expect(result.preferences.language, equals(language));
      }
    });

    test('should validate all supported theme options', () async {
      // Arrange
      for (final theme in UserPreferences.themeOptions) {
        final themePreferences = UserPreferences(theme: theme);
        final userWithTheme = testUser.copyWith(
          preferences: themePreferences,
          updatedAt: DateTime(2024, 1, 3),
        );
        
        when(mockUserRepository.updateUserPreferences(testUid, themePreferences))
            .thenAnswer((_) async => userWithTheme);

        // Act
        final result = await useCase(
          uid: testUid,
          preferences: themePreferences,
        );

        // Assert
        expect(result.preferences.theme, equals(theme));
      }
    });
  });
}