import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/repositories/auth_repository.dart';
import 'package:safestride/domain/usecases/reset_password.dart';

import 'reset_password_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  late ResetPasswordUseCase useCase;
  late MockAuthRepository mockAuthRepository;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    useCase = ResetPasswordUseCase(mockAuthRepository);
  });

  group('ResetPasswordUseCase', () {
    const validEmail = '<EMAIL>';

    test('should send reset password email successfully', () async {
      // Arrange
      when(mockAuthRepository.resetPassword(email: anyNamed('email')))
          .thenAnswer((_) async => {});

      // Act
      await useCase.call(email: validEmail);

      // Assert
      verify(mockAuthRepository.resetPassword(
        email: validEmail.toLowerCase(),
      )).called(1);
    });

    test('should normalize email to lowercase', () async {
      // Arrange
      const upperCaseEmail = '<EMAIL>';
      when(mockAuthRepository.resetPassword(email: anyNamed('email')))
          .thenAnswer((_) async => {});

      // Act
      await useCase.call(email: upperCaseEmail);

      // Assert
      verify(mockAuthRepository.resetPassword(
        email: upperCaseEmail.toLowerCase(),
      )).called(1);
    });

    test('should trim whitespace from email', () async {
      // Arrange
      const emailWithSpaces = '  <EMAIL>  ';
      when(mockAuthRepository.resetPassword(email: anyNamed('email')))
          .thenAnswer((_) async => {});

      // Act
      await useCase.call(email: emailWithSpaces);

      // Assert
      verify(mockAuthRepository.resetPassword(
        email: emailWithSpaces.trim().toLowerCase(),
      )).called(1);
    });

    test('should throw ArgumentError for invalid email format', () async {
      // Arrange
      const invalidEmail = 'invalid-email';

      // Act & Assert
      expect(
        () => useCase.call(email: invalidEmail),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          'Invalid email format',
        )),
      );
      verifyNever(mockAuthRepository.resetPassword(email: anyNamed('email')));
    });

    test('should throw ArgumentError for empty email', () async {
      // Arrange
      const emptyEmail = '';

      // Act & Assert
      expect(
        () => useCase.call(email: emptyEmail),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          'Invalid email format',
        )),
      );
      verifyNever(mockAuthRepository.resetPassword(email: anyNamed('email')));
    });

    test('should throw ArgumentError for whitespace-only email', () async {
      // Arrange
      const whitespaceEmail = '   ';

      // Act & Assert
      expect(
        () => useCase.call(email: whitespaceEmail),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          'Invalid email format',
        )),
      );
      verifyNever(mockAuthRepository.resetPassword(email: anyNamed('email')));
    });

    test('should throw exception when reset password fails', () async {
      // Arrange
      const errorMessage = 'Reset password failed';
      when(mockAuthRepository.resetPassword(email: anyNamed('email')))
          .thenThrow(Exception(errorMessage));

      // Act & Assert
      expect(
        () => useCase.call(email: validEmail),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Password reset failed: Exception: $errorMessage'),
        )),
      );
      verify(mockAuthRepository.resetPassword(
        email: validEmail.toLowerCase(),
      )).called(1);
    });

    test('should throw exception when user not found', () async {
      // Arrange
      const errorMessage = 'User not found';
      when(mockAuthRepository.resetPassword(email: anyNamed('email')))
          .thenThrow(Exception(errorMessage));

      // Act & Assert
      expect(
        () => useCase.call(email: validEmail),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Password reset failed: Exception: $errorMessage'),
        )),
      );
      verify(mockAuthRepository.resetPassword(
        email: validEmail.toLowerCase(),
      )).called(1);
    });

    test('should throw exception when network error occurs', () async {
      // Arrange
      const errorMessage = 'Network error';
      when(mockAuthRepository.resetPassword(email: anyNamed('email')))
          .thenThrow(Exception(errorMessage));

      // Act & Assert
      expect(
        () => useCase.call(email: validEmail),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Password reset failed: Exception: $errorMessage'),
        )),
      );
      verify(mockAuthRepository.resetPassword(
        email: validEmail.toLowerCase(),
      )).called(1);
    });
  });
}