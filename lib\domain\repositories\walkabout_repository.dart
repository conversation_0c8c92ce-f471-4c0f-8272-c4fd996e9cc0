import '../entities/walkabout.dart';

/// Repository interface for walkabout data operations
/// 
/// This interface defines the contract for walkabout data access
/// following Clean Architecture principles. Implementations handle
/// the actual data persistence and retrieval logic.
abstract class WalkaboutRepository {
  /// Create a new walkabout
  /// 
  /// Returns the created walkabout with generated ID and timestamps
  /// Throws [Exception] if creation fails
  Future<Walkabout> createWalkabout(Walkabout walkabout);

  /// Get walkabout by ID
  /// 
  /// Returns the walkabout if found, null otherwise
  /// Throws [Exception] if retrieval fails
  Future<Walkabout?> getWalkaboutById(String id);

  /// Get all walkabouts for a specific user
  /// 
  /// Returns list of walkabouts ordered by creation date (newest first)
  /// Returns empty list if no walkabouts found
  /// Throws [Exception] if retrieval fails
  Future<List<Walkabout>> getWalkaboutsByUserId(String userId);

  /// Get walkabouts by status for a specific user
  /// 
  /// Returns list of walkabouts with the specified status
  /// Returns empty list if no walkabouts found
  /// Throws [Exception] if retrieval fails
  Future<List<Walkabout>> getWalkaboutsByStatus(String userId, WalkaboutStatus status);

  /// Update an existing walkabout
  /// 
  /// Returns the updated walkabout
  /// Throws [Exception] if update fails or walkabout not found
  Future<Walkabout> updateWalkabout(Walkabout walkabout);

  /// Delete a walkabout by ID
  /// 
  /// Returns true if deletion was successful, false if walkabout not found
  /// Throws [Exception] if deletion fails
  Future<bool> deleteWalkabout(String id);

  /// Get walkabouts that need to be synced
  /// 
  /// Returns list of walkabouts with sync status other than 'synced'
  /// Used for offline synchronization
  /// Returns empty list if no walkabouts need syncing
  /// Throws [Exception] if retrieval fails
  Future<List<Walkabout>> getWalkaboutsToSync();

  /// Update sync status for a walkabout
  /// 
  /// Updates only the sync status field
  /// Returns the updated walkabout
  /// Throws [Exception] if update fails or walkabout not found
  Future<Walkabout> updateSyncStatus(String id, SyncStatus syncStatus);
}
