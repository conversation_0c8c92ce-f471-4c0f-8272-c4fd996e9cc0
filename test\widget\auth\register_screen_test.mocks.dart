// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in safestride/test/widget/auth/register_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:ui' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/user.dart' as _i4;
import 'package:safestride/presentation/providers/auth_provider.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [AuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthProvider extends _i1.Mock implements _i2.AuthProvider {
  MockAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.AuthState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _i2.AuthState.initial,
          )
          as _i2.AuthState);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  bool get isAuthenticated =>
      (super.noSuchMethod(
            Invocation.getter(#isAuthenticated),
            returnValue: false,
          )
          as bool);

  @override
  bool get isAdmin =>
      (super.noSuchMethod(Invocation.getter(#isAdmin), returnValue: false)
          as bool);

  @override
  bool get isInspector =>
      (super.noSuchMethod(Invocation.getter(#isInspector), returnValue: false)
          as bool);

  @override
  bool get isManager =>
      (super.noSuchMethod(Invocation.getter(#isManager), returnValue: false)
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i3.Future<void> registerWithEmail({
    required String? email,
    required String? password,
    String? displayName,
    String? organization,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerWithEmail, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
              #organization: organization,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> loginWithEmail({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginWithEmail, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> loginWithSSO() =>
      (super.noSuchMethod(
            Invocation.method(#loginWithSSO, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> resetPassword({required String? email}) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [], {#email: email}),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> refreshUser() =>
      (super.noSuchMethod(
            Invocation.method(#refreshUser, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void listenToAuthChanges() => super.noSuchMethod(
    Invocation.method(#listenToAuthChanges, []),
    returnValueForMissingStub: null,
  );

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  bool hasRole(_i4.UserRole? role) =>
      (super.noSuchMethod(
            Invocation.method(#hasRole, [role]),
            returnValue: false,
          )
          as bool);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i5.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i5.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}
