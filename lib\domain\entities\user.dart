import 'package:equatable/equatable.dart';

/// User entity representing an authenticated user in the system
class User extends Equatable {
  final String uid; // Primary identifier
  final String id; // For compatibility with Firestore
  final String email;
  final String? displayName;
  final String? photoUrl;
  final String? organization;
  final UserRole role;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastLoginAt;
  final UserPreferences preferences;
  final bool needsSync;

  const User({
    required this.uid,
    this.id = '',
    required this.email,
    this.displayName,
    this.photoUrl,
    this.organization,
    this.role = UserRole.inspector,
    required this.createdAt,
    required this.updatedAt,
    this.lastLoginAt,
    this.preferences = const UserPreferences(
      notificationsEnabled: true,
      language: 'en',
      theme: 'system',
      offlineMode: false,
    ),
    this.needsSync = false,
  });

  User copyWith({
    String? uid,
    String? id,
    String? email,
    String? displayName,
    String? photoUrl,
    String? organization,
    UserRole? role,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastLoginAt,
    UserPreferences? preferences,
    bool? needsSync,
  }) {
    return User(
      uid: uid ?? this.uid,
      id: id ?? this.id,
      email: email ?? this.email,
      displayName: displayName ?? this.displayName,
      photoUrl: photoUrl ?? this.photoUrl,
      organization: organization ?? this.organization,
      role: role ?? this.role,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
      needsSync: needsSync ?? this.needsSync,
    );
  }

  @override
  List<Object?> get props => [
        uid,
        id,
        email,
        displayName,
        photoUrl,
        organization,
        role,
        createdAt,
        updatedAt,
        lastLoginAt,
        preferences,
        needsSync,
      ];
      
  @override
  String toString() {
    return 'User(uid: $uid, id: $id, email: $email, displayName: $displayName, photoUrl: $photoUrl, organization: $organization, role: $role, createdAt: $createdAt, updatedAt: $updatedAt, lastLoginAt: $lastLoginAt, preferences: $preferences, needsSync: $needsSync)';
  }
}

/// User role enumeration
enum UserRole {
  inspector,
  manager,
  admin,
  moderator;
  
  String get displayName {
    switch (this) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.moderator:
        return 'Moderator';
      case UserRole.manager:
        return 'Manager';
      case UserRole.inspector:
        return 'Inspector';
    }
  }

  String get description {
    switch (this) {
      case UserRole.admin:
        return 'Full access to all features and administrative controls';
      case UserRole.moderator:
        return 'Can moderate content and manage standard users';
      case UserRole.manager:
        return 'Can manage inspections and sites';
      case UserRole.inspector:
        return 'Can perform inspections and view assigned sites';
    }
  }
}

/// User preferences for app settings
class UserPreferences extends Equatable {
  final bool notificationsEnabled;
  final String language;
  final String theme;
  final bool offlineMode;

  const UserPreferences({
    this.notificationsEnabled = true,
    this.language = 'en',
    this.theme = 'system',
    this.offlineMode = false,
  });

  UserPreferences copyWith({
    bool? notificationsEnabled,
    String? language,
    String? theme,
    bool? offlineMode,
  }) {
    return UserPreferences(
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      language: language ?? this.language,
      theme: theme ?? this.theme,
      offlineMode: offlineMode ?? this.offlineMode,
    );
  }

  @override
  List<Object?> get props => [
        notificationsEnabled,
        language,
        theme,
        offlineMode,
      ];

  @override
  String toString() {
    return 'UserPreferences(notificationsEnabled: $notificationsEnabled, language: $language, theme: $theme, offlineMode: $offlineMode)';
  }
  
  /// Available language options
  static List<Map<String, String>> get languageOptions => [
        {'code': 'en', 'name': 'English'},
        {'code': 'es', 'name': 'Spanish'},
        {'code': 'fr', 'name': 'French'},
      ];

  /// Available theme options
  static List<Map<String, String>> get themeOptions => [
        {'code': 'system', 'name': 'System Default'},
        {'code': 'light', 'name': 'Light'},
        {'code': 'dark', 'name': 'Dark'},
      ];
}