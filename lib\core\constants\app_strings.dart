/// Application string constants
class AppStrings {
  // Private constructor to prevent instantiation
  AppStrings._();

  // App information
  static const String appName = 'SafeStride';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Safety inspection and compliance management';

  // Authentication
  static const String signIn = 'Sign In';
  static const String signUp = 'Sign Up';
  static const String signOut = 'Sign Out';
  static const String register = 'Register';
  static const String login = 'Login';
  static const String logout = 'Logout';
  static const String forgotPassword = 'Forgot Password?';
  static const String resetPassword = 'Reset Password';
  static const String changePassword = 'Change Password';
  static const String confirmPassword = 'Confirm Password';
  static const String rememberMe = 'Remember Me';
  static const String continueWithGoogle = 'Continue with Google';
  static const String createAccount = 'Create Account';
  static const String alreadyHaveAccount = 'Already have an account?';
  static const String dontHaveAccount = "Don't have an account?";

  // Form fields
  static const String email = 'Email';
  static const String password = 'Password';
  static const String fullName = 'Full Name';
  static const String displayName = 'Display Name';
  static const String organization = 'Organization';
  static const String phoneNumber = 'Phone Number';
  static const String address = 'Address';
  static const String city = 'City';
  static const String state = 'State';
  static const String zipCode = 'ZIP Code';
  static const String country = 'Country';

  // Validation messages
  static const String fieldRequired = 'This field is required';
  static const String invalidEmail = 'Please enter a valid email address';
  static const String passwordTooShort = 'Password must be at least 8 characters';
  static const String passwordsDoNotMatch = 'Passwords do not match';
  static const String invalidPhoneNumber = 'Please enter a valid phone number';
  static const String nameTooShort = 'Name must be at least 2 characters';
  static const String passwordRequirements = 'Password must contain uppercase, lowercase, and number';

  // Authentication messages
  static const String signInSuccess = 'Signed in successfully';
  static const String signUpSuccess = 'Account created successfully';
  static const String signOutSuccess = 'Signed out successfully';
  static const String passwordResetSent = 'Password reset email sent';
  static const String authenticationFailed = 'Authentication failed';
  static const String invalidCredentials = 'Invalid email or password';
  static const String accountDisabled = 'This account has been disabled';
  static const String emailAlreadyInUse = 'An account already exists for this email';
  static const String weakPassword = 'The password provided is too weak';
  static const String userNotFound = 'No user found for this email';
  static const String wrongPassword = 'Wrong password provided';
  static const String tooManyRequests = 'Too many requests. Try again later';
  static const String networkError = 'Network error. Please check your connection';

  // General actions
  static const String save = 'Save';
  static const String cancel = 'Cancel';
  static const String delete = 'Delete';
  static const String edit = 'Edit';
  static const String update = 'Update';
  static const String create = 'Create';
  static const String add = 'Add';
  static const String remove = 'Remove';
  static const String confirm = 'Confirm';
  static const String submit = 'Submit';
  static const String retry = 'Retry';
  static const String refresh = 'Refresh';
  static const String loading = 'Loading...';
  static const String pleaseWait = 'Please wait...';
  static const String processing = 'Processing...';
  static const String uploading = 'Uploading...';
  static const String downloading = 'Downloading...';

  // Navigation
  static const String home = 'Home';
  static const String profile = 'Profile';
  static const String settings = 'Settings';
  static const String help = 'Help';
  static const String about = 'About';
  static const String back = 'Back';
  static const String next = 'Next';
  static const String previous = 'Previous';
  static const String close = 'Close';
  static const String done = 'Done';

  // User roles
  static const String admin = 'Admin';
  static const String inspector = 'Inspector';
  static const String viewer = 'Viewer';

  // Status
  static const String active = 'Active';
  static const String inactive = 'Inactive';
  static const String pending = 'Pending';
  static const String approved = 'Approved';
  static const String rejected = 'Rejected';
  static const String completed = 'Completed';
  static const String inProgress = 'In Progress';
  static const String failed = 'Failed';

  // Priority
  static const String high = 'High';
  static const String medium = 'Medium';
  static const String low = 'Low';

  // Error messages
  static const String genericError = 'Something went wrong. Please try again.';
  static const String connectionError = 'Connection error. Please check your internet connection.';
  static const String timeoutError = 'Request timeout. Please try again.';
  static const String serverError = 'Server error. Please try again later.';
  static const String notFoundError = 'The requested resource was not found.';
  static const String unauthorizedError = 'You are not authorized to perform this action.';
  static const String forbiddenError = 'Access forbidden.';
  static const String validationError = 'Please check your input and try again.';

  // Success messages
  static const String saveSuccess = 'Saved successfully';
  static const String updateSuccess = 'Updated successfully';
  static const String deleteSuccess = 'Deleted successfully';
  static const String createSuccess = 'Created successfully';
  static const String uploadSuccess = 'Uploaded successfully';
  static const String downloadSuccess = 'Downloaded successfully';

  // Confirmation messages
  static const String confirmDelete = 'Are you sure you want to delete this item?';
  static const String confirmLogout = 'Are you sure you want to sign out?';
  static const String confirmCancel = 'Are you sure you want to cancel? Your changes will be lost.';
  static const String unsavedChanges = 'You have unsaved changes. Do you want to save them?';

  // Permissions
  static const String permissionDenied = 'Permission denied';
  static const String cameraPermissionRequired = 'Camera permission is required';
  static const String locationPermissionRequired = 'Location permission is required';
  static const String storagePermissionRequired = 'Storage permission is required';

  // Offline
  static const String offlineMode = 'Offline Mode';
  static const String noInternetConnection = 'No internet connection';
  static const String syncWhenOnline = 'Data will sync when you\'re back online';
  static const String offlineDataAvailable = 'Offline data available';

  // Terms and Privacy
  static const String termsOfService = 'Terms of Service';
  static const String privacyPolicy = 'Privacy Policy';
  static const String acceptTerms = 'I agree to the Terms of Service and Privacy Policy';

  // Notifications
  static const String notifications = 'Notifications';
  static const String notificationsEnabled = 'Notifications enabled';
  static const String notificationsDisabled = 'Notifications disabled';
  static const String noNotifications = 'No notifications';

  // Theme
  static const String lightTheme = 'Light';
  static const String darkTheme = 'Dark';
  static const String systemTheme = 'System';

  // Language
  static const String language = 'Language';
  static const String english = 'English';
  static const String spanish = 'Spanish';
  static const String french = 'French';

  // Date and time
  static const String today = 'Today';
  static const String yesterday = 'Yesterday';
  static const String tomorrow = 'Tomorrow';
  static const String thisWeek = 'This Week';
  static const String lastWeek = 'Last Week';
  static const String thisMonth = 'This Month';
  static const String lastMonth = 'Last Month';
}