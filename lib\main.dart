import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:get_it/get_it.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'core/routes/app_routes.dart';
import 'data/repositories/auth_repository_impl.dart';
import 'data/repositories/user_repository_impl.dart';
import 'data/datasources/remote/auth_remote_datasource.dart';
import 'data/datasources/remote/user_remote_datasource.dart';
import 'data/datasources/local/auth_local_datasource.dart';
import 'data/datasources/local/user_local_datasource.dart';
import 'data/datasources/local/walkabout_local_datasource.dart';
import 'data/datasources/local/hazard_local_datasource.dart';
import 'data/repositories/walkabout_repository_impl.dart';
import 'data/repositories/hazard_repository_impl.dart';
import 'domain/repositories/auth_repository.dart';
import 'domain/repositories/user_repository.dart';
import 'domain/repositories/walkabout_repository.dart';
import 'domain/usecases/auth_usecases.dart';
import 'domain/usecases/user_usecases.dart';
import 'domain/usecases/create_walkabout.dart';
import 'presentation/providers/auth_provider.dart';
import 'presentation/providers/user_profile_provider.dart';
import 'presentation/providers/walkabout_provider.dart';
import 'services/sync/service_locator.dart';
import 'services/sync/profile_sync_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp();

  // Initialize database
  final database = await openDatabase(
    'safestride.db',
    version: 1,
    onCreate: (db, version) async {
      await AuthLocalDataSourceImpl.createTable(db);
      await UserLocalDataSourceImpl.createTable(db);
      await WalkaboutLocalDataSourceImpl.createTable(db);
      await HazardLocalDataSourceImpl.createTable(db);
    },
  );

  // Initialize service locator
  await initServiceLocator(database: database);

  runApp(MyApp(database: database));
}

class MyApp extends StatelessWidget {
  final Database database;

  const MyApp({super.key, required this.database});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Data sources
        Provider<AuthRemoteDataSource>(
          create: (_) => AuthRemoteDataSourceImpl(),
        ),
        Provider<AuthLocalDataSource>(
          create: (_) => AuthLocalDataSourceImpl(database),
        ),
        Provider<UserRemoteDataSource>(
          create: (_) => UserRemoteDataSourceImpl(),
        ),
        Provider<UserLocalDataSource>(
          create: (_) => UserLocalDataSourceImpl(database),
        ),
        Provider<WalkaboutLocalDataSource>(
          create: (_) => WalkaboutLocalDataSourceImpl(database),
        ),
        // Repositories
        ProxyProvider2<
          AuthRemoteDataSource,
          AuthLocalDataSource,
          AuthRepository
        >(
          update:
              (_, remoteDataSource, localDataSource, __) => AuthRepositoryImpl(
                remoteDataSource: remoteDataSource,
                localDataSource: localDataSource,
              ),
        ),
        ProxyProvider2<
          UserRemoteDataSource,
          UserLocalDataSource,
          UserRepository
        >(
          update:
              (_, remoteDataSource, localDataSource, __) => UserRepositoryImpl(
                remoteDataSource: remoteDataSource,
                localDataSource: localDataSource,
              ),
        ),
        ProxyProvider<WalkaboutLocalDataSource, WalkaboutRepository>(
          update:
              (_, localDataSource, __) =>
                  WalkaboutRepositoryImpl(localDataSource: localDataSource),
        ),
        // Use cases
        ProxyProvider<AuthRepository, LoginWithEmailUseCase>(
          update: (_, repository, __) => LoginWithEmailUseCase(repository),
        ),
        ProxyProvider<AuthRepository, RegisterWithEmailUseCase>(
          update: (_, repository, __) => RegisterWithEmailUseCase(repository),
        ),
        ProxyProvider<AuthRepository, LogoutUseCase>(
          update: (_, repository, __) => LogoutUseCase(repository),
        ),
        ProxyProvider<AuthRepository, GetCurrentUserUseCase>(
          update: (_, repository, __) => GetCurrentUserUseCase(repository),
        ),
        ProxyProvider<AuthRepository, ResetPasswordUseCase>(
          update: (_, repository, __) => ResetPasswordUseCase(repository),
        ),
        ProxyProvider<AuthRepository, LoginWithSSOUseCase>(
          update: (_, repository, __) => LoginWithSSOUseCase(repository),
        ),
        // User use cases
        ProxyProvider<UserRepository, GetUserByIdUseCase>(
          update: (_, repository, __) => GetUserByIdUseCase(repository),
        ),
        ProxyProvider<UserRepository, UpdateUserUseCase>(
          update: (_, repository, __) => UpdateUserUseCase(repository),
        ),
        ProxyProvider<UserRepository, UpdateUserPreferencesUseCase>(
          update:
              (_, repository, __) => UpdateUserPreferencesUseCase(repository),
        ),
        ProxyProvider<UserRepository, UpdateUserRoleUseCase>(
          update: (_, repository, __) => UpdateUserRoleUseCase(repository),
        ),
        ProxyProvider<WalkaboutRepository, CreateWalkaboutUseCase>(
          update:
              (_, repository, __) =>
                  CreateWalkaboutUseCase(repository: repository),
        ),
        ProxyProvider<UserRepository, SyncUserUseCase>(
          update: (_, repository, __) => SyncUserUseCase(repository),
        ),
        // Providers
        ChangeNotifierProxyProvider6<
          LoginWithEmailUseCase,
          RegisterWithEmailUseCase,
          LogoutUseCase,
          GetCurrentUserUseCase,
          ResetPasswordUseCase,
          LoginWithSSOUseCase,
          AuthProvider
        >(
          create:
              (_) => AuthProvider(
                loginWithEmailUseCase: LoginWithEmailUseCase(
                  AuthRepositoryImpl(
                    remoteDataSource: AuthRemoteDataSourceImpl(),
                    localDataSource: AuthLocalDataSourceImpl(database),
                  ),
                ),
                registerWithEmailUseCase: RegisterWithEmailUseCase(
                  AuthRepositoryImpl(
                    remoteDataSource: AuthRemoteDataSourceImpl(),
                    localDataSource: AuthLocalDataSourceImpl(database),
                  ),
                ),
                logoutUseCase: LogoutUseCase(
                  AuthRepositoryImpl(
                    remoteDataSource: AuthRemoteDataSourceImpl(),
                    localDataSource: AuthLocalDataSourceImpl(database),
                  ),
                ),
                getCurrentUserUseCase: GetCurrentUserUseCase(
                  AuthRepositoryImpl(
                    remoteDataSource: AuthRemoteDataSourceImpl(),
                    localDataSource: AuthLocalDataSourceImpl(database),
                  ),
                ),
                resetPasswordUseCase: ResetPasswordUseCase(
                  AuthRepositoryImpl(
                    remoteDataSource: AuthRemoteDataSourceImpl(),
                    localDataSource: AuthLocalDataSourceImpl(database),
                  ),
                ),
                loginWithSSOUseCase: LoginWithSSOUseCase(
                  AuthRepositoryImpl(
                    remoteDataSource: AuthRemoteDataSourceImpl(),
                    localDataSource: AuthLocalDataSourceImpl(database),
                  ),
                ),
              ),
          update:
              (
                _,
                login,
                register,
                logout,
                getCurrentUser,
                resetPassword,
                loginWithSSO,
                __,
              ) => AuthProvider(
                loginWithEmailUseCase: login,
                registerWithEmailUseCase: register,
                logoutUseCase: logout,
                getCurrentUserUseCase: getCurrentUser,
                resetPasswordUseCase: resetPassword,
                loginWithSSOUseCase: loginWithSSO,
              ),
        ),
        // User Profile Provider
        ChangeNotifierProxyProvider5<
          GetUserByIdUseCase,
          UpdateUserUseCase,
          UpdateUserPreferencesUseCase,
          UpdateUserRoleUseCase,
          SyncUserUseCase,
          UserProfileProvider
        >(
          create:
              (_) => UserProfileProvider(
                getUserByIdUseCase: GetUserByIdUseCase(
                  UserRepositoryImpl(
                    remoteDataSource: UserRemoteDataSourceImpl(),
                    localDataSource: UserLocalDataSourceImpl(database),
                  ),
                ),
                updateUserUseCase: UpdateUserUseCase(
                  UserRepositoryImpl(
                    remoteDataSource: UserRemoteDataSourceImpl(),
                    localDataSource: UserLocalDataSourceImpl(database),
                  ),
                ),
                updateUserPreferencesUseCase: UpdateUserPreferencesUseCase(
                  UserRepositoryImpl(
                    remoteDataSource: UserRemoteDataSourceImpl(),
                    localDataSource: UserLocalDataSourceImpl(database),
                  ),
                ),
                updateUserRoleUseCase: UpdateUserRoleUseCase(
                  UserRepositoryImpl(
                    remoteDataSource: UserRemoteDataSourceImpl(),
                    localDataSource: UserLocalDataSourceImpl(database),
                  ),
                ),
                syncUserUseCase: SyncUserUseCase(
                  UserRepositoryImpl(
                    remoteDataSource: UserRemoteDataSourceImpl(),
                    localDataSource: UserLocalDataSourceImpl(database),
                  ),
                ),
              ),
          update:
              (
                _,
                getUserById,
                updateUser,
                updateUserPreferences,
                updateUserRole,
                syncUser,
                __,
              ) => UserProfileProvider(
                getUserByIdUseCase: getUserById,
                updateUserUseCase: updateUser,
                updateUserPreferencesUseCase: updateUserPreferences,
                updateUserRoleUseCase: updateUserRole,
                syncUserUseCase: syncUser,
              ),
        ),
        // Walkabout Provider
        ChangeNotifierProxyProvider2<
          CreateWalkaboutUseCase,
          WalkaboutRepository,
          WalkaboutProvider
        >(
          create:
              (_) => WalkaboutProvider(
                createWalkaboutUseCase: CreateWalkaboutUseCase(
                  repository: WalkaboutRepositoryImpl(
                    localDataSource: WalkaboutLocalDataSourceImpl(database),
                  ),
                ),
                walkaboutRepository: WalkaboutRepositoryImpl(
                  localDataSource: WalkaboutLocalDataSourceImpl(database),
                ),
              ),
          update:
              (_, createUseCase, repository, __) => WalkaboutProvider(
                createWalkaboutUseCase: createUseCase,
                walkaboutRepository: repository,
              ),
        ),
      ],
      child: MaterialApp(
        title: 'SafeStride',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(
            seedColor: const Color(0xFF2E7D32), // Safety green
          ),
          useMaterial3: true,
        ),
        initialRoute: AppRoutes.initial,
        onGenerateRoute: AppRoutes.generateRoute,
      ),
    );
  }
}

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        title: const Text('SafeStride'),
        centerTitle: true,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(Icons.security, size: 80, color: Color(0xFF2E7D32)),
            SizedBox(height: 24),
            Text(
              'Welcome to SafeStride',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Text(
              'Workplace Safety Management',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            SizedBox(height: 32),
            Text(
              'Project setup complete!',
              style: TextStyle(fontSize: 14, fontStyle: FontStyle.italic),
            ),
          ],
        ),
      ),
    );
  }
}
