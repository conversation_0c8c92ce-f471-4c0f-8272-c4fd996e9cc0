import '../entities/user.dart';
import '../repositories/user_repository.dart';

/// Use case for getting a user by ID
class GetUserByIdUseCase {
  final UserRepository _userRepository;

  const GetUserByIdUseCase(this._userRepository);

  /// Execute getting user by ID
  /// 
  /// Returns: [User] object if found, null otherwise
  /// Throws: Exception if operation fails
  Future<User?> call(String uid) async {
    try {
      return await _userRepository.getUserById(uid);
    } catch (e) {
      throw Exception('Failed to get user: ${e.toString()}');
    }
  }
}