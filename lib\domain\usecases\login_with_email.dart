import '../../core/error/failures.dart';
import '../../core/utils/validators.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Use case for logging in a user with email and password
class LoginWithEmailUseCase {
  final AuthRepository _authRepository;

  const LoginWithEmailUseCase(this._authRepository);

  /// Execute the login process
  ///
  /// Parameters:
  /// - [email]: User's email address
  /// - [password]: User's password
  ///
  /// Returns: [User] object if login is successful
  /// Throws: Exception if login fails
  Future<User> call({
    required String email,
    required String password,
  }) async {
    // Validate email format
    if (!Validators.isValidEmail(email)) {
      throw const ValidationFailure('Invalid email format');
    }

    // Validate password is not empty
    if (password.trim().isEmpty) {
      throw const ValidationFailure('Password cannot be empty');
    }

    try {
      final user = await _authRepository.loginWithEmail(
        email: email.trim().toLowerCase(),
        password: password,
      );
      return user;
    } catch (e) {
      // Try offline authentication if online login fails
      try {
        final cachedUser = await _authRepository.getCachedUser();
        if (cachedUser != null &&
            cachedUser.email.toLowerCase() == email.trim().toLowerCase()) {
          return cachedUser;
        }
      } catch (cacheError) {
        // Ignore cache errors and throw original error
      }

      rethrow;
    }
  }
}