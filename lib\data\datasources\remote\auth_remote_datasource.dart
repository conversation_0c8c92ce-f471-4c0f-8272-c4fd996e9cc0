import 'package:firebase_auth/firebase_auth.dart' as firebase_auth;
import 'package:google_sign_in/google_sign_in.dart';
import '../../models/user_model.dart';
import '../../../domain/entities/user.dart';

/// Remote data source for authentication using Firebase Auth
abstract class AuthRemoteDataSource {
  /// Register a new user with email and password
  Future<UserModel> registerWithEmail({
    required String email,
    required String password,
    String? displayName,
    String? organization,
  });

  /// Login user with email and password
  Future<UserModel> loginWithEmail({
    required String email,
    required String password,
  });

  /// Login user with Google SSO
  Future<UserModel> loginWithSSO();

  /// Logout the current user
  Future<void> logout();

  /// Reset password for the given email
  Future<void> resetPassword({required String email});

  /// Get the currently authenticated user
  Future<UserModel?> getCurrentUser();

  /// Check if user is currently authenticated
  Future<bool> isAuthenticated();

  /// Stream of authentication state changes
  Stream<UserModel?> get authStateChanges;

  /// Refresh the current user's authentication token
  Future<void> refreshToken();
}

/// Implementation of AuthRemoteDataSource using Firebase Auth
class AuthRemoteDataSourceImpl implements AuthRemoteDataSource {
  final firebase_auth.FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;

  AuthRemoteDataSourceImpl({
    firebase_auth.FirebaseAuth? firebaseAuth,
    GoogleSignIn? googleSignIn,
  })
      : _firebaseAuth = firebaseAuth ?? firebase_auth.FirebaseAuth.instance,
        _googleSignIn = googleSignIn ?? GoogleSignIn();

  @override
  Future<UserModel> registerWithEmail({
    required String email,
    required String password,
    String? displayName,
    String? organization,
  }) async {
    try {
      final credential = await _firebaseAuth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      final firebaseUser = credential.user;
      if (firebaseUser == null) {
        throw Exception('Failed to create user');
      }

      // Update display name if provided
      if (displayName != null) {
        await firebaseUser.updateDisplayName(displayName);
        await firebaseUser.reload();
      }

      return _mapFirebaseUserToUserModel(
        _firebaseAuth.currentUser!,
        organization: organization,
      );
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw _mapFirebaseAuthException(e);
    } catch (e) {
      throw Exception('Registration failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> loginWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final credential = await _firebaseAuth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      final firebaseUser = credential.user;
      if (firebaseUser == null) {
        throw Exception('Failed to sign in');
      }

      return _mapFirebaseUserToUserModel(firebaseUser);
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw _mapFirebaseAuthException(e);
    } catch (e) {
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel> loginWithSSO() async {
    try {
      // Trigger the authentication flow
      final GoogleSignInAccount? googleUser = await _googleSignIn.signIn();
      
      if (googleUser == null) {
        throw Exception('Google sign in was cancelled');
      }

      // Obtain the auth details from the request
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

      // Create a new credential
      final credential = firebase_auth.GoogleAuthProvider.credential(
        accessToken: googleAuth.accessToken,
        idToken: googleAuth.idToken,
      );

      // Sign in to Firebase with the Google credential
      final userCredential = await _firebaseAuth.signInWithCredential(credential);
      
      final firebaseUser = userCredential.user;
      if (firebaseUser == null) {
        throw Exception('Failed to sign in with Google');
      }

      return _mapFirebaseUserToUserModel(firebaseUser);
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw _mapFirebaseAuthException(e);
    } catch (e) {
      throw Exception('Google SSO failed: ${e.toString()}');
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Sign out from Google if signed in
      if (await _googleSignIn.isSignedIn()) {
        await _googleSignIn.signOut();
      }
      
      // Sign out from Firebase
      await _firebaseAuth.signOut();
    } catch (e) {
      throw Exception('Logout failed: ${e.toString()}');
    }
  }

  @override
  Future<void> resetPassword({required String email}) async {
    try {
      await _firebaseAuth.sendPasswordResetEmail(email: email);
    } on firebase_auth.FirebaseAuthException catch (e) {
      throw _mapFirebaseAuthException(e);
    } catch (e) {
      throw Exception('Password reset failed: ${e.toString()}');
    }
  }

  @override
  Future<UserModel?> getCurrentUser() async {
    try {
      final firebaseUser = _firebaseAuth.currentUser;
      if (firebaseUser == null) {
        return null;
      }
      
      // Reload user to get latest data
      await firebaseUser.reload();
      return _mapFirebaseUserToUserModel(_firebaseAuth.currentUser!);
    } catch (e) {
      throw Exception('Failed to get current user: ${e.toString()}');
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    return _firebaseAuth.currentUser != null;
  }

  @override
  Stream<UserModel?> get authStateChanges {
    return _firebaseAuth.authStateChanges().map((firebaseUser) {
      if (firebaseUser == null) {
        return null;
      }
      return _mapFirebaseUserToUserModel(firebaseUser);
    });
  }

  @override
  Future<void> refreshToken() async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser != null) {
        // Force refresh the ID token
        await currentUser.getIdToken(true);
      }
    } catch (e) {
      throw Exception('Token refresh failed: ${e.toString()}');
    }
  }

  /// Map Firebase User to UserModel
  UserModel _mapFirebaseUserToUserModel(
    firebase_auth.User firebaseUser, {
    String? organization,
  }) {
    return UserModel(
      uid: firebaseUser.uid,
      email: firebaseUser.email ?? '',
      displayName: firebaseUser.displayName,
      organization: organization,
      role: UserRole.inspector, // Default role, should be set from user profile
      createdAt: firebaseUser.metadata.creationTime ?? DateTime.now(),
      updatedAt: DateTime.now(),
      lastLoginAt: firebaseUser.metadata.lastSignInTime,
      preferences: const UserPreferencesModel(
        notificationsEnabled: true,
        language: 'en',
        theme: 'light',
        offlineMode: false,
      ),
    );
  }

  /// Map Firebase Auth exceptions to readable error messages
  Exception _mapFirebaseAuthException(firebase_auth.FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return Exception('The password provided is too weak.');
      case 'email-already-in-use':
        return Exception('The account already exists for that email.');
      case 'user-not-found':
        return Exception('No user found for that email.');
      case 'wrong-password':
        return Exception('Wrong password provided for that user.');
      case 'invalid-email':
        return Exception('The email address is not valid.');
      case 'user-disabled':
        return Exception('This user account has been disabled.');
      case 'too-many-requests':
        return Exception('Too many requests. Try again later.');
      case 'operation-not-allowed':
        return Exception('Signing in with Email and Password is not enabled.');
      default:
        return Exception('Authentication failed: ${e.message}');
    }
  }
}