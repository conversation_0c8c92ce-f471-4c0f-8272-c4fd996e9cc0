import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/auth_repository.dart';
import 'package:safestride/domain/usecases/login_with_email.dart';
import 'package:safestride/core/error/failures.dart';

import 'login_with_email_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  late LoginWithEmailUseCase useCase;
  late MockAuthRepository mockAuthRepository;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    useCase = LoginWithEmailUseCase(mockAuthRepository);
  });

  const testEmail = '<EMAIL>';
  const testPassword = 'password123';

  final testUser = User(
    uid: 'test-uid',
    email: testEmail,
    displayName: 'Test User',
    organization: 'Test Org',
    role: UserRole.inspector,
    createdAt: DateTime.now(),
    lastLoginAt: DateTime.now(),
  );

  group('LoginWithEmailUseCase', () {
    test('should login user successfully when repository call succeeds', () async {
      // Arrange
      when(mockAuthRepository.loginWithEmail(
        email: testEmail,
        password: testPassword,
      )).thenAnswer((_) async => testUser);

      // Act
      final result = await useCase(
        email: testEmail,
        password: testPassword,
      );

      // Assert
      expect(result, testUser);
      verify(mockAuthRepository.loginWithEmail(
        email: testEmail,
        password: testPassword,
      )).called(1);
    });

    test('should throw AuthFailure when repository call fails with invalid credentials', () async {
      // Arrange
      when(mockAuthRepository.loginWithEmail(
        email: testEmail,
        password: testPassword,
      )).thenThrow(const AuthFailure('Invalid credentials'));

      // Act & Assert
      expect(
        () => useCase(
          email: testEmail,
          password: testPassword,
        ),
        throwsA(isA<AuthFailure>()),
      );
    });

    test('should throw AuthFailure when user not found', () async {
      // Arrange
      when(mockAuthRepository.loginWithEmail(
        email: testEmail,
        password: testPassword,
      )).thenThrow(const AuthFailure('User not found'));

      // Act & Assert
      expect(
        () => useCase(
          email: testEmail,
          password: testPassword,
        ),
        throwsA(isA<AuthFailure>()),
      );
    });

    test('should throw ValidationFailure for invalid email format', () async {
      // Arrange
      const invalidEmail = 'invalid-email';

      // Act & Assert
      expect(
        () => useCase(
          email: invalidEmail,
          password: testPassword,
        ),
        throwsA(isA<ValidationFailure>()),
      );
    });

    test('should throw ValidationFailure for empty email', () async {
      // Act & Assert
      expect(
        () => useCase(
          email: '',
          password: testPassword,
        ),
        throwsA(isA<ValidationFailure>()),
      );
    });

    test('should throw ValidationFailure for empty password', () async {
      // Act & Assert
      expect(
        () => useCase(
          email: testEmail,
          password: '',
        ),
        throwsA(isA<ValidationFailure>()),
      );
    });

    test('should throw NetworkFailure when network error occurs', () async {
      // Arrange
      when(mockAuthRepository.loginWithEmail(
        email: testEmail,
        password: testPassword,
      )).thenThrow(const NetworkFailure('No connection'));

      // Act & Assert
      expect(
        () => useCase(
          email: testEmail,
          password: testPassword,
        ),
        throwsA(isA<NetworkFailure>()),
      );
    });

    test('should throw AuthFailure when too many requests', () async {
      // Arrange
      when(mockAuthRepository.loginWithEmail(
        email: testEmail,
        password: testPassword,
      )).thenThrow(const AuthFailure('Too many requests'));

      // Act & Assert
      expect(
        () => useCase(
          email: testEmail,
          password: testPassword,
        ),
        throwsA(isA<AuthFailure>()),
      );
    });

    group('offline authentication scenarios', () {
      test('should login successfully with cached credentials when offline', () async {
        // Arrange
        when(mockAuthRepository.loginWithEmail(
          email: testEmail,
          password: testPassword,
        )).thenAnswer((_) async => testUser);

        // Act
        final result = await useCase(
          email: testEmail,
          password: testPassword,
        );

        // Assert
        expect(result, testUser);
        verify(mockAuthRepository.loginWithEmail(
          email: testEmail,
          password: testPassword,
        )).called(1);
      });

      test('should throw NetworkFailure when offline and no cached credentials', () async {
        // Arrange
        when(mockAuthRepository.loginWithEmail(
          email: testEmail,
          password: testPassword,
        )).thenThrow(const NetworkFailure('No connection'));

        // Act & Assert
        expect(
          () => useCase(
            email: testEmail,
            password: testPassword,
          ),
          throwsA(isA<NetworkFailure>()),
        );
      });

      test('should throw AuthFailure when cached credentials are expired', () async {
        // Arrange
        when(mockAuthRepository.loginWithEmail(
          email: testEmail,
          password: testPassword,
        )).thenThrow(const AuthFailure('Cached credentials expired'));

        // Act & Assert
        expect(
          () => useCase(
            email: testEmail,
            password: testPassword,
          ),
          throwsA(isA<AuthFailure>()),
        );
      });

      test('should throw AuthFailure when cached credentials are invalid', () async {
        // Arrange
        when(mockAuthRepository.loginWithEmail(
          email: testEmail,
          password: testPassword,
        )).thenThrow(const AuthFailure('Invalid cached credentials'));

        // Act & Assert
        expect(
          () => useCase(
            email: testEmail,
            password: testPassword,
          ),
          throwsA(isA<AuthFailure>()),
        );
      });
    });
  });
}