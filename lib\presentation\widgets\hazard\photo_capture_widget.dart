import 'package:flutter/material.dart';
import 'dart:io';

/// Widget for capturing and managing photos for hazard documentation
/// 
/// Provides interface for taking photos, selecting from gallery,
/// and managing photo collections with preview and removal capabilities.
class PhotoCaptureWidget extends StatelessWidget {
  final List<String> photos;
  final VoidCallback? onCapturePhoto;
  final VoidCallback? onSelectFromGallery;
  final VoidCallback? onSelectMultiple;
  final ValueChanged<String>? onRemovePhoto;
  final bool isLoading;
  final String? error;
  final int maxPhotos;

  const PhotoCaptureWidget({
    super.key,
    required this.photos,
    this.onCapturePhoto,
    this.onSelectFromGallery,
    this.onSelectMultiple,
    this.onRemovePhoto,
    this.isLoading = false,
    this.error,
    this.maxPhotos = 10,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with action buttons
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Photos (${photos.length}/$maxPhotos)',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            if (!isLoading && photos.length < maxPhotos)
              Row(
                children: [
                  if (onCapturePhoto != null)
                    IconButton(
                      onPressed: onCapturePhoto,
                      icon: const Icon(Icons.camera_alt),
                      tooltip: 'Take photo',
                    ),
                  if (onSelectFromGallery != null)
                    IconButton(
                      onPressed: onSelectFromGallery,
                      icon: const Icon(Icons.photo_library),
                      tooltip: 'Select from gallery',
                    ),
                  if (onSelectMultiple != null)
                    IconButton(
                      onPressed: onSelectMultiple,
                      icon: const Icon(Icons.photo_library_outlined),
                      tooltip: 'Select multiple',
                    ),
                ],
              ),
          ],
        ),
        
        // Error display
        if (error != null)
          Container(
            margin: const EdgeInsets.only(top: 8.0),
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              border: Border.all(color: Colors.red.shade200),
              borderRadius: BorderRadius.circular(4.0),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade600, size: 16),
                const SizedBox(width: 8.0),
                Expanded(
                  child: Text(
                    error!,
                    style: TextStyle(color: Colors.red.shade600, fontSize: 12),
                  ),
                ),
              ],
            ),
          ),

        const SizedBox(height: 8.0),

        // Photo grid or empty state
        if (isLoading)
          const Center(
            child: Padding(
              padding: EdgeInsets.all(32.0),
              child: CircularProgressIndicator(),
            ),
          )
        else if (photos.isEmpty)
          _buildEmptyState(context)
        else
          _buildPhotoGrid(context),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, style: BorderStyle.solid),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_a_photo, color: Colors.grey.shade400, size: 32),
            const SizedBox(height: 8.0),
            Text(
              'No photos added',
              style: TextStyle(color: Colors.grey.shade600),
            ),
            const SizedBox(height: 4.0),
            Text(
              'Tap the camera or gallery icons to add photos',
              style: TextStyle(color: Colors.grey.shade500, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPhotoGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8.0,
        mainAxisSpacing: 8.0,
        childAspectRatio: 1.0,
      ),
      itemCount: photos.length,
      itemBuilder: (context, index) {
        final photoPath = photos[index];
        return _buildPhotoTile(context, photoPath, index);
      },
    );
  }

  Widget _buildPhotoTile(BuildContext context, String photoPath, int index) {
    return GestureDetector(
      onTap: () => _showPhotoPreview(context, photoPath, index),
      child: Stack(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8.0),
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: _buildPhotoContent(photoPath),
            ),
          ),
          
          // Remove button
          if (onRemovePhoto != null)
            Positioned(
              top: 4,
              right: 4,
              child: GestureDetector(
                onTap: () => onRemovePhoto!(photoPath),
                child: Container(
                  padding: const EdgeInsets.all(2),
                  decoration: const BoxDecoration(
                    color: Colors.red,
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 16,
                  ),
                ),
              ),
            ),
          
          // Photo index
          Positioned(
            bottom: 4,
            left: 4,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoContent(String photoPath) {
    // For now, show a placeholder since we can't display actual images
    // In a real implementation, you would use Image.file(File(photoPath))
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Icon(Icons.image, size: 32, color: Colors.grey),
        const SizedBox(height: 4),
        Text(
          'Photo',
          style: TextStyle(
            fontSize: 10,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  void _showPhotoPreview(BuildContext context, String photoPath, int index) {
    showDialog(
      context: context,
      builder: (context) => PhotoPreviewDialog(
        photoPath: photoPath,
        photoIndex: index,
        totalPhotos: photos.length,
        onRemove: onRemovePhoto != null ? () => onRemovePhoto!(photoPath) : null,
      ),
    );
  }
}

/// Dialog for previewing photos with full-screen view
class PhotoPreviewDialog extends StatelessWidget {
  final String photoPath;
  final int photoIndex;
  final int totalPhotos;
  final VoidCallback? onRemove;

  const PhotoPreviewDialog({
    super.key,
    required this.photoPath,
    required this.photoIndex,
    required this.totalPhotos,
    this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.black,
      insetPadding: EdgeInsets.zero,
      child: Stack(
        children: [
          // Photo content
          Center(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.grey.shade300,
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.image, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Photo Preview',
                    style: TextStyle(color: Colors.grey, fontSize: 16),
                  ),
                ],
              ),
            ),
          ),
          
          // Header with controls
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black54,
                    Colors.transparent,
                  ],
                ),
              ),
              child: SafeArea(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                    Text(
                      '${photoIndex + 1} of $totalPhotos',
                      style: const TextStyle(color: Colors.white),
                    ),
                    if (onRemove != null)
                      IconButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                          onRemove!();
                        },
                        icon: const Icon(Icons.delete, color: Colors.white),
                      )
                    else
                      const SizedBox(width: 48),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Compact photo list widget for use in forms
class CompactPhotoList extends StatelessWidget {
  final List<String> photos;
  final ValueChanged<String>? onRemovePhoto;
  final double height;

  const CompactPhotoList({
    super.key,
    required this.photos,
    this.onRemovePhoto,
    this.height = 60,
  });

  @override
  Widget build(BuildContext context) {
    if (photos.isEmpty) {
      return Container(
        height: height,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Center(
          child: Text(
            'No photos',
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
          ),
        ),
      );
    }

    return SizedBox(
      height: height,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: photos.length,
        itemBuilder: (context, index) {
          final photoPath = photos[index];
          return Container(
            margin: const EdgeInsets.only(right: 8.0),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(4.0),
                  child: Container(
                    width: height,
                    height: height,
                    color: Colors.grey.shade200,
                    child: const Icon(Icons.image, size: 24),
                  ),
                ),
                if (onRemovePhoto != null)
                  Positioned(
                    top: 2,
                    right: 2,
                    child: GestureDetector(
                      onTap: () => onRemovePhoto!(photoPath),
                      child: Container(
                        padding: const EdgeInsets.all(1),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
