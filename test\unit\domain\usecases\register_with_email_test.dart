import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/auth_repository.dart';
import 'package:safestride/domain/usecases/register_with_email.dart';

import 'register_with_email_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  late RegisterWithEmailUseCase useCase;
  late MockAuthRepository mockAuthRepository;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    useCase = RegisterWithEmailUseCase(mockAuthRepository);
  });

  const tEmail = '<EMAIL>';
  const tPassword = 'password123';
  const tDisplayName = 'Test User';
  const tOrganization = 'Test Org';
  final tUser = User(
    uid: '123',
    email: tEmail,
    displayName: tDisplayName,
    organization: tOrganization,
    role: UserRole.inspector,
    createdAt: DateTime.now(),
  );

  group('RegisterWithEmailUseCase', () {
    test('should return User when registration is successful', () async {
      // arrange
      when(mockAuthRepository.registerWithEmail(
        email: anyNamed('email'),
        password: anyNamed('password'),
        displayName: anyNamed('displayName'),
        organization: anyNamed('organization'),
      )).thenAnswer((_) async => tUser);
      
      when(mockAuthRepository.cacheCredentials(any))
          .thenAnswer((_) async => {});

      // act
      final result = await useCase(
        email: tEmail,
        password: tPassword,
        displayName: tDisplayName,
        organization: tOrganization,
      );

      // assert
      expect(result, equals(tUser));
      verify(mockAuthRepository.registerWithEmail(
        email: tEmail.toLowerCase(),
        password: tPassword,
        displayName: tDisplayName,
        organization: tOrganization,
      ));
      verify(mockAuthRepository.cacheCredentials(tUser));
    });

    test('should throw Exception when repository throws exception', () async {
      // arrange
      when(mockAuthRepository.registerWithEmail(
        email: anyNamed('email'),
        password: anyNamed('password'),
        displayName: anyNamed('displayName'),
        organization: anyNamed('organization'),
      )).thenThrow(Exception('Registration failed'));

      // act & assert
      expect(
        () => useCase(
          email: tEmail,
          password: tPassword,
          displayName: tDisplayName,
          organization: tOrganization,
        ),
        throwsA(isA<Exception>()),
      );
    });

    test('should throw ArgumentError for invalid email', () async {
      // act & assert
      expect(
        () => useCase(
          email: 'invalid-email',
          password: tPassword,
          displayName: tDisplayName,
          organization: tOrganization,
        ),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should throw ArgumentError for weak password', () async {
      // act & assert
      expect(
        () => useCase(
          email: tEmail,
          password: '123',
          displayName: tDisplayName,
          organization: tOrganization,
        ),
        throwsA(isA<ArgumentError>()),
      );
    });

    test('should throw ArgumentError for empty display name', () async {
      // act & assert
      expect(
        () => useCase(
          email: tEmail,
          password: tPassword,
          displayName: '',
          organization: tOrganization,
        ),
        throwsA(isA<ArgumentError>()),
      );
    });
  });
}