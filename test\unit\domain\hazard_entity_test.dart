import 'package:flutter_test/flutter_test.dart';
import 'package:safestride/domain/entities/hazard.dart';
import 'package:safestride/domain/entities/walkabout.dart';

void main() {
  group('Hazard Entity Tests', () {
    late Hazard testHazard;
    late DateTime testDateTime;
    late GeoPoint testLocation;

    setUp(() {
      testDateTime = DateTime(2024, 1, 1, 12, 0, 0);
      testLocation = const GeoPoint(latitude: 37.7749, longitude: -122.4194);
      
      testHazard = Hazard(
        id: 'hazard_123',
        walkaboutId: 'walkabout_456',
        title: 'Test Hazard',
        description: 'Test hazard description',
        severity: HazardSeverity.medium,
        category: HazardCategory.slipTripFall,
        location: testLocation,
        photos: ['photo1.jpg', 'photo2.jpg'],
        notes: 'Test notes',
        createdAt: testDateTime,
        updatedAt: testDateTime,
        syncStatus: SyncStatus.local,
      );
    });

    test('should create hazard with all properties', () {
      expect(testHazard.id, equals('hazard_123'));
      expect(testHazard.walkaboutId, equals('walkabout_456'));
      expect(testHazard.title, equals('Test Hazard'));
      expect(testHazard.description, equals('Test hazard description'));
      expect(testHazard.severity, equals(HazardSeverity.medium));
      expect(testHazard.category, equals(HazardCategory.slipTripFall));
      expect(testHazard.location, equals(testLocation));
      expect(testHazard.photos, equals(['photo1.jpg', 'photo2.jpg']));
      expect(testHazard.notes, equals('Test notes'));
      expect(testHazard.createdAt, equals(testDateTime));
      expect(testHazard.updatedAt, equals(testDateTime));
      expect(testHazard.syncStatus, equals(SyncStatus.local));
    });

    test('should create hazard with minimal properties', () {
      final minimalHazard = Hazard(
        id: 'hazard_minimal',
        walkaboutId: 'walkabout_minimal',
        title: 'Minimal Hazard',
        severity: HazardSeverity.low,
        category: HazardCategory.other,
        photos: [],
        createdAt: testDateTime,
        updatedAt: testDateTime,
        syncStatus: SyncStatus.local,
      );

      expect(minimalHazard.description, isNull);
      expect(minimalHazard.location, isNull);
      expect(minimalHazard.notes, isNull);
      expect(minimalHazard.photos, isEmpty);
    });

    test('should create copy with updated properties', () {
      final updatedHazard = testHazard.copyWith(
        title: 'Updated Hazard',
        severity: HazardSeverity.high,
        photos: ['new_photo.jpg'],
      );

      expect(updatedHazard.title, equals('Updated Hazard'));
      expect(updatedHazard.severity, equals(HazardSeverity.high));
      expect(updatedHazard.photos, equals(['new_photo.jpg']));
      
      // Other properties should remain unchanged
      expect(updatedHazard.id, equals(testHazard.id));
      expect(updatedHazard.walkaboutId, equals(testHazard.walkaboutId));
      expect(updatedHazard.description, equals(testHazard.description));
    });

    test('should support equality comparison', () {
      final identicalHazard = Hazard(
        id: 'hazard_123',
        walkaboutId: 'walkabout_456',
        title: 'Test Hazard',
        description: 'Test hazard description',
        severity: HazardSeverity.medium,
        category: HazardCategory.slipTripFall,
        location: testLocation,
        photos: ['photo1.jpg', 'photo2.jpg'],
        notes: 'Test notes',
        createdAt: testDateTime,
        updatedAt: testDateTime,
        syncStatus: SyncStatus.local,
      );

      expect(testHazard, equals(identicalHazard));
      expect(testHazard.hashCode, equals(identicalHazard.hashCode));
    });

    test('should have proper toString representation', () {
      final stringRepresentation = testHazard.toString();
      
      expect(stringRepresentation, contains('hazard_123'));
      expect(stringRepresentation, contains('Test Hazard'));
      expect(stringRepresentation, contains('medium'));
      expect(stringRepresentation, contains('slipTripFall'));
    });
  });

  group('HazardSeverity Tests', () {
    test('should have correct display names', () {
      expect(HazardSeverity.low.displayName, equals('Low'));
      expect(HazardSeverity.medium.displayName, equals('Medium'));
      expect(HazardSeverity.high.displayName, equals('High'));
      expect(HazardSeverity.critical.displayName, equals('Critical'));
    });

    test('should have correct descriptions', () {
      expect(HazardSeverity.low.description, contains('Minor risk'));
      expect(HazardSeverity.medium.description, contains('Moderate risk'));
      expect(HazardSeverity.high.description, contains('Significant risk'));
      expect(HazardSeverity.critical.description, contains('Severe risk'));
    });

    test('should have correct color values', () {
      expect(HazardSeverity.low.colorValue, equals(0xFF4CAF50)); // Green
      expect(HazardSeverity.medium.colorValue, equals(0xFFFF9800)); // Orange
      expect(HazardSeverity.high.colorValue, equals(0xFFFF5722)); // Deep Orange
      expect(HazardSeverity.critical.colorValue, equals(0xFFF44336)); // Red
    });
  });

  group('HazardCategory Tests', () {
    test('should have correct display names', () {
      expect(HazardCategory.slipTripFall.displayName, equals('Slip/Trip/Fall'));
      expect(HazardCategory.electrical.displayName, equals('Electrical'));
      expect(HazardCategory.chemical.displayName, equals('Chemical'));
      expect(HazardCategory.fire.displayName, equals('Fire'));
      expect(HazardCategory.machinery.displayName, equals('Machinery'));
      expect(HazardCategory.ergonomic.displayName, equals('Ergonomic'));
      expect(HazardCategory.environmental.displayName, equals('Environmental'));
      expect(HazardCategory.biological.displayName, equals('Biological'));
      expect(HazardCategory.radiation.displayName, equals('Radiation'));
      expect(HazardCategory.other.displayName, equals('Other'));
    });

    test('should have correct icon names', () {
      expect(HazardCategory.slipTripFall.iconName, equals('warning'));
      expect(HazardCategory.electrical.iconName, equals('flash_on'));
      expect(HazardCategory.chemical.iconName, equals('science'));
      expect(HazardCategory.fire.iconName, equals('local_fire_department'));
      expect(HazardCategory.machinery.iconName, equals('precision_manufacturing'));
      expect(HazardCategory.ergonomic.iconName, equals('accessibility'));
      expect(HazardCategory.environmental.iconName, equals('eco'));
      expect(HazardCategory.biological.iconName, equals('biotech'));
      expect(HazardCategory.radiation.iconName, equals('radioactive'));
      expect(HazardCategory.other.iconName, equals('help_outline'));
    });
  });
}
