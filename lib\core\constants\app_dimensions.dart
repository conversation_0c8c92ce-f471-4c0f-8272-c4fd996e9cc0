/// Application dimension constants
class AppDimensions {
  // Private constructor to prevent instantiation
  AppDimensions._();

  // Padding
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  static const double paddingXXL = 48.0;

  // Margin
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;
  static const double marginXXL = 48.0;

  // Border radius
  static const double radiusXS = 2.0;
  static const double radiusS = 4.0;
  static const double radiusM = 8.0;
  static const double radiusL = 12.0;
  static const double radiusXL = 16.0;
  static const double radiusXXL = 24.0;
  static const double radiusCircular = 50.0;

  // Icon sizes
  static const double iconXS = 12.0;
  static const double iconS = 16.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  static const double iconXXL = 64.0;

  // Button dimensions
  static const double buttonHeight = 48.0;
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightLarge = 56.0;
  static const double buttonMinWidth = 88.0;
  static const double buttonPadding = 16.0;
  static const double buttonPaddingSmall = 12.0;
  static const double buttonPaddingLarge = 20.0;

  // Input field dimensions
  static const double inputHeight = 48.0;
  static const double inputHeightSmall = 36.0;
  static const double inputHeightLarge = 56.0;
  static const double inputPadding = 16.0;
  static const double inputBorderWidth = 1.0;
  static const double inputBorderWidthFocused = 2.0;

  // Card dimensions
  static const double cardElevation = 2.0;
  static const double cardElevationHovered = 4.0;
  static const double cardElevationPressed = 8.0;
  static const double cardPadding = 16.0;
  static const double cardMargin = 8.0;
  static const double cardRadius = 8.0;

  // AppBar dimensions
  static const double appBarHeight = 56.0;
  static const double appBarElevation = 4.0;
  static const double appBarIconSize = 24.0;

  // Bottom navigation
  static const double bottomNavHeight = 60.0;
  static const double bottomNavIconSize = 24.0;
  static const double bottomNavElevation = 8.0;

  // Drawer dimensions
  static const double drawerWidth = 280.0;
  static const double drawerHeaderHeight = 160.0;
  static const double drawerItemHeight = 48.0;
  static const double drawerItemPadding = 16.0;

  // List item dimensions
  static const double listItemHeight = 56.0;
  static const double listItemHeightSmall = 40.0;
  static const double listItemHeightLarge = 72.0;
  static const double listItemPadding = 16.0;
  static const double listItemIconSize = 24.0;

  // Avatar dimensions
  static const double avatarS = 24.0;
  static const double avatarM = 32.0;
  static const double avatarL = 48.0;
  static const double avatarXL = 64.0;
  static const double avatarXXL = 96.0;

  // Divider
  static const double dividerThickness = 1.0;
  static const double dividerIndent = 16.0;

  // Spacing
  static const double spaceXS = 4.0;
  static const double spaceS = 8.0;
  static const double spaceM = 16.0;
  static const double spaceL = 24.0;
  static const double spaceXL = 32.0;
  static const double spaceXXL = 48.0;

  // Border width
  static const double borderThin = 0.5;
  static const double borderNormal = 1.0;
  static const double borderThick = 2.0;
  static const double borderExtraThick = 4.0;

  // Shadow
  static const double shadowBlurRadius = 4.0;
  static const double shadowSpreadRadius = 0.0;
  static const double shadowOffsetX = 0.0;
  static const double shadowOffsetY = 2.0;

  // Dialog dimensions
  static const double dialogMaxWidth = 400.0;
  static const double dialogMinWidth = 280.0;
  static const double dialogPadding = 24.0;
  static const double dialogRadius = 12.0;
  static const double dialogElevation = 24.0;

  // Snackbar dimensions
  static const double snackbarHeight = 48.0;
  static const double snackbarPadding = 16.0;
  static const double snackbarRadius = 4.0;
  static const double snackbarElevation = 6.0;

  // Tab dimensions
  static const double tabHeight = 48.0;
  static const double tabMinWidth = 72.0;
  static const double tabPadding = 16.0;
  static const double tabIndicatorHeight = 2.0;

  // Chip dimensions
  static const double chipHeight = 32.0;
  static const double chipPadding = 12.0;
  static const double chipRadius = 16.0;
  static const double chipIconSize = 18.0;

  // Progress indicator
  static const double progressIndicatorSize = 24.0;
  static const double progressIndicatorSizeSmall = 16.0;
  static const double progressIndicatorSizeLarge = 32.0;
  static const double progressIndicatorStrokeWidth = 4.0;

  // Floating action button
  static const double fabSize = 56.0;
  static const double fabSizeSmall = 40.0;
  static const double fabSizeLarge = 64.0;
  static const double fabIconSize = 24.0;
  static const double fabElevation = 6.0;

  // Switch dimensions
  static const double switchWidth = 51.0;
  static const double switchHeight = 31.0;
  static const double switchThumbSize = 20.0;

  // Checkbox dimensions
  static const double checkboxSize = 20.0;
  static const double checkboxBorderWidth = 2.0;
  static const double checkboxRadius = 2.0;

  // Radio button dimensions
  static const double radioSize = 20.0;
  static const double radioBorderWidth = 2.0;
  static const double radioInnerSize = 10.0;

  // Slider dimensions
  static const double sliderHeight = 40.0;
  static const double sliderThumbSize = 20.0;
  static const double sliderTrackHeight = 4.0;

  // Stepper dimensions
  static const double stepperIconSize = 24.0;
  static const double stepperLineWidth = 1.0;
  static const double stepperPadding = 24.0;

  // Expansion tile dimensions
  static const double expansionTileHeight = 56.0;
  static const double expansionTilePadding = 16.0;
  static const double expansionTileIconSize = 24.0;

  // Image dimensions
  static const double imageThumbSize = 80.0;
  static const double imagePreviewSize = 200.0;
  static const double imageFullSize = 400.0;

  // Screen breakpoints
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;

  // Safe area
  static const double safeAreaTop = 24.0;
  static const double safeAreaBottom = 24.0;
  static const double safeAreaLeft = 16.0;
  static const double safeAreaRight = 16.0;

  // Animation durations (in milliseconds)
  static const int animationFast = 150;
  static const int animationNormal = 300;
  static const int animationSlow = 500;
  static const int animationVerySlow = 1000;

  // Opacity values
  static const double opacityDisabled = 0.38;
  static const double opacityMedium = 0.54;
  static const double opacityHigh = 0.87;
  static const double opacityFull = 1.0;

  // Z-index values
  static const double zIndexBase = 0.0;
  static const double zIndexDropdown = 1000.0;
  static const double zIndexSticky = 1020.0;
  static const double zIndexFixed = 1030.0;
  static const double zIndexModalBackdrop = 1040.0;
  static const double zIndexModal = 1050.0;
  static const double zIndexPopover = 1060.0;
  static const double zIndexTooltip = 1070.0;
}