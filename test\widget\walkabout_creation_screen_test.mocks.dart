// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in safestride/test/widget/walkabout_creation_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i6;
import 'dart:ui' as _i7;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/user.dart' as _i9;
import 'package:safestride/domain/entities/walkabout.dart' as _i5;
import 'package:safestride/domain/repositories/walkabout_repository.dart'
    as _i3;
import 'package:safestride/domain/usecases/create_walkabout.dart' as _i2;
import 'package:safestride/presentation/providers/auth_provider.dart' as _i8;
import 'package:safestride/presentation/providers/walkabout_provider.dart'
    as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCreateWalkaboutUseCase_0 extends _i1.SmartFake
    implements _i2.CreateWalkaboutUseCase {
  _FakeCreateWalkaboutUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeWalkaboutRepository_1 extends _i1.SmartFake
    implements _i3.WalkaboutRepository {
  _FakeWalkaboutRepository_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [WalkaboutProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockWalkaboutProvider extends _i1.Mock implements _i4.WalkaboutProvider {
  MockWalkaboutProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.CreateWalkaboutUseCase get createWalkaboutUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#createWalkaboutUseCase),
            returnValue: _FakeCreateWalkaboutUseCase_0(
              this,
              Invocation.getter(#createWalkaboutUseCase),
            ),
          )
          as _i2.CreateWalkaboutUseCase);

  @override
  _i3.WalkaboutRepository get walkaboutRepository =>
      (super.noSuchMethod(
            Invocation.getter(#walkaboutRepository),
            returnValue: _FakeWalkaboutRepository_1(
              this,
              Invocation.getter(#walkaboutRepository),
            ),
          )
          as _i3.WalkaboutRepository);

  @override
  List<_i5.Walkabout> get walkabouts =>
      (super.noSuchMethod(
            Invocation.getter(#walkabouts),
            returnValue: <_i5.Walkabout>[],
          )
          as List<_i5.Walkabout>);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  _i5.WalkaboutStatus get selectedStatus =>
      (super.noSuchMethod(
            Invocation.getter(#selectedStatus),
            returnValue: _i5.WalkaboutStatus.draft,
          )
          as _i5.WalkaboutStatus);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  List<_i5.Walkabout> getWalkaboutsByStatus(_i5.WalkaboutStatus? status) =>
      (super.noSuchMethod(
            Invocation.method(#getWalkaboutsByStatus, [status]),
            returnValue: <_i5.Walkabout>[],
          )
          as List<_i5.Walkabout>);

  @override
  int getWalkaboutsCountByStatus(_i5.WalkaboutStatus? status) =>
      (super.noSuchMethod(
            Invocation.method(#getWalkaboutsCountByStatus, [status]),
            returnValue: 0,
          )
          as int);

  @override
  _i6.Future<_i5.Walkabout?> createWalkabout({
    required String? title,
    String? description,
    required String? userId,
    _i5.GeoPoint? location,
    _i5.WalkaboutStatus? status,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createWalkabout, [], {
              #title: title,
              #description: description,
              #userId: userId,
              #location: location,
              #status: status,
            }),
            returnValue: _i6.Future<_i5.Walkabout?>.value(),
          )
          as _i6.Future<_i5.Walkabout?>);

  @override
  _i6.Future<void> loadWalkabouts(String? userId) =>
      (super.noSuchMethod(
            Invocation.method(#loadWalkabouts, [userId]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> loadWalkaboutsByStatus(
    String? userId,
    _i5.WalkaboutStatus? status,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#loadWalkaboutsByStatus, [userId, status]),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<_i5.Walkabout?> getWalkaboutById(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#getWalkaboutById, [id]),
            returnValue: _i6.Future<_i5.Walkabout?>.value(),
          )
          as _i6.Future<_i5.Walkabout?>);

  @override
  _i6.Future<_i5.Walkabout?> updateWalkabout(_i5.Walkabout? walkabout) =>
      (super.noSuchMethod(
            Invocation.method(#updateWalkabout, [walkabout]),
            returnValue: _i6.Future<_i5.Walkabout?>.value(),
          )
          as _i6.Future<_i5.Walkabout?>);

  @override
  _i6.Future<bool> deleteWalkabout(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteWalkabout, [id]),
            returnValue: _i6.Future<bool>.value(false),
          )
          as _i6.Future<bool>);

  @override
  void setCurrentWalkabout(_i5.Walkabout? walkabout) => super.noSuchMethod(
    Invocation.method(#setCurrentWalkabout, [walkabout]),
    returnValueForMissingStub: null,
  );

  @override
  void setSelectedStatus(_i5.WalkaboutStatus? status) => super.noSuchMethod(
    Invocation.method(#setSelectedStatus, [status]),
    returnValueForMissingStub: null,
  );

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  void clearWalkabouts() => super.noSuchMethod(
    Invocation.method(#clearWalkabouts, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i7.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i7.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AuthProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockAuthProvider extends _i1.Mock implements _i8.AuthProvider {
  MockAuthProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i8.AuthState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _i8.AuthState.initial,
          )
          as _i8.AuthState);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  bool get isAuthenticated =>
      (super.noSuchMethod(
            Invocation.getter(#isAuthenticated),
            returnValue: false,
          )
          as bool);

  @override
  bool get isAdmin =>
      (super.noSuchMethod(Invocation.getter(#isAdmin), returnValue: false)
          as bool);

  @override
  bool get isInspector =>
      (super.noSuchMethod(Invocation.getter(#isInspector), returnValue: false)
          as bool);

  @override
  bool get isManager =>
      (super.noSuchMethod(Invocation.getter(#isManager), returnValue: false)
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i6.Future<void> registerWithEmail({
    required String? email,
    required String? password,
    String? displayName,
    String? organization,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#registerWithEmail, [], {
              #email: email,
              #password: password,
              #displayName: displayName,
              #organization: organization,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> loginWithEmail({
    required String? email,
    required String? password,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#loginWithEmail, [], {
              #email: email,
              #password: password,
            }),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> loginWithSSO() =>
      (super.noSuchMethod(
            Invocation.method(#loginWithSSO, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> logout() =>
      (super.noSuchMethod(
            Invocation.method(#logout, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> resetPassword({required String? email}) =>
      (super.noSuchMethod(
            Invocation.method(#resetPassword, [], {#email: email}),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  _i6.Future<void> refreshUser() =>
      (super.noSuchMethod(
            Invocation.method(#refreshUser, []),
            returnValue: _i6.Future<void>.value(),
            returnValueForMissingStub: _i6.Future<void>.value(),
          )
          as _i6.Future<void>);

  @override
  void listenToAuthChanges() => super.noSuchMethod(
    Invocation.method(#listenToAuthChanges, []),
    returnValueForMissingStub: null,
  );

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  bool hasRole(_i9.UserRole? role) =>
      (super.noSuchMethod(
            Invocation.method(#hasRole, [role]),
            returnValue: false,
          )
          as bool);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i7.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i7.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}
