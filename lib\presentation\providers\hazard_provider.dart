import 'package:flutter/foundation.dart';
import '../../domain/entities/hazard.dart';
import '../../domain/entities/walkabout.dart';
import '../../domain/usecases/create_hazard.dart';
import '../../domain/usecases/update_hazard.dart';
import '../../domain/repositories/hazard_repository.dart';
import '../../services/camera/camera_service.dart';
import '../../services/location/location_service.dart';
import '../../services/voice/voice_input_service.dart';

/// Provider for hazard state management
/// 
/// Handles hazard creation, retrieval, photo management, voice input,
/// and state updates following Clean Architecture and Provider pattern principles.
class HazardProvider extends ChangeNotifier {
  final CreateHazardUseCase createHazardUseCase;
  final UpdateHazardUseCase updateHazardUseCase;
  final HazardRepository hazardRepository;
  final CameraService cameraService;
  final LocationService locationService;
  final VoiceInputService voiceInputService;

  // State variables
  List<Hazard> _hazards = [];
  Hazard? _currentHazard;
  bool _isLoading = false;
  String? _error;
  HazardSeverity _selectedSeverity = HazardSeverity.medium;
  HazardCategory _selectedCategory = HazardCategory.other;
  List<String> _selectedPhotos = [];
  GeoPoint? _selectedLocation;
  bool _isVoiceInputActive = false;
  String _voiceInputText = '';

  HazardProvider({
    required this.createHazardUseCase,
    required this.updateHazardUseCase,
    required this.hazardRepository,
    required this.cameraService,
    required this.locationService,
    required this.voiceInputService,
  });

  // Getters
  List<Hazard> get hazards => List.unmodifiable(_hazards);
  Hazard? get currentHazard => _currentHazard;
  bool get isLoading => _isLoading;
  String? get error => _error;
  HazardSeverity get selectedSeverity => _selectedSeverity;
  HazardCategory get selectedCategory => _selectedCategory;
  List<String> get selectedPhotos => List.unmodifiable(_selectedPhotos);
  GeoPoint? get selectedLocation => _selectedLocation;
  bool get isVoiceInputActive => _isVoiceInputActive;
  String get voiceInputText => _voiceInputText;

  /// Get hazards for a specific walkabout
  List<Hazard> getHazardsByWalkabout(String walkaboutId) {
    return _hazards.where((hazard) => hazard.walkaboutId == walkaboutId).toList();
  }

  /// Get hazards filtered by severity
  List<Hazard> getHazardsBySeverity(String walkaboutId, HazardSeverity severity) {
    return _hazards
        .where((hazard) => hazard.walkaboutId == walkaboutId && hazard.severity == severity)
        .toList();
  }

  /// Get hazards filtered by category
  List<Hazard> getHazardsByCategory(String walkaboutId, HazardCategory category) {
    return _hazards
        .where((hazard) => hazard.walkaboutId == walkaboutId && hazard.category == category)
        .toList();
  }

  /// Get hazard statistics for a walkabout
  Map<String, dynamic> getHazardStatistics(String walkaboutId) {
    final walkaboutHazards = getHazardsByWalkabout(walkaboutId);
    
    final Map<HazardSeverity, int> severityCounts = {};
    final Map<HazardCategory, int> categoryCounts = {};
    
    for (final hazard in walkaboutHazards) {
      severityCounts[hazard.severity] = (severityCounts[hazard.severity] ?? 0) + 1;
      categoryCounts[hazard.category] = (categoryCounts[hazard.category] ?? 0) + 1;
    }
    
    return {
      'total': walkaboutHazards.length,
      'bySeverity': severityCounts,
      'byCategory': categoryCounts,
    };
  }

  /// Load hazards for a specific walkabout
  Future<void> loadHazards(String walkaboutId) async {
    try {
      _setLoading(true);
      _clearError();

      final hazards = await hazardRepository.getHazardsByWalkaboutId(walkaboutId);
      _hazards = hazards;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to load hazards: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Create a new hazard
  Future<Hazard?> createHazard({
    required String walkaboutId,
    required String title,
    String? description,
    required HazardSeverity severity,
    required HazardCategory category,
    GeoPoint? location,
    List<String>? photos,
    String? notes,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final params = CreateHazardParams(
        walkaboutId: walkaboutId,
        title: title,
        description: description,
        severity: severity,
        category: category,
        location: location,
        photos: photos ?? [],
        notes: notes,
      );

      final hazard = await createHazardUseCase.call(params);
      
      // Add to local list
      _hazards.add(hazard);
      _currentHazard = hazard;
      
      notifyListeners();
      return hazard;
    } catch (e) {
      _setError('Failed to create hazard: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing hazard
  Future<Hazard?> updateHazard({
    required String id,
    String? title,
    String? description,
    HazardSeverity? severity,
    HazardCategory? category,
    GeoPoint? location,
    List<String>? photos,
    String? notes,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final params = UpdateHazardParams(
        id: id,
        title: title,
        description: description,
        severity: severity,
        category: category,
        location: location,
        photos: photos,
        notes: notes,
      );

      final updatedHazard = await updateHazardUseCase.call(params);
      
      // Update in local list
      final index = _hazards.indexWhere((h) => h.id == id);
      if (index != -1) {
        _hazards[index] = updatedHazard;
        if (_currentHazard?.id == id) {
          _currentHazard = updatedHazard;
        }
      }
      
      notifyListeners();
      return updatedHazard;
    } catch (e) {
      _setError('Failed to update hazard: ${e.toString()}');
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a hazard
  Future<bool> deleteHazard(String id) async {
    try {
      _setLoading(true);
      _clearError();

      final success = await hazardRepository.deleteHazard(id);
      
      if (success) {
        _hazards.removeWhere((h) => h.id == id);
        if (_currentHazard?.id == id) {
          _currentHazard = null;
        }
        notifyListeners();
      }
      
      return success;
    } catch (e) {
      _setError('Failed to delete hazard: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Set current hazard
  void setCurrentHazard(Hazard? hazard) {
    _currentHazard = hazard;
    if (hazard != null) {
      _selectedSeverity = hazard.severity;
      _selectedCategory = hazard.category;
      _selectedPhotos = List.from(hazard.photos);
      _selectedLocation = hazard.location;
    }
    notifyListeners();
  }

  /// Set selected severity
  void setSelectedSeverity(HazardSeverity severity) {
    _selectedSeverity = severity;
    notifyListeners();
  }

  /// Set selected category
  void setSelectedCategory(HazardCategory category) {
    _selectedCategory = category;
    notifyListeners();
  }

  /// Set selected location
  void setSelectedLocation(GeoPoint? location) {
    _selectedLocation = location;
    notifyListeners();
  }

  /// Get current location
  Future<void> getCurrentLocation() async {
    try {
      _setLoading(true);
      _clearError();

      final location = await locationService.getCurrentLocation();
      _selectedLocation = location;
      
      notifyListeners();
    } catch (e) {
      _setError('Failed to get current location: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Capture photo from camera
  Future<void> capturePhoto() async {
    try {
      _setLoading(true);
      _clearError();

      final photoPath = await cameraService.capturePhoto();
      if (photoPath != null) {
        _selectedPhotos.add(photoPath);
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to capture photo: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Select photo from gallery
  Future<void> selectPhotoFromGallery() async {
    try {
      _setLoading(true);
      _clearError();

      final photoPath = await cameraService.selectFromGallery();
      if (photoPath != null) {
        _selectedPhotos.add(photoPath);
        notifyListeners();
      }
    } catch (e) {
      _setError('Failed to select photo: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Select multiple photos from gallery
  Future<void> selectMultiplePhotos() async {
    try {
      _setLoading(true);
      _clearError();

      final photoPaths = await cameraService.selectMultipleFromGallery();
      _selectedPhotos.addAll(photoPaths);
      notifyListeners();
    } catch (e) {
      _setError('Failed to select photos: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Remove photo from selection
  void removePhoto(String photoPath) {
    _selectedPhotos.remove(photoPath);
    notifyListeners();
  }

  /// Clear all selected photos
  void clearPhotos() {
    _selectedPhotos.clear();
    notifyListeners();
  }

  /// Start voice input
  Future<void> startVoiceInput() async {
    try {
      _isVoiceInputActive = true;
      _voiceInputText = '';
      notifyListeners();

      final success = await voiceInputService.startListening(
        onResult: (text) {
          _voiceInputText = text;
          notifyListeners();
        },
        onError: (error) {
          _setError('Voice input error: $error');
          _isVoiceInputActive = false;
          notifyListeners();
        },
      );

      if (!success) {
        _isVoiceInputActive = false;
        _setError('Failed to start voice input');
        notifyListeners();
      }
    } catch (e) {
      _isVoiceInputActive = false;
      _setError('Failed to start voice input: ${e.toString()}');
      notifyListeners();
    }
  }

  /// Stop voice input
  Future<void> stopVoiceInput() async {
    try {
      await voiceInputService.stopListening();
      _isVoiceInputActive = false;
      notifyListeners();
    } catch (e) {
      _setError('Failed to stop voice input: ${e.toString()}');
    }
  }

  /// Cancel voice input
  Future<void> cancelVoiceInput() async {
    try {
      await voiceInputService.cancelListening();
      _isVoiceInputActive = false;
      _voiceInputText = '';
      notifyListeners();
    } catch (e) {
      _setError('Failed to cancel voice input: ${e.toString()}');
    }
  }

  /// Clear all form data
  void clearForm() {
    _selectedSeverity = HazardSeverity.medium;
    _selectedCategory = HazardCategory.other;
    _selectedPhotos.clear();
    _selectedLocation = null;
    _voiceInputText = '';
    _isVoiceInputActive = false;
    _currentHazard = null;
    _clearError();
    notifyListeners();
  }

  /// Search hazards
  Future<List<Hazard>> searchHazards(String walkaboutId, String query) async {
    try {
      return await hazardRepository.searchHazards(walkaboutId, query);
    } catch (e) {
      _setError('Failed to search hazards: ${e.toString()}');
      return [];
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
