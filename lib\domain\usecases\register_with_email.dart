import '../../core/utils/validators.dart';
import '../entities/user.dart';
import '../repositories/auth_repository.dart';

/// Use case for registering a new user with email and password
class RegisterWithEmailUseCase {
  final AuthRepository _authRepository;

  const RegisterWithEmailUseCase(this._authRepository);

  /// Execute the registration process
  ///
  /// Parameters:
  /// - [email]: User's email address
  /// - [password]: User's password
  /// - [displayName]: Optional display name
  /// - [organization]: Optional organization name
  ///
  /// Returns: [User] object if registration is successful
  /// Throws: Exception if registration fails
  Future<User> call({
    required String email,
    required String password,
    String? displayName,
    String? organization,
  }) async {
    // Validate email format
    if (!Validators.isValidEmail(email)) {
      throw ArgumentError('Invalid email format');
    }

    // Validate password strength
    if (!Validators.isValidPassword(password)) {
      throw ArgumentError('Password must be at least 6 characters long');
    }

    // Validate display name if provided
    if (displayName != null && displayName.trim().isEmpty) {
      throw ArgumentError('Display name cannot be empty');
    }

    try {
      final user = await _authRepository.registerWithEmail(
        email: email.trim().toLowerCase(),
        password: password,
        displayName: displayName?.trim(),
        organization: organization?.trim(),
      );

      return user;
    } catch (e) {
      throw Exception('Registration failed: ${e.toString()}');
    }
  }
}