import '../entities/user.dart';
import '../repositories/user_repository.dart';

/// Use case for updating a user's profile
class UpdateUserUseCase {
  final UserRepository _userRepository;

  const UpdateUserUseCase(this._userRepository);

  /// Execute updating user
  /// 
  /// Returns: Updated [User] object
  /// Throws: Exception if operation fails
  Future<User> call(User user) async {
    try {
      return await _userRepository.updateUser(user);
    } catch (e) {
      throw Exception('Failed to update user: ${e.toString()}');
    }
  }
}