import 'package:flutter/material.dart';
import '../../../domain/entities/walkabout.dart';

/// Card widget for displaying walkabout information
/// 
/// Shows walkabout details in a card format with status indicators,
/// actions, and interactive elements following Material Design.
class WalkaboutCard extends StatelessWidget {
  final Walkabout walkabout;
  final VoidCallback? onTap;
  final ValueChanged<WalkaboutStatus>? onStatusChanged;
  final VoidCallback? onDelete;

  const WalkaboutCard({
    super.key,
    required this.walkabout,
    this.onTap,
    this.onStatusChanged,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2.0,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: <PERSON>umn(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with title and status
              Row(
                children: [
                  Expanded(
                    child: Text(
                      walkabout.title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8.0),
                  _buildStatusChip(context),
                ],
              ),
              
              // Description if available
              if (walkabout.description != null && walkabout.description!.isNotEmpty) ...[
                const SizedBox(height: 8.0),
                Text(
                  walkabout.description!,
                  style: Theme.of(context).textTheme.bodyMedium,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
              
              const SizedBox(height: 12.0),
              
              // Metadata row
              Row(
                children: [
                  Icon(
                    Icons.access_time,
                    size: 16.0,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  const SizedBox(width: 4.0),
                  Text(
                    _formatDate(walkabout.createdAt),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(width: 16.0),
                  if (walkabout.location != null) ...[
                    Icon(
                      Icons.location_on,
                      size: 16.0,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4.0),
                    Text(
                      'Location set',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                  const Spacer(),
                  _buildSyncStatusIndicator(context),
                ],
              ),
              
              // Action buttons
              const SizedBox(height: 12.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (onStatusChanged != null)
                    _buildStatusButton(context),
                  const SizedBox(width: 8.0),
                  if (onDelete != null)
                    IconButton(
                      onPressed: onDelete,
                      icon: const Icon(Icons.delete_outline),
                      tooltip: 'Delete walkabout',
                      iconSize: 20.0,
                    ),
                  IconButton(
                    onPressed: onTap,
                    icon: const Icon(Icons.arrow_forward),
                    tooltip: 'View details',
                    iconSize: 20.0,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    final color = _getStatusColor(context, walkabout.status);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _getStatusIcon(walkabout.status, size: 14.0, color: color),
          const SizedBox(width: 4.0),
          Text(
            walkabout.status.displayName,
            style: TextStyle(
              color: color,
              fontSize: 12.0,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSyncStatusIndicator(BuildContext context) {
    final syncColor = _getSyncStatusColor(context, walkabout.syncStatus);
    
    return Tooltip(
      message: walkabout.syncStatus.description,
      child: Container(
        width: 8.0,
        height: 8.0,
        decoration: BoxDecoration(
          color: syncColor,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  Widget _buildStatusButton(BuildContext context) {
    return PopupMenuButton<WalkaboutStatus>(
      onSelected: onStatusChanged,
      itemBuilder: (context) => WalkaboutStatus.values.map((status) {
        return PopupMenuItem(
          value: status,
          enabled: status != walkabout.status,
          child: Row(
            children: [
              _getStatusIcon(status, size: 16.0),
              const SizedBox(width: 8.0),
              Text(status.displayName),
            ],
          ),
        );
      }).toList(),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        decoration: BoxDecoration(
          border: Border.all(
            color: Theme.of(context).colorScheme.outline,
          ),
          borderRadius: BorderRadius.circular(16.0),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Change Status',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(width: 4.0),
            Icon(
              Icons.arrow_drop_down,
              size: 16.0,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  Widget _getStatusIcon(WalkaboutStatus status, {double size = 16.0, Color? color}) {
    IconData iconData;
    switch (status) {
      case WalkaboutStatus.draft:
        iconData = Icons.edit_note;
        break;
      case WalkaboutStatus.inProgress:
        iconData = Icons.play_circle;
        break;
      case WalkaboutStatus.completed:
        iconData = Icons.check_circle;
        break;
      case WalkaboutStatus.archived:
        iconData = Icons.archive;
        break;
    }
    
    return Icon(iconData, size: size, color: color);
  }

  Color _getStatusColor(BuildContext context, WalkaboutStatus status) {
    switch (status) {
      case WalkaboutStatus.draft:
        return Colors.orange;
      case WalkaboutStatus.inProgress:
        return Colors.blue;
      case WalkaboutStatus.completed:
        return Colors.green;
      case WalkaboutStatus.archived:
        return Colors.grey;
    }
  }

  Color _getSyncStatusColor(BuildContext context, SyncStatus syncStatus) {
    switch (syncStatus) {
      case SyncStatus.local:
        return Colors.orange;
      case SyncStatus.syncing:
        return Colors.blue;
      case SyncStatus.synced:
        return Colors.green;
      case SyncStatus.error:
        return Colors.red;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
