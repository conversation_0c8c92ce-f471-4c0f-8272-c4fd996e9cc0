// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:sqflite/sqflite.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_core_platform_interface/firebase_core_platform_interface.dart';

import 'package:safestride/main.dart';

// Generate mock for Database
@GenerateMocks([Database])
import 'widget_test.mocks.dart';

void setupFirebaseMocks() {
  TestWidgetsFlutterBinding.ensureInitialized();
  FirebasePlatform.instance = MockFirebasePlatform();
}

class MockFirebasePlatform extends FirebasePlatform {
  MockFirebasePlatform() : super();

  @override
  Future<FirebaseAppPlatform> initializeApp({
    String? name,
    FirebaseOptions? options,
  }) async {
    return MockFirebaseApp(name: name, options: options);
  }

  @override
  FirebaseAppPlatform app([String name = defaultFirebaseAppName]) {
    return MockFirebaseApp(
      name: name,
      options: const FirebaseOptions(
        apiKey: 'testApiKey',
        appId: 'testAppId',
        messagingSenderId: 'testSenderId',
        projectId: 'testProjectId',
      ),
    );
  }

  @override
  List<FirebaseAppPlatform> get apps => [app()];
}

class MockFirebaseApp extends FirebaseAppPlatform {
  MockFirebaseApp({
    String? name,
    FirebaseOptions? options,
  }) : super(
          name ?? defaultFirebaseAppName,
          options ??
              const FirebaseOptions(
                apiKey: 'testApiKey',
                appId: 'testAppId',
                messagingSenderId: 'testSenderId',
                projectId: 'testProjectId',
              ),
        );
}

void main() {
  late MockDatabase mockDatabase;

  setUpAll(() async {
    setupFirebaseMocks();
    await Firebase.initializeApp(
      options: const FirebaseOptions(
        apiKey: 'testApiKey',
        appId: 'testAppId',
        messagingSenderId: 'testSenderId',
        projectId: 'testProjectId',
      ),
    );
  });

  setUp(() {
    mockDatabase = MockDatabase();
    // Mock the isOpen property to return true
    when(mockDatabase.isOpen).thenReturn(true);
  });

  group('SafeStride App Tests', () {
    testWidgets('App should build without errors', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(MyApp(database: mockDatabase));

      // Verify that the app builds successfully
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('App should have correct title', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(MyApp(database: mockDatabase));

      // Verify that the app has the correct title
      final MaterialApp app = tester.widget(find.byType(MaterialApp));
      expect(app.title, 'SafeStride');
    });
  });
}
