import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../../models/user_model.dart';
import '../../../domain/entities/user.dart' as domain;

/// Interface for local user data operations
abstract class UserLocalDataSource {
  /// Get user by ID from local cache
  Future<domain.User?> getCachedUser(String uid);

  /// Cache user data locally
  Future<void> cacheUser(domain.User user);

  /// Check if user needs syncing with remote
  Future<bool> needsSync(String uid);

  /// Mark user for syncing with remote
  Future<void> markForSync(String uid);

  /// Clear sync flag for user
  Future<void> clearSyncFlag(String uid);

  /// Check if user cache is valid
  Future<bool> hasValidCache(String uid);
}

/// Implementation of UserLocalDataSource using SQLite
class UserLocalDataSourceImpl implements UserLocalDataSource {
  final Database _database;
  
  // Cache expiry duration (30 days)
  static const _cacheExpiryDuration = Duration(days: 30);

  UserLocalDataSourceImpl(this._database);

  /// Create users table in database
  static Future<void> createTable(Database db) async {
    await db.execute(
      '''
      CREATE TABLE IF NOT EXISTS users(
        uid TEXT PRIMARY KEY,
        id TEXT,
        email TEXT NOT NULL,
        display_name TEXT,
        photo_url TEXT,
        organization TEXT,
        role TEXT NOT NULL,
        preferences TEXT,
        created_at INTEGER NOT NULL,
        updated_at INTEGER NOT NULL,
        last_login_at INTEGER,
        needs_sync INTEGER DEFAULT 0,
        cache_time INTEGER NOT NULL
      )
      ''',
    );
  }

  @override
  Future<domain.User?> getCachedUser(String uid) async {
    try {
      // Query user from database
      final result = await _database.query(
        'users',
        where: 'uid = ?',
        whereArgs: [uid],
      );

      if (result.isEmpty) {
        return null;
      }

      // Check if cache is valid
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(
        result.first['cache_time'] as int,
      );
      
      final now = DateTime.now();
      if (now.difference(cacheTime) > _cacheExpiryDuration) {
        // Cache expired
        return null;
      }

      // Convert database row to UserModel
      return UserModelSQLite.fromMap(result.first);
    } catch (e) {
      throw Exception('Failed to get cached user: ${e.toString()}');
    }
  }

  @override
  Future<void> cacheUser(domain.User user) async {
    try {
      // Convert domain User to UserModel
      final userModel = user is UserModel 
          ? user 
          : UserModel.fromEntity(user);
      
      // Convert to SQLite map
      final userData = userModel.toMap();

      // Insert or update user in database
      await _database.insert(
        'users',
        userData,
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to cache user: ${e.toString()}');
    }
  }

  @override
  Future<bool> needsSync(String uid) async {
    try {
      // Query sync flag from database
      final result = await _database.query(
        'users',
        columns: ['needs_sync'],
        where: 'uid = ?',
        whereArgs: [uid],
      );

      if (result.isEmpty) {
        return false;
      }

      return (result.first['needs_sync'] as int) == 1;
    } catch (e) {
      throw Exception('Failed to check sync status: ${e.toString()}');
    }
  }

  @override
  Future<void> markForSync(String uid) async {
    try {
      // Update sync flag in database
      await _database.update(
        'users',
        {'needs_sync': 1},
        where: 'uid = ?',
        whereArgs: [uid],
      );
    } catch (e) {
      throw Exception('Failed to mark user for sync: ${e.toString()}');
    }
  }

  @override
  Future<void> clearSyncFlag(String uid) async {
    try {
      // Clear sync flag in database
      await _database.update(
        'users',
        {'needs_sync': 0},
        where: 'uid = ?',
        whereArgs: [uid],
      );
    } catch (e) {
      throw Exception('Failed to clear sync flag: ${e.toString()}');
    }
  }

  @override
  Future<bool> hasValidCache(String uid) async {
    try {
      // Query user from database
      final result = await _database.query(
        'users',
        columns: ['cache_time'],
        where: 'uid = ?',
        whereArgs: [uid],
      );

      if (result.isEmpty) {
        return false;
      }

      // Check if cache is valid
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(
        result.first['cache_time'] as int,
      );
      
      final now = DateTime.now();
      return now.difference(cacheTime) <= _cacheExpiryDuration;
    } catch (e) {
      throw Exception('Failed to check cache validity: ${e.toString()}');
    }
  }
}