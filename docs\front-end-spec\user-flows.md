# User Flows

## New Walkabout Creation

**User Goal:** Start a new safety inspection walkabout

**Entry Points:** Dashboard FAB, Walkabouts tab

**Success Criteria:** Walkabout created and ready for hazard documentation

### Flow Diagram

```mermaid
graph TD
    Start[Dashboard] --> Click[Tap New Walkabout]
    Click --> Type{Select Type}
    Type -->|Solo| Area[Select Area]
    Type -->|Team| Team[Add Team Members]
    Team --> Area
    Area --> Template[Choose Template]
    Template --> Confirm[Confirm Details]
    Confirm --> Begin[Begin Walkabout]
    Begin --> Ready[Ready for Hazards]
```

**Edge Cases & Error Handling:**
- Offline mode: Cache template selection
- Team unavailable: Allow provisional assignment
- Area not listed: Support custom area entry

## Hazard Documentation

**User Goal:** Document a safety hazard during walkabout

**Entry Points:** Active walkabout, Quick action FAB

**Success Criteria:** Hazard recorded with required details

### Flow Diagram

```mermaid
graph TD
    Start[Active Walkabout] --> Add[Add Hazard]
    Add --> Photo[Take Photo]
    Photo --> Optional{More Photos?}
    Optional -->|Yes| Photo
    Optional -->|No| Details[Add Details]
    Details --> Severity[Set Severity]
    Severity --> Location[Tag Location]
    Location --> Save[Save Hazard]
    Save --> Sync{Network Available?}
    Sync -->|Yes| Upload[Upload to Server]
    Sync -->|No| Queue[Queue for Sync]
```

**Edge Cases & Error Handling:**
- Camera unavailable: Support photo upload
- Low storage: Compress images
- Sync conflict: Show merge options
