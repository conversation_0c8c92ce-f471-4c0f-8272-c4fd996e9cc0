import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:sqflite/sqflite.dart';
import 'package:get_it/get_it.dart';

import '../../data/datasources/local/user_local_datasource.dart';
import '../../data/datasources/remote/user_remote_datasource.dart';
import '../../data/datasources/local/walkabout_local_datasource.dart';
import '../../data/repositories/user_repository_impl.dart';
import '../../data/repositories/walkabout_repository_impl.dart';
import '../../domain/repositories/user_repository.dart';
import '../../domain/repositories/walkabout_repository.dart';
import '../../domain/usecases/get_user_by_id.dart';
import '../../domain/usecases/update_user.dart';
import '../../domain/usecases/update_user_preferences.dart';
import '../../domain/usecases/update_user_role.dart';
import '../../domain/usecases/sync_user.dart';
import '../../domain/usecases/create_walkabout.dart';
import '../../presentation/providers/user_profile_provider.dart';
import '../../presentation/providers/walkabout_provider.dart';
import 'profile_sync_service.dart';

final serviceLocator = GetIt.instance;

/// Initialize service locator
Future<void> initServiceLocator({
  FirebaseAuth? firebaseAuth,
  FirebaseFirestore? firestore,
  Database? database,
  Connectivity? connectivity,
}) async {
  // Firebase
  serviceLocator.registerLazySingleton<FirebaseAuth>(
    () => firebaseAuth ?? FirebaseAuth.instance,
  );
  serviceLocator.registerLazySingleton<FirebaseFirestore>(
    () => firestore ?? FirebaseFirestore.instance,
  );

  // Database
  serviceLocator.registerLazySingleton<Database>(
    () => database!, // Database must be provided for testing
  );

  // Connectivity
  serviceLocator.registerLazySingleton<Connectivity>(
    () => connectivity ?? Connectivity(),
  );

  // Data sources
  serviceLocator.registerLazySingleton<UserRemoteDataSource>(
    () => UserRemoteDataSourceImpl(
      firestore: serviceLocator<FirebaseFirestore>(),
      firebaseAuth: serviceLocator<FirebaseAuth>(),
    ),
  );
  serviceLocator.registerLazySingleton<UserLocalDataSource>(
    () => UserLocalDataSourceImpl(serviceLocator<Database>()),
  );
  serviceLocator.registerLazySingleton<WalkaboutLocalDataSource>(
    () => WalkaboutLocalDataSourceImpl(serviceLocator<Database>()),
  );

  // Repositories
  serviceLocator.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(
      remoteDataSource: serviceLocator<UserRemoteDataSource>(),
      localDataSource: serviceLocator<UserLocalDataSource>(),
    ),
  );
  serviceLocator.registerLazySingleton<WalkaboutRepository>(
    () => WalkaboutRepositoryImpl(
      localDataSource: serviceLocator<WalkaboutLocalDataSource>(),
    ),
  );

  // Use cases
  serviceLocator.registerLazySingleton<GetUserByIdUseCase>(
    () => GetUserByIdUseCase(serviceLocator<UserRepository>()),
  );
  serviceLocator.registerLazySingleton<UpdateUserUseCase>(
    () => UpdateUserUseCase(serviceLocator<UserRepository>()),
  );
  serviceLocator.registerLazySingleton<UpdateUserPreferencesUseCase>(
    () => UpdateUserPreferencesUseCase(serviceLocator<UserRepository>()),
  );
  serviceLocator.registerLazySingleton<UpdateUserRoleUseCase>(
    () => UpdateUserRoleUseCase(serviceLocator<UserRepository>()),
  );
  serviceLocator.registerLazySingleton<SyncUserUseCase>(
    () => SyncUserUseCase(serviceLocator<UserRepository>()),
  );
  serviceLocator.registerLazySingleton<CreateWalkaboutUseCase>(
    () => CreateWalkaboutUseCase(
      repository: serviceLocator<WalkaboutRepository>(),
    ),
  );

  // Providers
  serviceLocator.registerFactory<UserProfileProvider>(
    () => UserProfileProvider(
      getUserByIdUseCase: serviceLocator<GetUserByIdUseCase>(),
      updateUserUseCase: serviceLocator<UpdateUserUseCase>(),
      updateUserPreferencesUseCase:
          serviceLocator<UpdateUserPreferencesUseCase>(),
      updateUserRoleUseCase: serviceLocator<UpdateUserRoleUseCase>(),
      syncUserUseCase: serviceLocator<SyncUserUseCase>(),
    ),
  );
  serviceLocator.registerFactory<WalkaboutProvider>(
    () => WalkaboutProvider(
      createWalkaboutUseCase: serviceLocator<CreateWalkaboutUseCase>(),
      walkaboutRepository: serviceLocator<WalkaboutRepository>(),
    ),
  );

  // Services
  serviceLocator.registerLazySingleton<ProfileSyncService>(
    () => ProfileSyncService(
      userRepository: serviceLocator<UserRepository>(),
      syncUserUseCase: serviceLocator<SyncUserUseCase>(),
      connectivity: serviceLocator<Connectivity>(),
    ),
  );
}
