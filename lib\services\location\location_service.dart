import 'package:geolocator/geolocator.dart';
import '../../domain/entities/walkabout.dart';

/// Service for handling location operations and GPS functionality
/// 
/// This service provides functionality for getting current location,
/// checking permissions, and managing location settings for hazard documentation.
abstract class LocationService {
  /// Get current location
  Future<GeoPoint> getCurrentLocation();

  /// Check if location services are enabled
  Future<bool> isLocationServiceEnabled();

  /// Check location permissions
  Future<LocationPermissionStatus> checkLocationPermission();

  /// Request location permissions
  Future<LocationPermissionStatus> requestLocationPermission();

  /// Get location with custom accuracy
  Future<GeoPoint> getLocationWithAccuracy(LocationAccuracy accuracy);

  /// Calculate distance between two points in meters
  double calculateDistance(GeoPoint point1, GeoPoint point2);

  /// Check if a point is within radius of another point
  bool isWithinRadius(GeoPoint center, GeoPoint point, double radiusMeters);

  /// Get location settings
  Future<LocationSettings> getLocationSettings();

  /// Open location settings
  Future<bool> openLocationSettings();
}

/// Implementation of LocationService using geolocator plugin
class LocationServiceImpl implements LocationService {
  
  @override
  Future<GeoPoint> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      if (!await isLocationServiceEnabled()) {
        throw LocationException('Location services are disabled');
      }

      // Check permissions
      final permission = await checkLocationPermission();
      if (permission == LocationPermissionStatus.denied) {
        final requestResult = await requestLocationPermission();
        if (requestResult == LocationPermissionStatus.denied) {
          throw LocationException('Location permission denied');
        }
      }

      if (permission == LocationPermissionStatus.deniedForever) {
        throw LocationException('Location permission permanently denied');
      }

      // Get current position
      final Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return GeoPoint(
        latitude: position.latitude,
        longitude: position.longitude,
      );
    } catch (e) {
      if (e is LocationException) {
        rethrow;
      }
      throw LocationException('Failed to get current location: ${e.toString()}');
    }
  }

  @override
  Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      throw LocationException('Failed to check location service status: ${e.toString()}');
    }
  }

  @override
  Future<LocationPermissionStatus> checkLocationPermission() async {
    try {
      final permission = await Geolocator.checkPermission();
      return _mapGeolocatorPermission(permission);
    } catch (e) {
      throw LocationException('Failed to check location permission: ${e.toString()}');
    }
  }

  @override
  Future<LocationPermissionStatus> requestLocationPermission() async {
    try {
      final permission = await Geolocator.requestPermission();
      return _mapGeolocatorPermission(permission);
    } catch (e) {
      throw LocationException('Failed to request location permission: ${e.toString()}');
    }
  }

  @override
  Future<GeoPoint> getLocationWithAccuracy(LocationAccuracy accuracy) async {
    try {
      // Check if location services are enabled
      if (!await isLocationServiceEnabled()) {
        throw LocationException('Location services are disabled');
      }

      // Check permissions
      final permission = await checkLocationPermission();
      if (permission == LocationPermissionStatus.denied) {
        final requestResult = await requestLocationPermission();
        if (requestResult == LocationPermissionStatus.denied) {
          throw LocationException('Location permission denied');
        }
      }

      if (permission == LocationPermissionStatus.deniedForever) {
        throw LocationException('Location permission permanently denied');
      }

      // Get position with specified accuracy
      final Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: accuracy,
        timeLimit: const Duration(seconds: 15),
      );

      return GeoPoint(
        latitude: position.latitude,
        longitude: position.longitude,
      );
    } catch (e) {
      if (e is LocationException) {
        rethrow;
      }
      throw LocationException('Failed to get location with accuracy: ${e.toString()}');
    }
  }

  @override
  double calculateDistance(GeoPoint point1, GeoPoint point2) {
    try {
      return Geolocator.distanceBetween(
        point1.latitude,
        point1.longitude,
        point2.latitude,
        point2.longitude,
      );
    } catch (e) {
      throw LocationException('Failed to calculate distance: ${e.toString()}');
    }
  }

  @override
  bool isWithinRadius(GeoPoint center, GeoPoint point, double radiusMeters) {
    try {
      final distance = calculateDistance(center, point);
      return distance <= radiusMeters;
    } catch (e) {
      throw LocationException('Failed to check radius: ${e.toString()}');
    }
  }

  @override
  Future<LocationSettings> getLocationSettings() async {
    try {
      // Return default location settings for high accuracy
      return const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
        timeLimit: Duration(seconds: 10),
      );
    } catch (e) {
      throw LocationException('Failed to get location settings: ${e.toString()}');
    }
  }

  @override
  Future<bool> openLocationSettings() async {
    try {
      return await Geolocator.openLocationSettings();
    } catch (e) {
      throw LocationException('Failed to open location settings: ${e.toString()}');
    }
  }

  /// Helper method to map Geolocator permission to our enum
  LocationPermissionStatus _mapGeolocatorPermission(LocationPermission permission) {
    switch (permission) {
      case LocationPermission.denied:
        return LocationPermissionStatus.denied;
      case LocationPermission.deniedForever:
        return LocationPermissionStatus.deniedForever;
      case LocationPermission.whileInUse:
        return LocationPermissionStatus.whileInUse;
      case LocationPermission.always:
        return LocationPermissionStatus.always;
      case LocationPermission.unableToDetermine:
        return LocationPermissionStatus.denied;
    }
  }
}

/// Location permission status enumeration
enum LocationPermissionStatus {
  denied,
  deniedForever,
  whileInUse,
  always;

  String get displayName {
    switch (this) {
      case LocationPermissionStatus.denied:
        return 'Denied';
      case LocationPermissionStatus.deniedForever:
        return 'Permanently Denied';
      case LocationPermissionStatus.whileInUse:
        return 'While In Use';
      case LocationPermissionStatus.always:
        return 'Always';
    }
  }

  String get description {
    switch (this) {
      case LocationPermissionStatus.denied:
        return 'Location access is denied';
      case LocationPermissionStatus.deniedForever:
        return 'Location access is permanently denied';
      case LocationPermissionStatus.whileInUse:
        return 'Location access granted while app is in use';
      case LocationPermissionStatus.always:
        return 'Location access always granted';
    }
  }

  bool get isGranted {
    return this == LocationPermissionStatus.whileInUse || 
           this == LocationPermissionStatus.always;
  }
}

/// Exception thrown by location operations
class LocationException implements Exception {
  final String message;
  
  const LocationException(this.message);
  
  @override
  String toString() => 'LocationException: $message';
}
