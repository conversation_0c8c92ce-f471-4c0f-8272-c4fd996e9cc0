import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/usecases/get_user_by_id.dart';
import 'package:safestride/domain/usecases/sync_user.dart';
import 'package:safestride/domain/usecases/update_user.dart';
import 'package:safestride/domain/usecases/update_user_preferences.dart';
import 'package:safestride/domain/usecases/update_user_role.dart';
import 'package:safestride/presentation/providers/user_profile_provider.dart';

import 'user_profile_provider_test.mocks.dart';

@GenerateMocks([
  GetUserByIdUseCase,
  UpdateUserUseCase,
  UpdateUserPreferencesUseCase,
  UpdateUserRoleUseCase,
  SyncUserUseCase,
])
void main() {
  late UserProfileProvider provider;
  late MockGetUserByIdUseCase mockGetUserByIdUseCase;
  late MockUpdateUserUseCase mockUpdateUserUseCase;
  late MockUpdateUserPreferencesUseCase mockUpdateUserPreferencesUseCase;
  late MockUpdateUserRoleUseCase mockUpdateUserRoleUseCase;
  late MockSyncUserUseCase mockSyncUserUseCase;

  setUp(() {
    mockGetUserByIdUseCase = MockGetUserByIdUseCase();
    mockUpdateUserUseCase = MockUpdateUserUseCase();
    mockUpdateUserPreferencesUseCase = MockUpdateUserPreferencesUseCase();
    mockUpdateUserRoleUseCase = MockUpdateUserRoleUseCase();
    mockSyncUserUseCase = MockSyncUserUseCase();
    
    provider = UserProfileProvider(
      getUserByIdUseCase: mockGetUserByIdUseCase,
      updateUserUseCase: mockUpdateUserUseCase,
      updateUserPreferencesUseCase: mockUpdateUserPreferencesUseCase,
      updateUserRoleUseCase: mockUpdateUserRoleUseCase,
      syncUserUseCase: mockSyncUserUseCase,
    );
  });

  group('UserProfileProvider', () {
    const testUid = 'test-uid-123';
    final testUser = User(
      id: testUid,
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime(2024, 1, 1),
      lastLoginAt: DateTime(2024, 1, 2),
      preferences: UserPreferences(),
    );

    group('Initial State', () {
      test('should have correct initial state', () {
        expect(provider.state, equals(UserProfileState.initial));
        expect(provider.user, isNull);
        expect(provider.errorMessage, isNull);
        expect(provider.isLoading, isFalse);
        expect(provider.isSyncing, isFalse);
      });
    });

    group('loadUserProfile', () {
      test('should load user profile successfully', () async {
        // Arrange
        when(mockGetUserByIdUseCase.call(testUid))
            .thenAnswer((_) async => testUser);

        // Act
        await provider.loadUserProfile(testUid);

        // Assert
        expect(provider.state, equals(UserProfileState.loaded));
        expect(provider.user, equals(testUser));
        expect(provider.errorMessage, isNull);
        expect(provider.isLoading, isFalse);
        verify(mockGetUserByIdUseCase.call(testUid)).called(1);
      });

      test('should handle user not found', () async {
        // Arrange
        when(mockGetUserByIdUseCase.call(testUid))
            .thenAnswer((_) async => null);

        // Act
        await provider.loadUserProfile(testUid);

        // Assert
        expect(provider.state, equals(UserProfileState.error));
        expect(provider.user, isNull);
        expect(provider.errorMessage, equals('User not found'));
        expect(provider.isLoading, isFalse);
        verify(mockGetUserByIdUseCase.call(testUid)).called(1);
      });

      test('should handle exception during load', () async {
        // Arrange
        when(mockGetUserByIdUseCase.call(testUid))
            .thenThrow(Exception('Network error'));

        // Act
        await provider.loadUserProfile(testUid);

        // Assert
        expect(provider.state, equals(UserProfileState.error));
        expect(provider.user, isNull);
        expect(provider.errorMessage, contains('Exception: Network error'));
        expect(provider.isLoading, isFalse);
        verify(mockGetUserByIdUseCase.call(testUid)).called(1);
      });

      test('should set loading state during operation', () async {
        // Arrange
        when(mockGetUserByIdUseCase.call(testUid))
            .thenAnswer((_) async {
          // Verify loading state is true during operation
          expect(provider.isLoading, isTrue);
          return testUser;
        });

        // Act
        await provider.loadUserProfile(testUid);

        // Assert
        expect(provider.isLoading, isFalse);
      });
    });

    group('updateProfile', () {
      setUp(() {
        // Set up provider with loaded user
        provider.loadUserProfile(testUid);
        when(mockGetUserByIdUseCase.call(testUid))
            .thenAnswer((_) async => testUser);
      });

      test('should update profile successfully', () async {
        // Arrange
        const newDisplayName = 'Updated User';
        const newOrganization = 'Updated Org';
        final updatedUser = testUser.copyWith(
          displayName: newDisplayName,
          organization: newOrganization,
        );
        
        when(mockUpdateUserUseCase.call(any))
            .thenAnswer((_) async => updatedUser);

        // Load user first
        await provider.loadUserProfile(testUid);

        // Act
        await provider.updateProfile(
          displayName: newDisplayName,
          organization: newOrganization,
        );

        // Assert
        expect(provider.state, equals(UserProfileState.loaded));
        expect(provider.user?.displayName, equals(newDisplayName));
        expect(provider.user?.organization, equals(newOrganization));
        expect(provider.errorMessage, isNull);
        verify(mockUpdateUserUseCase.call(any)).called(1);
      });

      test('should handle error when no user loaded', () async {
        // Act
        await provider.updateProfile(
          displayName: 'New Name',
          organization: 'New Org',
        );

        // Assert
        expect(provider.state, equals(UserProfileState.error));
        expect(provider.errorMessage, equals('No user loaded'));
        verifyNever(mockUpdateUserUseCase.call(any));
      });

      test('should handle exception during update', () async {
        // Arrange
        when(mockUpdateUserUseCase.call(any))
            .thenThrow(Exception('Update failed'));

        // Load user first
        await provider.loadUserProfile(testUid);

        // Act
        await provider.updateProfile(
          displayName: 'New Name',
          organization: 'New Org',
        );

        // Assert
        expect(provider.state, equals(UserProfileState.error));
        expect(provider.errorMessage, contains('Exception: Update failed'));
        verify(mockUpdateUserUseCase.call(any)).called(1);
      });
    });

    group('updatePreferences', () {
      test('should update preferences successfully', () async {
        // Arrange
        final newPreferences = UserPreferences(
          notificationsEnabled: false,
          language: 'es',
          theme: 'dark',
          offlineMode: true,
        );
        final updatedUser = testUser.copyWith(preferences: newPreferences);
        
        when(mockUpdateUserPreferencesUseCase.call(
          uid: testUid,
          preferences: any,
        )).thenAnswer((_) async => updatedUser);

        // Load user first
        await provider.loadUserProfile(testUid);

        // Act
        await provider.updatePreferences(
          notificationsEnabled: false,
          language: 'es',
          theme: 'dark',
          offlineMode: true,
        );

        // Assert
        expect(provider.state, equals(UserProfileState.loaded));
        expect(provider.user?.preferences.language, equals('es'));
        expect(provider.user?.preferences.theme, equals('dark'));
        expect(provider.user?.preferences.notificationsEnabled, isFalse);
        expect(provider.user?.preferences.offlineMode, isTrue);
        verify(mockUpdateUserPreferencesUseCase.call(
          uid: testUid,
          preferences: any,
        )).called(1);
      });

      test('should handle error when no user loaded', () async {
        // Act
        await provider.updatePreferences(
          notificationsEnabled: true,
          language: 'en',
          theme: 'light',
          offlineMode: false,
        );

        // Assert
        expect(provider.state, equals(UserProfileState.error));
        expect(provider.errorMessage, equals('No user loaded'));
        verifyNever(mockUpdateUserPreferencesUseCase.call(
          uid: any,
          preferences: any,
        ));
      });
    });

    group('updateRole', () {
      test('should update role successfully', () async {
        // Arrange
        const newRole = UserRole.manager;
        final updatedUser = testUser.copyWith(role: newRole);
        
        when(mockUpdateUserRoleUseCase.call(
          uid: testUid,
          role: newRole,
        )).thenAnswer((_) async => updatedUser);

        // Load user first
        await provider.loadUserProfile(testUid);

        // Act
        await provider.updateRole(newRole);

        // Assert
        expect(provider.state, equals(UserProfileState.loaded));
        expect(provider.user?.role, equals(newRole));
        verify(mockUpdateUserRoleUseCase.call(
          uid: testUid,
          role: newRole,
        )).called(1);
      });

      test('should handle error when no user loaded', () async {
        // Act
        await provider.updateRole(UserRole.manager);

        // Assert
        expect(provider.state, equals(UserProfileState.error));
        expect(provider.errorMessage, equals('No user loaded'));
        verifyNever(mockUpdateUserRoleUseCase.call(
          uid: any,
          role: any,
        ));
      });
    });

    group('syncUserData', () {
      test('should sync user data successfully', () async {
        // Arrange
        final syncedUser = testUser.copyWith(
          displayName: 'Synced User',
          updatedAt: DateTime(2024, 1, 3),
        );
        
        when(mockSyncUserUseCase.call(testUid))
            .thenAnswer((_) async => syncedUser);

        // Load user first
        await provider.loadUserProfile(testUid);

        // Act
        await provider.syncUserData();

        // Assert
        expect(provider.state, equals(UserProfileState.loaded));
        expect(provider.user?.displayName, equals('Synced User'));
        expect(provider.isSyncing, isFalse);
        verify(mockSyncUserUseCase.call(testUid)).called(1);
      });

      test('should handle error when no user loaded', () async {
        // Act
        await provider.syncUserData();

        // Assert
        expect(provider.state, equals(UserProfileState.error));
        expect(provider.errorMessage, equals('No user loaded'));
        verifyNever(mockSyncUserUseCase.call(any));
      });

      test('should set syncing state during operation', () async {
        // Arrange
        when(mockSyncUserUseCase.call(testUid))
            .thenAnswer((_) async {
          // Verify syncing state is true during operation
          expect(provider.isSyncing, isTrue);
          return testUser;
        });

        // Load user first
        await provider.loadUserProfile(testUid);

        // Act
        await provider.syncUserData();

        // Assert
        expect(provider.isSyncing, isFalse);
      });
    });

    group('refreshProfile', () {
      test('should refresh profile successfully', () async {
        // Arrange
        final refreshedUser = testUser.copyWith(
          displayName: 'Refreshed User',
          updatedAt: DateTime(2024, 1, 3),
        );
        
        when(mockGetUserByIdUseCase.call(testUid))
            .thenAnswer((_) async => testUser)
            .thenAnswer((_) async => refreshedUser);

        // Load user first
        await provider.loadUserProfile(testUid);

        // Act
        await provider.refreshProfile();

        // Assert
        expect(provider.state, equals(UserProfileState.loaded));
        expect(provider.user?.displayName, equals('Refreshed User'));
        verify(mockGetUserByIdUseCase.call(testUid)).called(2);
      });

      test('should handle error when no user loaded', () async {
        // Act
        await provider.refreshProfile();

        // Assert
        expect(provider.state, equals(UserProfileState.error));
        expect(provider.errorMessage, equals('No user loaded'));
        verifyNever(mockGetUserByIdUseCase.call(any));
      });

      test('should handle user not found during refresh', () async {
        // Arrange
        when(mockGetUserByIdUseCase.call(testUid))
            .thenAnswer((_) async => testUser)
            .thenAnswer((_) async => null);

        // Load user first
        await provider.loadUserProfile(testUid);

        // Act
        await provider.refreshProfile();

        // Assert
        expect(provider.state, equals(UserProfileState.error));
        expect(provider.errorMessage, equals('User not found'));
        verify(mockGetUserByIdUseCase.call(testUid)).called(2);
      });
    });

    group('State Management', () {
      test('should notify listeners when state changes', () async {
        // Arrange
        var notificationCount = 0;
        provider.addListener(() {
          notificationCount++;
        });

        when(mockGetUserByIdUseCase.call(testUid))
            .thenAnswer((_) async => testUser);

        // Act
        await provider.loadUserProfile(testUid);

        // Assert
        expect(notificationCount, greaterThan(0));
      });

      test('should clear error when starting new operation', () async {
        // Arrange
        when(mockGetUserByIdUseCase.call(testUid))
            .thenThrow(Exception('First error'))
            .thenAnswer((_) async => testUser);

        // Act - First call to set error
        await provider.loadUserProfile(testUid);
        expect(provider.errorMessage, isNotNull);

        // Act - Second call should clear error
        await provider.loadUserProfile(testUid);

        // Assert
        expect(provider.errorMessage, isNull);
        expect(provider.state, equals(UserProfileState.loaded));
      });
    });
  });
}