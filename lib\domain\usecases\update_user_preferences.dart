import '../entities/user.dart';
import '../repositories/user_repository.dart';

/// Use case for updating a user's preferences
class UpdateUserPreferencesUseCase {
  final UserRepository _userRepository;

  const UpdateUserPreferencesUseCase(this._userRepository);

  /// Execute updating user preferences
  /// 
  /// Returns: Updated [User] object with new preferences
  /// Throws: Exception if operation fails
  Future<User> call({
    required String uid,
    required UserPreferences preferences,
  }) async {
    try {
      return await _userRepository.updateUserPreferences(uid, preferences);
    } catch (e) {
      throw Exception('Failed to update user preferences: ${e.toString()}');
    }
  }
}