import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/get_user_by_id.dart';
import '../../domain/usecases/update_user.dart';
import '../../domain/usecases/update_user_preferences.dart';
import '../../domain/usecases/update_user_role.dart';
import '../../domain/usecases/sync_user.dart';
import '../../services/sync/profile_sync_service.dart';

/// State for user profile operations
enum UserProfileState { initial, loading, loaded, error }

/// Provider for managing user profile state and operations
class UserProfileProvider extends ChangeNotifier {
  final GetUserByIdUseCase _getUserByIdUseCase;
  final UpdateUserUseCase _updateUserUseCase;
  final UpdateUserPreferencesUseCase _updateUserPreferencesUseCase;
  final UpdateUserRoleUseCase _updateUserRoleUseCase;
  final SyncUserUseCase _syncUserUseCase;

  UserProfileProvider({
    required GetUserByIdUseCase getUserByIdUseCase,
    required UpdateUserUseCase updateUserUseCase,
    required UpdateUserPreferencesUseCase updateUserPreferencesUseCase,
    required UpdateUserRoleUseCase updateUserRoleUseCase,
    required SyncUserUseCase syncUserUseCase,
  }) : _getUserByIdUseCase = getUserByIdUseCase,
       _updateUserUseCase = updateUserUseCase,
       _updateUserPreferencesUseCase = updateUserPreferencesUseCase,
       _updateUserRoleUseCase = updateUserRoleUseCase,
       _syncUserUseCase = syncUserUseCase;

  // Private fields
  UserProfileState _state = UserProfileState.initial;
  User? _user;
  String? _errorMessage;
  bool _isLoading = false;
  bool _isSyncing = false;

  // Public getters
  UserProfileState get state => _state;
  User? get user => _user;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isSyncing => _isSyncing;

  /// Load user profile by ID
  Future<void> loadUserProfile(String uid) async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _getUserByIdUseCase.call(uid);

      if (user != null) {
        _setLoadedState(user);
      } else {
        _setErrorState('User not found');
      }
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Update user profile
  Future<void> updateProfile({
    required String displayName,
    required String organization,
  }) async {
    try {
      if (_user == null) {
        _setErrorState('No user loaded');
        return;
      }

      _setLoading(true);
      _clearError();

      final updatedUser = _user!.copyWith(
        displayName: displayName,
        organization: organization,
      );

      final result = await _updateUserUseCase.call(updatedUser);
      _setLoadedState(result);

      // Queue user for sync
      try {
        final profileSyncService = GetIt.instance<ProfileSyncService>();
        await profileSyncService.queueForSync(result.uid);
      } catch (e) {
        print('Failed to queue user for sync: $e');
      }
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Update user preferences
  Future<void> updatePreferences({
    required bool notificationsEnabled,
    required String language,
    required String theme,
    required bool offlineMode,
  }) async {
    try {
      if (_user == null) {
        _setErrorState('No user loaded');
        return;
      }

      _setLoading(true);
      _clearError();

      final preferences = UserPreferences(
        notificationsEnabled: notificationsEnabled,
        language: language,
        theme: theme,
        offlineMode: offlineMode,
      );

      final result = await _updateUserPreferencesUseCase.call(
        uid: _user!.uid,
        preferences: preferences,
      );

      _setLoadedState(result);

      // Queue user for sync
      try {
        final profileSyncService = GetIt.instance<ProfileSyncService>();
        await profileSyncService.queueForSync(result.uid);
      } catch (e) {
        print('Failed to queue user for sync: $e');
      }
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Update user role
  Future<void> updateRole(UserRole role) async {
    try {
      if (_user == null) {
        _setErrorState('No user loaded');
        return;
      }

      _setLoading(true);
      _clearError();

      final result = await _updateUserRoleUseCase.call(
        uid: _user!.uid,
        role: role,
      );

      _setLoadedState(result);
      
      // Queue user for sync
      try {
        final profileSyncService = GetIt.instance<ProfileSyncService>();
        await profileSyncService.queueForSync(result.uid);
      } catch (e) {
        print('Failed to queue user for sync: $e');
      }
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Sync user data with remote server
  Future<void> syncUserData() async {
    try {
      if (_user == null) {
        _setErrorState('No user loaded');
        return;
      }

      _setSyncing(true);
      _clearError();

      User? result;
      
      // Try to use ProfileSyncService first
      try {
        final profileSyncService = GetIt.instance<ProfileSyncService>();
        result = await profileSyncService.syncNow(_user!.uid);
      } catch (e) {
        print('Failed to sync using ProfileSyncService: $e');
        // Fall back to direct sync
        result = await _syncUserUseCase.call(_user!.uid);
      }
      
      if (result != null) {
        _setLoadedState(result);
      }
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setSyncing(false);
    }
  }

  /// Refresh user profile
  Future<void> refreshProfile() async {
    try {
      if (_user == null) {
        _setErrorState('No user loaded');
        return;
      }

      _setLoading(true);
      _clearError();

      final user = await _getUserByIdUseCase.call(_user!.uid);

      if (user != null) {
        _setLoadedState(user);
      } else {
        _setErrorState('User not found');
      }
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setSyncing(bool syncing) {
    _isSyncing = syncing;
    notifyListeners();
  }

  void _setLoadedState(User user) {
    _state = UserProfileState.loaded;
    _user = user;
    notifyListeners();
  }

  void _setErrorState(String message) {
    _state = UserProfileState.error;
    _errorMessage = message;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }
}
