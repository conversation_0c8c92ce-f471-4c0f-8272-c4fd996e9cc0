import '../entities/hazard.dart';
import '../entities/walkabout.dart';
import '../repositories/hazard_repository.dart';

/// Use case for creating a new hazard
/// 
/// This use case encapsulates the business logic for hazard creation
/// following Clean Architecture principles.
class CreateHazardUseCase {
  final HazardRepository repository;

  const CreateHazardUseCase({required this.repository});

  /// Execute the create hazard use case
  /// 
  /// Takes a [CreateHazardParams] object containing the hazard details
  /// Returns the created hazard with generated ID and timestamps
  /// Throws [Exception] if creation fails or validation errors occur
  Future<Hazard> call(CreateHazardParams params) async {
    // Validate input parameters
    _validateParams(params);

    // Generate unique ID and timestamps
    final now = DateTime.now();
    final hazardId = _generateHazardId();

    // Create hazard entity
    final hazard = Hazard(
      id: hazardId,
      walkaboutId: params.walkaboutId,
      title: params.title,
      description: params.description,
      severity: params.severity,
      category: params.category,
      location: params.location,
      photos: List<String>.from(params.photos ?? []),
      notes: params.notes,
      createdAt: now,
      updatedAt: now,
      syncStatus: SyncStatus.local,
    );

    // Save hazard through repository
    return await repository.createHazard(hazard);
  }

  /// Validate input parameters
  void _validateParams(CreateHazardParams params) {
    if (params.title.trim().isEmpty) {
      throw ArgumentError('Hazard title cannot be empty');
    }

    if (params.title.length > 100) {
      throw ArgumentError('Hazard title cannot exceed 100 characters');
    }

    if (params.walkaboutId.trim().isEmpty) {
      throw ArgumentError('Walkabout ID is required');
    }

    if (params.description != null && params.description!.length > 1000) {
      throw ArgumentError('Hazard description cannot exceed 1000 characters');
    }

    if (params.notes != null && params.notes!.length > 500) {
      throw ArgumentError('Hazard notes cannot exceed 500 characters');
    }

    // Validate location coordinates if provided
    if (params.location != null) {
      final lat = params.location!.latitude;
      final lng = params.location!.longitude;
      
      if (lat < -90 || lat > 90) {
        throw ArgumentError('Invalid latitude: must be between -90 and 90');
      }
      
      if (lng < -180 || lng > 180) {
        throw ArgumentError('Invalid longitude: must be between -180 and 180');
      }
    }

    // Validate photo paths
    if (params.photos != null) {
      for (final photo in params.photos!) {
        if (photo.trim().isEmpty) {
          throw ArgumentError('Photo path cannot be empty');
        }
      }
    }
  }

  /// Generate a unique hazard ID
  /// 
  /// Uses timestamp and random component for uniqueness
  String _generateHazardId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 10000).toString().padLeft(4, '0');
    return 'hazard_${timestamp}_$random';
  }
}

/// Parameters for creating a hazard
class CreateHazardParams {
  final String walkaboutId;
  final String title;
  final String? description;
  final HazardSeverity severity;
  final HazardCategory category;
  final GeoPoint? location;
  final List<String>? photos;
  final String? notes;

  const CreateHazardParams({
    required this.walkaboutId,
    required this.title,
    this.description,
    required this.severity,
    required this.category,
    this.location,
    this.photos,
    this.notes,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CreateHazardParams &&
        other.walkaboutId == walkaboutId &&
        other.title == title &&
        other.description == description &&
        other.severity == severity &&
        other.category == category &&
        other.location == location &&
        _listEquals(other.photos, photos) &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return Object.hash(
      walkaboutId,
      title,
      description,
      severity,
      category,
      location,
      photos != null ? Object.hashAll(photos!) : null,
      notes,
    );
  }

  @override
  String toString() {
    return 'CreateHazardParams(walkaboutId: $walkaboutId, title: $title, '
        'description: $description, severity: $severity, category: $category, '
        'location: $location, photos: $photos, notes: $notes)';
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T>? a, List<T>? b) {
    if (a == null) return b == null;
    if (b == null || a.length != b.length) return false;
    for (int index = 0; index < a.length; index += 1) {
      if (a[index] != b[index]) return false;
    }
    return true;
  }
}
