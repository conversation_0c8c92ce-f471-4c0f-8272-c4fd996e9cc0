import 'dart:async';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/auth_repository.dart';
import 'package:safestride/domain/usecases/register_with_email.dart';

// Simple mock implementation for testing
class MockAuthRepository implements AuthRepository {
  User? _mockUser;
  Exception? _mockException;
  
  void setMockUser(User user) {
    _mockUser = user;
    _mockException = null;
  }
  
  void setMockException(Exception exception) {
    _mockException = exception;
    _mockUser = null;
  }

  @override
  Future<User> registerWithEmail({
    required String email,
    required String password,
    String? displayName,
    String? organization,
  }) async {
    if (_mockException != null) {
      throw _mockException!;
    }
    return _mockUser!;
  }

  @override
  Future<void> cacheCredentials(User user) async {
    // Mock implementation
  }

  @override
  Future<User> loginWithEmail({required String email, required String password}) async {
    throw UnimplementedError();
  }

  @override
  Future<User> loginWithSSO() async {
    throw UnimplementedError();
  }

  @override
  Future<void> logout() async {
    throw UnimplementedError();
  }

  @override
  Future<void> resetPassword({required String email}) async {
    throw UnimplementedError();
  }

  @override
  Future<User?> getCurrentUser() async {
    throw UnimplementedError();
  }

  @override
  Future<bool> isAuthenticated() async {
    throw UnimplementedError();
  }

  @override
  Stream<User?> get authStateChanges {
    throw UnimplementedError();
  }

  @override
  Future<void> refreshToken() async {
    throw UnimplementedError();
  }

  @override
  Future<User?> getCachedUser() async {
    throw UnimplementedError();
  }

  @override
  Future<void> clearCachedCredentials() async {
    throw UnimplementedError();
  }
}

void main() async {
  print('Testing RegisterWithEmailUseCase...');
  
  final mockRepository = MockAuthRepository();
  final useCase = RegisterWithEmailUseCase(mockRepository);
  
  final testUser = User(
    uid: '123',
    email: '<EMAIL>',
    displayName: 'Test User',
    organization: 'Test Org',
    role: UserRole.inspector,
    createdAt: DateTime.now(),
  );
  
  // Test successful registration
  try {
    mockRepository.setMockUser(testUser);
    final result = await useCase(
      email: '<EMAIL>',
      password: 'password123',
      displayName: 'Test User',
      organization: 'Test Org',
    );
    print('✓ Successful registration test passed');
    print('  Result: ${result.email}, ${result.displayName}');
  } catch (e) {
    print('✗ Successful registration test failed: $e');
  }
  
  // Test invalid email
  try {
    await useCase(
      email: 'invalid-email',
      password: 'password123',
      displayName: 'Test User',
      organization: 'Test Org',
    );
    print('✗ Invalid email test failed - should have thrown exception');
  } catch (e) {
    print('✓ Invalid email test passed - threw: ${e.runtimeType}');
  }
  
  // Test weak password
  try {
    await useCase(
      email: '<EMAIL>',
      password: '123',
      displayName: 'Test User',
      organization: 'Test Org',
    );
    print('✗ Weak password test failed - should have thrown exception');
  } catch (e) {
    print('✓ Weak password test passed - threw: ${e.runtimeType}');
  }
  
  // Test repository exception
  try {
    mockRepository.setMockException(Exception('Registration failed'));
    await useCase(
      email: '<EMAIL>',
      password: 'password123',
      displayName: 'Test User',
      organization: 'Test Org',
    );
    print('✗ Repository exception test failed - should have thrown exception');
  } catch (e) {
    print('✓ Repository exception test passed - threw: ${e.runtimeType}');
  }
  
  // Test empty display name
  try {
    mockRepository.setMockUser(testUser);
    await useCase(
      email: '<EMAIL>',
      password: 'password123',
      displayName: '',
      organization: 'Test Org',
    );
    print('✗ Empty display name test failed - should have thrown exception');
  } catch (e) {
    print('✓ Empty display name test passed - threw: ${e.runtimeType}');
  }
  
  print('\nAll tests completed!');
}