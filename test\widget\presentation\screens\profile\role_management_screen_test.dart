import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/presentation/providers/auth_provider.dart';
import 'package:safestride/presentation/providers/user_profile_provider.dart';
import 'package:safestride/presentation/screens/profile/role_management_screen.dart';
import 'package:safestride/presentation/widgets/app_bar_widget.dart';
import 'package:safestride/presentation/widgets/loading_indicator.dart';

import 'role_management_screen_test.mocks.dart';

@GenerateMocks([UserProfileProvider, AuthProvider])
void main() {
  late MockUserProfileProvider mockUserProfileProvider;
  late MockAuthProvider mockAuthProvider;

  setUp(() {
    mockUserProfileProvider = MockUserProfileProvider();
    mockAuthProvider = MockAuthProvider();
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: MultiProvider(
        providers: [
          ChangeNotifierProvider<UserProfileProvider>.value(
            value: mockUserProfileProvider,
          ),
          ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
          ),
        ],
        child: const RoleManagementScreen(),
      ),
    );
  }

  final testUser = User(
    id: 'test-uid-123',
    email: '<EMAIL>',
    displayName: 'Test User',
    organization: 'Test Organization',
    role: UserRole.inspector,
    createdAt: DateTime(2024, 1, 1),
    lastLoginAt: DateTime(2024, 1, 2),
    preferences: UserPreferences(
      notificationsEnabled: true,
      language: 'English',
      theme: 'Light',
      offlineMode: false,
    ),
  );

  final adminUser = User(
    id: 'admin-uid-456',
    email: '<EMAIL>',
    displayName: 'Admin User',
    organization: 'Test Organization',
    role: UserRole.admin,
    createdAt: DateTime(2024, 1, 1),
    lastLoginAt: DateTime(2024, 1, 2),
    preferences: UserPreferences(
      notificationsEnabled: true,
      language: 'English',
      theme: 'Light',
      offlineMode: false,
    ),
  );

  group('RoleManagementScreen Widget Tests', () {
    testWidgets('should display app bar with correct title', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow initialization

      // Assert
      expect(find.byType(AppBar), findsOneWidget);
      expect(find.text('Role Management'), findsOneWidget);
    });

    testWidgets('should display loading indicator when loading', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(true);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loading);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display user not found when user is null', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(null);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('User not found'), findsOneWidget);
    });

    testWidgets('should display access denied for non-admin users', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(testUser); // Non-admin user

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Access Denied'), findsOneWidget);
      expect(find.text('You need administrator privileges to manage roles.'), findsOneWidget);
      expect(find.byIcon(Icons.lock), findsOneWidget);
    });

    testWidgets('should display role management content for admin users', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('User Information'), findsOneWidget);
      expect(find.text('Role Assignment'), findsOneWidget);
    });

    testWidgets('should display user information card with correct data', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Test User'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('User ID'), findsOneWidget);
      expect(find.text('test-uid-123'), findsOneWidget);
      expect(find.text('Organization'), findsOneWidget);
      expect(find.text('Test Organization'), findsOneWidget);
      expect(find.text('Current Role'), findsOneWidget);
      expect(find.text('User'), findsOneWidget);
    });

    testWidgets('should display user avatar with first letter of display name', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.byType(CircleAvatar), findsOneWidget);
      expect(find.text('T'), findsOneWidget); // First letter of 'Test User'
    });

    testWidgets('should display role selection options', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Administrator'), findsOneWidget);
      expect(find.text('Full access to all features and settings'), findsOneWidget);
      expect(find.text('Inspector'), findsOneWidget);
      expect(find.text('Can manage content but not system settings'), findsOneWidget);
      expect(find.text('Moderator'), findsOneWidget);
      expect(find.text('Standard access to application features'), findsOneWidget);
      expect(find.byType(RadioListTile<UserRole>), findsNWidgets(3));
    });

    testWidgets('should display role icons with correct colors', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.byIcon(Icons.admin_panel_settings), findsOneWidget);
      expect(find.byIcon(Icons.security), findsOneWidget);
      expect(find.byIcon(Icons.person), findsOneWidget);
    });

    testWidgets('should initialize with current user role selected', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      final inspectorRoleRadio = tester.widget<RadioListTile<UserRole>>(
        find.widgetWithText(RadioListTile<UserRole>, 'Inspector'),
      );
      expect(inspectorRoleRadio.groupValue, equals(UserRole.inspector));
      expect(inspectorRoleRadio.value, equals(UserRole.inspector));
    });

    testWidgets('should select different role when tapped', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Tap on Administrator role
      await tester.tap(find.widgetWithText(RadioListTile<UserRole>, 'Administrator'));
      await tester.pump();

      // Assert
      final adminRoleRadio = tester.widget<RadioListTile<UserRole>>(
        find.widgetWithText(RadioListTile<UserRole>, 'Administrator'),
      );
      expect(adminRoleRadio.groupValue, equals(UserRole.admin));
    });

    testWidgets('should display action buttons', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Update Role'), findsOneWidget);
      expect(find.byType(OutlinedButton), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should disable update button when no role change', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      final updateButton = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(updateButton.onPressed, isNull);
    });

    testWidgets('should enable update button when role is changed', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Change role
      await tester.tap(find.widgetWithText(RadioListTile<UserRole>, 'Administrator'));
      await tester.pump();

      // Assert
      final updateButton = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(updateButton.onPressed, isNotNull);
    });

    testWidgets('should show confirmation dialog when updating role', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Change role and update
      await tester.tap(find.widgetWithText(RadioListTile<UserRole>, 'Administrator'));
      await tester.pump();
      await tester.tap(find.text('Update Role'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Confirm Role Change'), findsOneWidget);
      expect(find.text('Are you sure you want to change this user\'s role to Administrator?'), findsOneWidget);
      expect(find.text('This will modify their permissions and access level.'), findsOneWidget);
      expect(find.text('Change Role'), findsOneWidget);
    });

    testWidgets('should call updateRole when confirmed', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);
      when(mockUserProfileProvider.updateRole(any)).thenAnswer((_) async {});

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Change role, update, and confirm
      await tester.tap(find.widgetWithText(RadioListTile<UserRole>, 'Administrator'));
      await tester.pump();
      await tester.tap(find.text('Update Role'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Change Role'));
      await tester.pump();

      // Assert
      verify(mockUserProfileProvider.updateRole(UserRole.admin)).called(1);
    });

    testWidgets('should not call updateRole when cancelled in dialog', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Change role, update, and cancel
      await tester.tap(find.widgetWithText(RadioListTile<UserRole>, 'Administrator'));
      await tester.pump();
      await tester.tap(find.text('Update Role'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Cancel').last);
      await tester.pumpAndSettle();

      // Assert
      verifyNever(mockUserProfileProvider.updateRole(any));
      expect(find.text('Confirm Role Change'), findsNothing);
    });

    testWidgets('should show success message after successful role update', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);
      when(mockUserProfileProvider.updateRole(any)).thenAnswer((_) async {});

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Change role, update, and confirm
      await tester.tap(find.widgetWithText(RadioListTile<UserRole>, 'Administrator'));
      await tester.pump();
      await tester.tap(find.text('Update Role'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Change Role'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('User role updated to Administrator'), findsOneWidget);
    });

    testWidgets('should navigate back on cancel', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Assert - Should navigate back (screen should be popped)
      expect(find.byType(RoleManagementScreen), findsNothing);
    });

    testWidgets('should display error message when present', (WidgetTester tester) async {
      // Arrange
      const errorMessage = 'Failed to update role';
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.error);
      when(mockUserProfileProvider.errorMessage).thenReturn(errorMessage);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text(errorMessage), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should disable update button when loading', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(true);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loading);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Change role to enable button
      await tester.tap(find.widgetWithText(RadioListTile<UserRole>, 'Administrator'));
      await tester.pump();

      // Assert
      final updateButton = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(updateButton.onPressed, isNull);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should handle inspector role selection', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);
      when(mockUserProfileProvider.updateRole(any)).thenAnswer((_) async {});

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Select inspector role
      await tester.tap(find.widgetWithText(RadioListTile<UserRole>, 'Inspector'));
      await tester.pump();
      await tester.tap(find.text('Update Role'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Change Role'));
      await tester.pump();

      // Assert
      verify(mockUserProfileProvider.updateRole(UserRole.inspector)).called(1);
    });

    testWidgets('should handle user with empty display name', (WidgetTester tester) async {
      // Arrange
      final userWithEmptyName = User(
        id: 'test-uid-123',
        email: '<EMAIL>',
        displayName: '',
        organization: 'Test Organization',
        role: UserRole.inspector,
        createdAt: DateTime(2024, 1, 1),
        lastLoginAt: DateTime(2024, 1, 2),
        preferences: UserPreferences(
          notificationsEnabled: true,
          language: 'English',
          theme: 'Light',
          offlineMode: false,
        ),
      );
      
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(userWithEmptyName);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockAuthProvider.currentUser).thenReturn(adminUser);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('?'), findsOneWidget); // Avatar should show '?' for empty name
    });
  });
}