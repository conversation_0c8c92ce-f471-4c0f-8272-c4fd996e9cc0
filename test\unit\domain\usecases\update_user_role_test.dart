import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/user_repository.dart';
import 'package:safestride/domain/usecases/update_user_role.dart';

import 'update_user_role_test.mocks.dart';

@GenerateMocks([UserRepository])
void main() {
  late UpdateUserRoleUseCase useCase;
  late MockUserRepository mockUserRepository;

  setUp(() {
    mockUserRepository = MockUserRepository();
    useCase = UpdateUserRoleUseCase(mockUserRepository);
  });

  group('UpdateUserRoleUseCase', () {
    const testUid = 'test-uid-123';
    final testUser = User(
      id: testUid,
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime(2024, 1, 1),
      lastLoginAt: DateTime(2024, 1, 2),
      preferences: UserPreferences(),
    );

    test('should return updated user when role update succeeds', () async {
      // Arrange
      const newRole = UserRole.manager;
      final updatedUser = testUser.copyWith(
        role: newRole,
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.updateUserRole(testUid, newRole))
          .thenAnswer((_) async => updatedUser);

      // Act
      final result = await useCase(
        uid: testUid,
        role: newRole,
      );

      // Assert
      expect(result, equals(updatedUser));
      expect(result.role, equals(newRole));
      verify(mockUserRepository.updateUserRole(testUid, newRole)).called(1);
    });

    test('should throw exception when repository throws exception', () async {
      // Arrange
      const newRole = UserRole.admin;
      when(mockUserRepository.updateUserRole(testUid, newRole))
          .thenThrow(Exception('Repository error'));

      // Act & Assert
      expect(
        () async => await useCase(
          uid: testUid,
          role: newRole,
        ),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Failed to update user role'),
        )),
      );
      verify(mockUserRepository.updateUserRole(testUid, newRole)).called(1);
    });

    test('should handle role upgrade from inspector to manager', () async {
      // Arrange
      const newRole = UserRole.manager;
      final updatedUser = testUser.copyWith(
        role: newRole,
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.updateUserRole(testUid, newRole))
          .thenAnswer((_) async => updatedUser);

      // Act
      final result = await useCase(
        uid: testUid,
        role: newRole,
      );

      // Assert
      expect(result.role, equals(UserRole.manager));
      expect(result.role.displayName, equals('Manager'));
      verify(mockUserRepository.updateUserRole(testUid, newRole)).called(1);
    });

    test('should handle role upgrade from manager to admin', () async {
      // Arrange
      final managerUser = testUser.copyWith(role: UserRole.manager);
      const newRole = UserRole.admin;
      final updatedUser = managerUser.copyWith(
        role: newRole,
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.updateUserRole(testUid, newRole))
          .thenAnswer((_) async => updatedUser);

      // Act
      final result = await useCase(
        uid: testUid,
        role: newRole,
      );

      // Assert
      expect(result.role, equals(UserRole.admin));
      expect(result.role.displayName, equals('Administrator'));
      expect(result.role.description, contains('Full system access'));
      verify(mockUserRepository.updateUserRole(testUid, newRole)).called(1);
    });

    test('should handle role downgrade from admin to inspector', () async {
      // Arrange
      final adminUser = testUser.copyWith(role: UserRole.admin);
      const newRole = UserRole.inspector;
      final updatedUser = adminUser.copyWith(
        role: newRole,
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.updateUserRole(testUid, newRole))
          .thenAnswer((_) async => updatedUser);

      // Act
      final result = await useCase(
        uid: testUid,
        role: newRole,
      );

      // Assert
      expect(result.role, equals(UserRole.inspector));
      expect(result.role.displayName, equals('Inspector'));
      verify(mockUserRepository.updateUserRole(testUid, newRole)).called(1);
    });

    test('should handle same role assignment', () async {
      // Arrange
      const sameRole = UserRole.inspector;
      final updatedUser = testUser.copyWith(
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.updateUserRole(testUid, sameRole))
          .thenAnswer((_) async => updatedUser);

      // Act
      final result = await useCase(
        uid: testUid,
        role: sameRole,
      );

      // Assert
      expect(result.role, equals(UserRole.inspector));
      verify(mockUserRepository.updateUserRole(testUid, sameRole)).called(1);
    });

    test('should handle empty uid parameter', () async {
      // Arrange
      const emptyUid = '';
      const newRole = UserRole.manager;
      when(mockUserRepository.updateUserRole(emptyUid, newRole))
          .thenThrow(Exception('User not found'));

      // Act & Assert
      expect(
        () async => await useCase(
          uid: emptyUid,
          role: newRole,
        ),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Failed to update user role'),
        )),
      );
      verify(mockUserRepository.updateUserRole(emptyUid, newRole)).called(1);
    });

    test('should validate all user roles have proper display names', () async {
      // Test all UserRole enum values
      for (final role in UserRole.values) {
        final updatedUser = testUser.copyWith(
          role: role,
          updatedAt: DateTime(2024, 1, 3),
        );
        
        when(mockUserRepository.updateUserRole(testUid, role))
            .thenAnswer((_) async => updatedUser);

        // Act
        final result = await useCase(
          uid: testUid,
          role: role,
        );

        // Assert
        expect(result.role, equals(role));
        expect(result.role.displayName, isNotEmpty);
        expect(result.role.description, isNotEmpty);
      }
    });

    test('should verify role hierarchy and permissions', () {
      // Test role hierarchy
      expect(UserRole.inspector.displayName, equals('Inspector'));
      expect(UserRole.manager.displayName, equals('Manager'));
      expect(UserRole.admin.displayName, equals('Administrator'));
      
      // Test role descriptions contain relevant keywords
      expect(UserRole.inspector.description, contains('safety inspections'));
      expect(UserRole.manager.description, contains('team management'));
      expect(UserRole.admin.description, contains('Full system access'));
    });
  });
}