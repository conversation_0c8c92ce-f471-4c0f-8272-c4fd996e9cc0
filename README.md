# SafeStride

A Flutter-based workplace safety application for hazard reporting and walkabout management.

## Features

- **Walkabout Management**: Create and manage safety walkabouts
- **Hazard Reporting**: Report and track workplace hazards with photos and location data
- **Team Collaboration**: Share walkabouts and collaborate with team members
- **Offline Support**: Work offline with automatic sync when connected
- **Real-time Updates**: Live updates using Firebase Firestore
- **Location Services**: GPS-based hazard mapping and location tracking

## Tech Stack

- **Framework**: Flutter 3.16.0
- **Language**: Dart 3.2.0
- **Backend**: Firebase (Firestore, Auth, Storage, Analytics)
- **Local Database**: SQLite (sqflite 2.3.0)
- **State Management**: Provider 6.1.1
- **Maps**: Google Maps Flutter 2.5.0
- **Location**: Geolocator 10.1.0
- **Image Handling**: Image Picker 1.0.4

## Prerequisites

- Flutter SDK 3.16.0 or higher
- Dart SDK 3.2.0 or higher
- Android Studio / VS Code with Flutter extensions
- Firebase project setup
- Google Maps API key

## Getting Started

### 1. Clone the Repository

```bash
git clone <repository-url>
cd safestride
```

### 2. Install Dependencies

```bash
flutter pub get
```

### 3. Environment Configuration

1. Copy the environment template:
   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your Firebase and Google Maps configuration:
   - Firebase API keys and project details
   - Google Maps API key
   - Environment-specific settings

### 4. Firebase Setup

1. Create a Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable the following services:
   - Authentication
   - Firestore Database
   - Storage
   - Analytics
3. Download and configure:
   - `google-services.json` for Android (place in `android/app/`)
   - `GoogleService-Info.plist` for iOS (place in `ios/Runner/`)

### 5. Google Maps Setup

1. Get a Google Maps API key from [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the following APIs:
   - Maps SDK for Android
   - Maps SDK for iOS
   - Places API
3. Add the API key to your `.env` file

### 6. Run the Application

```bash
# Run on connected device/emulator
flutter run

# Run in debug mode
flutter run --debug

# Run in release mode
flutter run --release
```

## Project Structure

```
lib/
├── core/                   # Core utilities and constants
│   ├── constants/          # App constants
│   ├── errors/            # Error handling
│   ├── network/           # Network utilities
│   └── utils/             # General utilities
├── data/                  # Data layer
│   ├── datasources/       # Data sources (local/remote)
│   ├── models/            # Data models
│   └── repositories/      # Repository implementations
├── domain/                # Business logic layer
│   ├── entities/          # Business entities
│   ├── repositories/      # Repository interfaces
│   └── usecases/          # Use cases
├── presentation/          # UI layer
│   ├── pages/             # App screens
│   ├── providers/         # State management
│   ├── widgets/           # Reusable widgets
│   └── themes/            # App themes
├── services/              # External services
│   ├── camera/            # Camera service
│   ├── location/          # Location service
│   └── sync/              # Sync service
└── main.dart              # App entry point
```

## Development

### Code Style

- Follow [Effective Dart](https://dart.dev/guides/language/effective-dart) guidelines
- Use snake_case for variables and functions
- Use PascalCase for classes
- Run `dart format .` before committing

### Testing

```bash
# Run all tests
flutter test

# Run tests with coverage
flutter test --coverage

# Run specific test file
flutter test test/unit/example_test.dart
```

### Building

```bash
# Build Android APK
flutter build apk --release

# Build iOS (requires macOS)
flutter build ios --release

# Build for web
flutter build web
```

## CI/CD

The project includes GitHub Actions workflows for:
- Automated testing on push/PR
- Code formatting and analysis
- Android and iOS builds
- Test coverage reporting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and ensure they pass
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please contact the development team or create an issue in the repository.
