import 'dart:convert';
import 'package:sqflite/sqflite.dart';
import '../../models/user_model.dart';

/// Local data source for authentication credential caching
abstract class AuthLocalDataSource {
  /// Cache user credentials locally
  Future<void> cacheUser(UserModel user);

  /// Get cached user credentials
  Future<UserModel?> getCachedUser();

  /// Clear cached user credentials
  Future<void> clearCachedUser();

  /// Check if user credentials are cached
  Future<bool> hasValidCache();

  /// Update cached user data
  Future<void> updateCachedUser(UserModel user);
}

/// Implementation of AuthLocalDataSource using SQLite
class AuthLocalDataSourceImpl implements AuthLocalDataSource {
  final Database _database;
  static const String _tableName = 'cached_user';
  static const String _columnId = 'id';
  static const String _columnUserData = 'user_data';
  static const String _columnCachedAt = 'cached_at';
  static const String _columnExpiresAt = 'expires_at';

  AuthLocalDataSourceImpl(this._database);

  /// Initialize the database table
  static Future<void> createTable(Database db) async {
    await db.execute('''
      CREATE TABLE $_tableName (
        $_columnId INTEGER PRIMARY KEY,
        $_columnUserData TEXT NOT NULL,
        $_columnCachedAt INTEGER NOT NULL,
        $_columnExpiresAt INTEGER NOT NULL
      )
    ''');
  }

  @override
  Future<void> cacheUser(UserModel user) async {
    try {
      final now = DateTime.now().millisecondsSinceEpoch;
      final expiresAt = DateTime.now()
          .add(const Duration(days: 30)) // Cache for 30 days
          .millisecondsSinceEpoch;

      final userData = {
        'user': user.toJson(),
        'cached_at': now,
      };

      await _database.insert(
        _tableName,
        {
          _columnId: 1, // Single user cache
          _columnUserData: jsonEncode(userData),
          _columnCachedAt: now,
          _columnExpiresAt: expiresAt,
        },
        conflictAlgorithm: ConflictAlgorithm.replace,
      );
    } catch (e) {
      throw Exception('Failed to cache user: ${e.toString()}');
    }
  }

  @override
  Future<UserModel?> getCachedUser() async {
    try {
      final List<Map<String, dynamic>> maps = await _database.query(
        _tableName,
        where: '$_columnId = ?',
        whereArgs: [1],
      );

      if (maps.isEmpty) {
        return null;
      }

      final cachedData = maps.first;
      final expiresAt = cachedData[_columnExpiresAt] as int;
      final now = DateTime.now().millisecondsSinceEpoch;

      // Check if cache has expired
      if (now > expiresAt) {
        await clearCachedUser();
        return null;
      }

      final userDataJson = cachedData[_columnUserData] as String;
      final userData = jsonDecode(userDataJson) as Map<String, dynamic>;
      final userJson = userData['user'] as Map<String, dynamic>;

      return UserModel.fromJson(userJson);
    } catch (e) {
      // If there's any error reading cache, clear it and return null
      await clearCachedUser();
      return null;
    }
  }

  @override
  Future<void> clearCachedUser() async {
    try {
      await _database.delete(
        _tableName,
        where: '$_columnId = ?',
        whereArgs: [1],
      );
    } catch (e) {
      throw Exception('Failed to clear cached user: ${e.toString()}');
    }
  }

  @override
  Future<bool> hasValidCache() async {
    try {
      final List<Map<String, dynamic>> maps = await _database.query(
        _tableName,
        columns: [_columnExpiresAt],
        where: '$_columnId = ?',
        whereArgs: [1],
      );

      if (maps.isEmpty) {
        return false;
      }

      final expiresAt = maps.first[_columnExpiresAt] as int;
      final now = DateTime.now().millisecondsSinceEpoch;

      return now <= expiresAt;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> updateCachedUser(UserModel user) async {
    try {
      // Check if cache exists
      final hasCache = await hasValidCache();
      
      if (hasCache) {
        // Update existing cache
        final now = DateTime.now().millisecondsSinceEpoch;
        final userData = {
          'user': user.toJson(),
          'cached_at': now,
        };

        await _database.update(
          _tableName,
          {
            _columnUserData: jsonEncode(userData),
            _columnCachedAt: now,
          },
          where: '$_columnId = ?',
          whereArgs: [1],
        );
      } else {
        // Create new cache
        await cacheUser(user);
      }
    } catch (e) {
      throw Exception('Failed to update cached user: ${e.toString()}');
    }
  }
}