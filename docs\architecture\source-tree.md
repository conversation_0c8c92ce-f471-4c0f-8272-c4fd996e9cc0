# Source Tree

```plaintext
safestride/
├── .github/                    # CI/CD workflows
│   └── workflows/
│       └── main.yaml
├── android/                    # Android-specific configuration
├── ios/                        # iOS-specific configuration
├── lib/                        # Main application source code
│   ├── core/                   # Core utilities and constants
│   │   ├── constants/
│   │   ├── errors/
│   │   ├── network/
│   │   └── utils/
│   ├── data/                   # Data layer
│   │   ├── datasources/
│   │   │   ├── local/
│   │   │   └── remote/
│   │   ├── models/
│   │   └── repositories/
│   ├── domain/                 # Business logic layer
│   │   ├── entities/
│   │   ├── repositories/
│   │   └── usecases/
│   ├── presentation/           # UI layer
│   │   ├── pages/
│   │   ├── providers/
│   │   ├── widgets/
│   │   └── themes/
│   ├── services/               # External services
│   │   ├── camera/
│   │   ├── location/
│   │   └── sync/
│   └── main.dart               # Application entry point
├── test/                       # Test files
│   ├── unit/
│   ├── widget/
│   └── integration/
├── assets/                     # Static assets
│   ├── images/
│   └── icons/
├── docs/                       # Project documentation
│   ├── brief.md
│   ├── prd.md
│   ├── front-end-spec.md
│   └── architecture.md
├── .env.example                # Environment variables template
├── .gitignore                  # Git ignore rules
├── pubspec.yaml                # Flutter dependencies
├── analysis_options.yaml       # Dart analysis configuration
└── README.md                   # Project documentation
```
