import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/presentation/providers/auth_provider.dart';

import 'widget/auth/register_screen_test.mocks.dart';

@GenerateMocks([AuthProvider])
void main() {
  group('Simple Mock Tests', () {
    test('should be able to create mock and verify calls', () {
      // Arrange
      final mockAuthProvider = MockAuthProvider();
      when(mockAuthProvider.state).thenReturn(AuthState.initial);
      
      // Act
      mockAuthProvider.clearError();
      
      // Assert
      verify(mockAuthProvider.clearError()).called(1);
      expect(mockAuthProvider.state, equals(AuthState.initial));
    });
    
    test('should be able to verify clearError method exists', () {
      // Arrange
      final mockAuthProvider = MockAuthProvider();
      
      // Act & Assert - Just verify the method exists and can be called
      expect(() => mockAuthProvider.clearError(), returnsNormally);
    });
  });
}