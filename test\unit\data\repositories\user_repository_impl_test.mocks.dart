// Mocks generated by <PERSON>ckito 5.4.6 from annotations
// in safestride/test/unit/data/repositories/user_repository_impl_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/data/datasources/local/user_local_datasource.dart'
    as _i5;
import 'package:safestride/data/datasources/remote/user_remote_datasource.dart'
    as _i3;
import 'package:safestride/domain/entities/user.dart' as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeUser_0 extends _i1.SmartFake implements _i2.User {
  _FakeUser_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [UserRemoteDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserRemoteDataSource extends _i1.Mock
    implements _i3.UserRemoteDataSource {
  MockUserRemoteDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User?> getUserById(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#getUserById, [uid]),
            returnValue: _i4.Future<_i2.User?>.value(),
          )
          as _i4.Future<_i2.User?>);

  @override
  _i4.Future<_i2.User> updateUser(_i2.User? user) =>
      (super.noSuchMethod(
            Invocation.method(#updateUser, [user]),
            returnValue: _i4.Future<_i2.User>.value(
              _FakeUser_0(this, Invocation.method(#updateUser, [user])),
            ),
          )
          as _i4.Future<_i2.User>);
}

/// A class which mocks [UserLocalDataSource].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserLocalDataSource extends _i1.Mock
    implements _i5.UserLocalDataSource {
  MockUserLocalDataSource() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Future<_i2.User?> getCachedUser(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#getCachedUser, [uid]),
            returnValue: _i4.Future<_i2.User?>.value(),
          )
          as _i4.Future<_i2.User?>);

  @override
  _i4.Future<void> cacheUser(_i2.User? user) =>
      (super.noSuchMethod(
            Invocation.method(#cacheUser, [user]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> needsSync(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#needsSync, [uid]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);

  @override
  _i4.Future<void> markForSync(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#markForSync, [uid]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> clearSyncFlag(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#clearSyncFlag, [uid]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<bool> hasValidCache(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#hasValidCache, [uid]),
            returnValue: _i4.Future<bool>.value(false),
          )
          as _i4.Future<bool>);
}
