import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../domain/entities/walkabout.dart';
import '../../providers/walkabout_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/walkabout/location_picker_widget.dart';
import '../../widgets/walkabout/walkabout_template_selector.dart';

/// Screen for creating new walkabouts
/// 
/// Provides form interface for walkabout creation with template selection,
/// location picker, and validation following Material Design principles.
class WalkaboutCreationScreen extends StatefulWidget {
  const WalkaboutCreationScreen({super.key});

  @override
  State<WalkaboutCreationScreen> createState() => _WalkaboutCreationScreenState();
}

class _WalkaboutCreationScreenState extends State<WalkaboutCreationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  
  WalkaboutStatus _selectedStatus = WalkaboutStatus.draft;
  GeoPoint? _selectedLocation;
  String? _selectedTemplate;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Walkabout'),
        actions: [
          TextButton(
            onPressed: _createWalkabout,
            child: const Text('Create'),
          ),
        ],
      ),
      body: Consumer2<WalkaboutProvider, AuthProvider>(
        builder: (context, walkaboutProvider, authProvider, child) {
          return LoadingOverlay(
            isLoading: walkaboutProvider.isLoading,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Error display
                    if (walkaboutProvider.error != null)
                      Container(
                        padding: const EdgeInsets.all(12.0),
                        margin: const EdgeInsets.only(bottom: 16.0),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.errorContainer,
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Theme.of(context).colorScheme.error,
                            ),
                            const SizedBox(width: 8.0),
                            Expanded(
                              child: Text(
                                walkaboutProvider.error!,
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.error,
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: walkaboutProvider.clearError,
                              icon: const Icon(Icons.close),
                              iconSize: 20.0,
                            ),
                          ],
                        ),
                      ),

                    // Title field
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Walkabout Title *',
                        hintText: 'Enter a descriptive title',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Title is required';
                        }
                        if (value.length > 100) {
                          return 'Title cannot exceed 100 characters';
                        }
                        return null;
                      },
                      maxLength: 100,
                    ),
                    const SizedBox(height: 16.0),

                    // Description field
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(
                        labelText: 'Description',
                        hintText: 'Optional description of the walkabout',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      maxLength: 500,
                      validator: (value) {
                        if (value != null && value.length > 500) {
                          return 'Description cannot exceed 500 characters';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 16.0),

                    // Status selector
                    DropdownButtonFormField<WalkaboutStatus>(
                      value: _selectedStatus,
                      decoration: const InputDecoration(
                        labelText: 'Initial Status',
                        border: OutlineInputBorder(),
                      ),
                      items: WalkaboutStatus.values.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Row(
                            children: [
                              _getStatusIcon(status),
                              const SizedBox(width: 8.0),
                              Text(status.displayName),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (status) {
                        if (status != null) {
                          setState(() {
                            _selectedStatus = status;
                          });
                        }
                      },
                    ),
                    const SizedBox(height: 16.0),

                    // Template selector
                    WalkaboutTemplateSelector(
                      selectedTemplate: _selectedTemplate,
                      onTemplateSelected: (template) {
                        setState(() {
                          _selectedTemplate = template;
                        });
                      },
                    ),
                    const SizedBox(height: 16.0),

                    // Location picker
                    LocationPickerWidget(
                      selectedLocation: _selectedLocation,
                      onLocationSelected: (location) {
                        setState(() {
                          _selectedLocation = location;
                        });
                      },
                    ),
                    const SizedBox(height: 24.0),

                    // Create button
                    ElevatedButton(
                      onPressed: walkaboutProvider.isLoading ? null : _createWalkabout,
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16.0),
                      ),
                      child: const Text('Create Walkabout'),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Get status icon for display
  Widget _getStatusIcon(WalkaboutStatus status) {
    switch (status) {
      case WalkaboutStatus.draft:
        return const Icon(Icons.edit_note, size: 20.0);
      case WalkaboutStatus.inProgress:
        return const Icon(Icons.play_circle, size: 20.0);
      case WalkaboutStatus.completed:
        return const Icon(Icons.check_circle, size: 20.0);
      case WalkaboutStatus.archived:
        return const Icon(Icons.archive, size: 20.0);
    }
  }

  /// Create walkabout
  Future<void> _createWalkabout() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final authProvider = context.read<AuthProvider>();
    final walkaboutProvider = context.read<WalkaboutProvider>();

    if (authProvider.currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User not authenticated')),
      );
      return;
    }

    final walkabout = await walkaboutProvider.createWalkabout(
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim().isEmpty 
          ? null 
          : _descriptionController.text.trim(),
      userId: authProvider.currentUser!.uid,
      location: _selectedLocation,
      status: _selectedStatus,
    );

    if (walkabout != null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Walkabout created successfully')),
        );
        Navigator.of(context).pop(walkabout);
      }
    }
  }
}
