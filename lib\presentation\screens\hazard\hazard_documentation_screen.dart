import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../domain/entities/hazard.dart';
import '../../../domain/entities/walkabout.dart';
import '../../providers/hazard_provider.dart';
import '../../widgets/common/loading_overlay.dart';
import '../../widgets/hazard/location_tagger.dart';

/// Screen for documenting hazards during walkabouts
///
/// Provides comprehensive interface for hazard documentation including
/// photo capture, voice input, location tagging, and severity assessment.
class HazardDocumentationScreen extends StatefulWidget {
  final String walkaboutId;
  final Hazard? existingHazard;

  const HazardDocumentationScreen({
    super.key,
    required this.walkaboutId,
    this.existingHazard,
  });

  @override
  State<HazardDocumentationScreen> createState() =>
      _HazardDocumentationScreenState();
}

class _HazardDocumentationScreenState extends State<HazardDocumentationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();

  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _isEditing = widget.existingHazard != null;

    if (_isEditing) {
      _populateFormWithExistingHazard();
    }
  }

  void _populateFormWithExistingHazard() {
    final hazard = widget.existingHazard!;
    _titleController.text = hazard.title;
    _descriptionController.text = hazard.description ?? '';
    _notesController.text = hazard.notes ?? '';

    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = context.read<HazardProvider>();
      provider.setCurrentHazard(hazard);
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_isEditing ? 'Edit Hazard' : 'Document Hazard'),
        actions: [
          TextButton(
            onPressed: _saveHazard,
            child: Text(_isEditing ? 'Update' : 'Save'),
          ),
        ],
      ),
      body: Consumer<HazardProvider>(
        builder: (context, hazardProvider, child) {
          return LoadingOverlay(
            isLoading: hazardProvider.isLoading,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Error display
                    if (hazardProvider.error != null)
                      Container(
                        margin: const EdgeInsets.only(bottom: 16.0),
                        padding: const EdgeInsets.all(12.0),
                        decoration: BoxDecoration(
                          color: Colors.red.shade50,
                          border: Border.all(color: Colors.red.shade200),
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.error_outline,
                              color: Colors.red.shade600,
                            ),
                            const SizedBox(width: 8.0),
                            Expanded(
                              child: Text(
                                hazardProvider.error!,
                                style: TextStyle(color: Colors.red.shade600),
                              ),
                            ),
                          ],
                        ),
                      ),

                    // Title field
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: 'Hazard Title *',
                        hintText: 'Brief description of the hazard',
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Please enter a hazard title';
                        }
                        if (value.length > 100) {
                          return 'Title cannot exceed 100 characters';
                        }
                        return null;
                      },
                      maxLength: 100,
                    ),
                    const SizedBox(height: 16.0),

                    // Description field with voice input
                    _buildDescriptionField(hazardProvider),
                    const SizedBox(height: 16.0),

                    // Severity selector
                    _buildSeveritySelector(hazardProvider),
                    const SizedBox(height: 16.0),

                    // Category selector
                    _buildCategorySelector(hazardProvider),
                    const SizedBox(height: 16.0),

                    // Location section
                    _buildLocationSection(hazardProvider),
                    const SizedBox(height: 16.0),

                    // Photo section
                    _buildPhotoSection(hazardProvider),
                    const SizedBox(height: 16.0),

                    // Notes field
                    TextFormField(
                      controller: _notesController,
                      decoration: const InputDecoration(
                        labelText: 'Additional Notes',
                        hintText: 'Any additional information about the hazard',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      maxLength: 500,
                    ),
                    const SizedBox(height: 24.0),

                    // Action buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: _clearForm,
                            child: const Text('Clear'),
                          ),
                        ),
                        const SizedBox(width: 16.0),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _saveHazard,
                            child: Text(
                              _isEditing ? 'Update Hazard' : 'Save Hazard',
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildDescriptionField(HazardProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(
                  labelText: 'Description',
                  hintText: 'Detailed description of the hazard',
                  border: OutlineInputBorder(),
                ),
                maxLines: 3,
                maxLength: 1000,
              ),
            ),
            const SizedBox(width: 8.0),
            Column(
              children: [
                IconButton(
                  onPressed:
                      provider.isVoiceInputActive
                          ? _stopVoiceInput
                          : _startVoiceInput,
                  icon: Icon(
                    provider.isVoiceInputActive ? Icons.mic : Icons.mic_none,
                    color: provider.isVoiceInputActive ? Colors.red : null,
                  ),
                  tooltip:
                      provider.isVoiceInputActive
                          ? 'Stop voice input'
                          : 'Start voice input',
                ),
                if (provider.isVoiceInputActive)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
              ],
            ),
          ],
        ),
        if (provider.voiceInputText.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 8.0),
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              border: Border.all(color: Colors.blue.shade200),
              borderRadius: BorderRadius.circular(4.0),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.record_voice_over,
                  color: Colors.blue.shade600,
                  size: 16,
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: Text(
                    provider.voiceInputText,
                    style: TextStyle(color: Colors.blue.shade600),
                  ),
                ),
                IconButton(
                  onPressed: _useVoiceInput,
                  icon: const Icon(Icons.add, size: 16),
                  tooltip: 'Add to description',
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildSeveritySelector(HazardProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Severity Level *',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8.0),
        Wrap(
          spacing: 8.0,
          children:
              HazardSeverity.values.map((severity) {
                final isSelected = provider.selectedSeverity == severity;
                return ChoiceChip(
                  label: Text(severity.displayName),
                  selected: isSelected,
                  onSelected: (selected) {
                    if (selected) {
                      provider.setSelectedSeverity(severity);
                    }
                  },
                  backgroundColor: Color(severity.colorValue).withOpacity(0.1),
                  selectedColor: Color(severity.colorValue).withOpacity(0.3),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildCategorySelector(HazardProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Category *',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8.0),
        DropdownButtonFormField<HazardCategory>(
          value: provider.selectedCategory,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
            hintText: 'Select hazard category',
          ),
          items:
              HazardCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Row(
                    children: [
                      Icon(Icons.category, size: 16),
                      const SizedBox(width: 8.0),
                      Text(category.displayName),
                    ],
                  ),
                );
              }).toList(),
          onChanged: (category) {
            if (category != null) {
              provider.setSelectedCategory(category);
            }
          },
          validator: (value) {
            if (value == null) {
              return 'Please select a category';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildLocationSection(HazardProvider provider) {
    return LocationTagger(
      selectedLocation: provider.selectedLocation,
      onLocationChanged: (location) {
        provider.setSelectedLocation(location);
      },
      label: 'Hazard Location',
      locationService: provider.locationService,
    );
  }

  Widget _buildPhotoSection(HazardProvider provider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'Photos',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
            ),
            Row(
              children: [
                IconButton(
                  onPressed: _capturePhoto,
                  icon: const Icon(Icons.camera_alt),
                  tooltip: 'Take photo',
                ),
                IconButton(
                  onPressed: _selectFromGallery,
                  icon: const Icon(Icons.photo_library),
                  tooltip: 'Select from gallery',
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8.0),
        if (provider.selectedPhotos.isEmpty)
          Container(
            height: 100,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey.shade300,
                style: BorderStyle.solid,
              ),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.add_a_photo, color: Colors.grey, size: 32),
                  SizedBox(height: 8.0),
                  Text('No photos added', style: TextStyle(color: Colors.grey)),
                ],
              ),
            ),
          )
        else
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: provider.selectedPhotos.length,
              itemBuilder: (context, index) {
                final photoPath = provider.selectedPhotos[index];
                return Container(
                  margin: const EdgeInsets.only(right: 8.0),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: Container(
                          width: 100,
                          height: 100,
                          color: Colors.grey.shade200,
                          child: const Icon(Icons.image, size: 32),
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: GestureDetector(
                          onTap: () => _removePhoto(photoPath),
                          child: Container(
                            padding: const EdgeInsets.all(2),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              color: Colors.white,
                              size: 16,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  // Event handlers
  void _startVoiceInput() async {
    final provider = context.read<HazardProvider>();
    await provider.startVoiceInput();
  }

  void _stopVoiceInput() async {
    final provider = context.read<HazardProvider>();
    await provider.stopVoiceInput();
  }

  void _useVoiceInput() {
    final provider = context.read<HazardProvider>();
    final currentText = _descriptionController.text;
    final voiceText = provider.voiceInputText;

    if (voiceText.isNotEmpty) {
      final newText =
          currentText.isEmpty ? voiceText : '$currentText $voiceText';
      _descriptionController.text = newText;
      provider.cancelVoiceInput();
    }
  }

  void _capturePhoto() async {
    final provider = context.read<HazardProvider>();
    await provider.capturePhoto();
  }

  void _selectFromGallery() async {
    final provider = context.read<HazardProvider>();
    await provider.selectPhotoFromGallery();
  }

  void _removePhoto(String photoPath) {
    final provider = context.read<HazardProvider>();
    provider.removePhoto(photoPath);
  }

  void _clearForm() {
    _titleController.clear();
    _descriptionController.clear();
    _notesController.clear();

    final provider = context.read<HazardProvider>();
    provider.clearForm();
  }

  void _saveHazard() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final provider = context.read<HazardProvider>();

    if (_isEditing) {
      final updatedHazard = await provider.updateHazard(
        id: widget.existingHazard!.id,
        title: _titleController.text.trim(),
        description:
            _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
        severity: provider.selectedSeverity,
        category: provider.selectedCategory,
        location: provider.selectedLocation,
        photos: provider.selectedPhotos,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
      );

      if (updatedHazard != null && mounted) {
        Navigator.of(context).pop(updatedHazard);
      }
    } else {
      final newHazard = await provider.createHazard(
        walkaboutId: widget.walkaboutId,
        title: _titleController.text.trim(),
        description:
            _descriptionController.text.trim().isEmpty
                ? null
                : _descriptionController.text.trim(),
        severity: provider.selectedSeverity,
        category: provider.selectedCategory,
        location: provider.selectedLocation,
        photos: provider.selectedPhotos,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
      );

      if (newHazard != null && mounted) {
        Navigator.of(context).pop(newHazard);
      }
    }
  }
}
