import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/user_repository.dart';
import 'package:safestride/domain/usecases/sync_user.dart';
import 'package:safestride/services/sync/profile_sync_service.dart';

// Generate mock classes
@GenerateMocks([UserRepository, SyncUserUseCase, Connectivity])
import 'profile_sync_service_test.mocks.dart';

void main() {
  late ProfileSyncService profileSyncService;
  late MockUserRepository mockUserRepository;
  late MockSyncUserUseCase mockSyncUserUseCase;
  late MockConnectivity mockConnectivity;

  const String testUserId = 'test-user-123';

  final testUser = User(
    uid: testUserId,
    email: '<EMAIL>',
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    needsSync: true,
  );

  setUp(() {
    mockUserRepository = MockUserRepository();
    mockSyncUserUseCase = MockSyncUserUseCase();
    mockConnectivity = MockConnectivity();

    // Default connectivity setup
    when(
      mockConnectivity.checkConnectivity(),
    ).thenAnswer((_) async => ConnectivityResult.wifi);

    when(
      mockConnectivity.onConnectivityChanged,
    ).thenAnswer((_) => Stream.value(ConnectivityResult.wifi));

    // Setup repository defaults
    when(
      mockUserRepository.getCachedUser(any),
    ).thenAnswer((_) async => testUser);

    // Setup use case defaults
    when(mockSyncUserUseCase.call(any)).thenAnswer((_) async => testUser);

    profileSyncService = ProfileSyncService(
      userRepository: mockUserRepository,
      syncUserUseCase: mockSyncUserUseCase,
      connectivity: mockConnectivity,
    );
  });

  group('ProfileSyncService', () {
    test('should initialize with provided dependencies', () {
      expect(profileSyncService, isNotNull);
    });

    test('should queue user for sync', () async {
      // Act
      await profileSyncService.queueForSync(testUserId);

      // Verify sync usecase was called
      verify(mockSyncUserUseCase.call(testUserId)).called(1);
    });

    test('should sync user when requested', () async {
      // Act
      final result = await profileSyncService.syncNow(testUserId);

      // Assert
      expect(result, equals(testUser));
      verify(mockSyncUserUseCase.call(testUserId)).called(1);
    });

    test('should not sync if already syncing', () async {
      // Setup: Make first call to start syncing
      final syncFuture = profileSyncService.syncNow(testUserId);

      // Act: Try to sync again while first sync is running
      final result = await profileSyncService.syncNow('other-user');

      // Wait for first sync to complete
      await syncFuture;

      // Assert
      expect(result, isNull);
      verify(mockSyncUserUseCase.call(testUserId)).called(1);
      verifyNever(mockSyncUserUseCase.call('other-user'));
    });

    test('should report correct sync status', () async {
      // Setup
      when(
        mockUserRepository.getCachedUser(testUserId),
      ).thenAnswer((_) async => testUser.copyWith(needsSync: true));

      // Act
      final needsSync = await profileSyncService.needsSync(testUserId);

      // Assert
      expect(needsSync, isTrue);
    });

    test('should handle connectivity changes', () async {
      // Setup: Initialize with user
      profileSyncService.initialize(testUserId);

      // Act: Simulate connectivity change
      final connectivityController = StreamController<ConnectivityResult>();
      when(
        mockConnectivity.onConnectivityChanged,
      ).thenAnswer((_) => connectivityController.stream);

      // Initial check after initialize
      verify(mockConnectivity.checkConnectivity()).called(1);

      // Simulate connectivity change to offline
      connectivityController.add(ConnectivityResult.none);
      await Future.delayed(Duration.zero);

      // Simulate connectivity change to online
      connectivityController.add(ConnectivityResult.mobile);
      await Future.delayed(Duration.zero);

      // User should be checked for sync when coming back online
      verify(
        mockUserRepository.getCachedUser(testUserId),
      ).called(greaterThan(0));

      // Cleanup
      connectivityController.close();
    });

    test('should queue for sync if sync fails', () async {
      // Setup
      when(
        mockSyncUserUseCase.call(testUserId),
      ).thenThrow(Exception('Sync failed'));

      // Act
      final result = await profileSyncService.syncNow(testUserId);

      // Assert
      expect(result, isNull);

      // Verify it was queued (by checking that needsSync returns true)
      expect(await profileSyncService.needsSync(testUserId), isTrue);
    });
  });
}
