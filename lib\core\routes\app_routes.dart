import 'package:flutter/material.dart';
import '../../presentation/screens/auth/login_screen.dart';
import '../../presentation/screens/auth/register_screen.dart';
import '../../presentation/screens/auth/forgot_password_screen.dart';
import '../../presentation/screens/profile/profile_screen.dart';
import '../../presentation/screens/profile/profile_edit_screen.dart';
import '../../presentation/screens/profile/preferences_screen.dart';
import '../../presentation/screens/profile/role_management_screen.dart';
import 'route_guard.dart';
import 'initial_route_handler.dart';

/// Application route names
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // Route names
  static const String initial = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String home = '/home';
  static const String profile = '/profile';
  static const String profileEdit = '/profile/edit';
  static const String profilePreferences = '/profile/preferences';
  static const String profileRoleManagement = '/profile/role-management';
  static const String settings = '/settings';
  static const String forgotPassword = '/forgot-password';
  static const String resetPassword = '/reset-password';
  static const String emailVerification = '/email-verification';
  static const String onboarding = '/onboarding';
  static const String splash = '/splash';

  // Dashboard routes
  static const String dashboard = '/dashboard';
  static const String analytics = '/analytics';
  static const String reports = '/reports';

  // Inspection routes
  static const String inspections = '/inspections';
  static const String inspectionDetail = '/inspection-detail';
  static const String createInspection = '/create-inspection';
  static const String editInspection = '/edit-inspection';
  static const String inspectionHistory = '/inspection-history';

  // Site routes
  static const String sites = '/sites';
  static const String siteDetail = '/site-detail';
  static const String createSite = '/create-site';
  static const String editSite = '/edit-site';
  static const String siteMap = '/site-map';

  // User management routes
  static const String users = '/users';
  static const String userDetail = '/user-detail';
  static const String createUser = '/create-user';
  static const String editUser = '/edit-user';
  static const String userRoles = '/user-roles';

  // Organization routes
  static const String organizations = '/organizations';
  static const String organizationDetail = '/organization-detail';
  static const String createOrganization = '/create-organization';
  static const String editOrganization = '/edit-organization';

  // Notification routes
  static const String notifications = '/notifications';
  static const String notificationDetail = '/notification-detail';
  static const String notificationSettings = '/notification-settings';

  // Help and support routes
  static const String help = '/help';
  static const String support = '/support';
  static const String about = '/about';
  static const String privacyPolicy = '/privacy-policy';
  static const String termsOfService = '/terms-of-service';

  // Error routes
  static const String notFound = '/not-found';
  static const String error = '/error';
  static const String offline = '/offline';

  /// Generate routes based on route settings
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case initial:
        // Initial route will redirect based on auth state
        return MaterialPageRoute(
          builder: (_) => const InitialRouteHandler(),
          settings: settings,
        );
        
      case login:
        return MaterialPageRoute(
          builder: (_) => const UnauthenticatedGuard(
            child: LoginScreen(),
          ),
          settings: settings,
        );

      case register:
        return MaterialPageRoute(
          builder: (_) => const UnauthenticatedGuard(
            child: RegisterScreen(),
          ),
          settings: settings,
        );

      case forgotPassword:
        return MaterialPageRoute(
          builder: (_) => const UnauthenticatedGuard(
            child: ForgotPasswordScreen(),
          ),
          settings: settings,
        );

      case home:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: Scaffold(
              body: Center(
                child: Text('Home Screen - Coming Soon'),
              ),
            ),
          ),
          settings: settings,
        );

      case profile:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: ProfileScreen(),
          ),
          settings: settings,
        );
        
      case profileEdit:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: ProfileEditScreen(),
          ),
          settings: settings,
        );
        
      case profilePreferences:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: PreferencesScreen(),
          ),
          settings: settings,
        );
        
      case profileRoleManagement:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: RoleManagementScreen(),
          ),
          settings: settings,
        );

      case AppRoutes.settings:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: Scaffold(
              body: Center(
                child: Text('Settings Screen - Coming Soon'),
              ),
            ),
          ),
          settings: settings,
        );

      case dashboard:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: Scaffold(
              body: Center(
                child: Text('Dashboard Screen - Coming Soon'),
              ),
            ),
          ),
          settings: settings,
        );

      case inspections:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: Scaffold(
              body: Center(
                child: Text('Inspections Screen - Coming Soon'),
              ),
            ),
          ),
          settings: settings,
        );

      case sites:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: Scaffold(
              body: Center(
                child: Text('Sites Screen - Coming Soon'),
              ),
            ),
          ),
          settings: settings,
        );

      case users:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: Scaffold(
              body: Center(
                child: Text('Users Screen - Coming Soon'),
              ),
            ),
          ),
          settings: settings,
        );

      case notifications:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: Scaffold(
              body: Center(
                child: Text('Notifications Screen - Coming Soon'),
              ),
            ),
          ),
          settings: settings,
        );

      case help:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: Scaffold(
              body: Center(
                child: Text('Help Screen - Coming Soon'),
              ),
            ),
          ),
          settings: settings,
        );

      case about:
        return MaterialPageRoute(
          builder: (_) => const AuthGuard(
            child: Scaffold(
              body: Center(
                child: Text('About Screen - Coming Soon'),
              ),
            ),
          ),
          settings: settings,
        );

      case notFound:
      default:
        return MaterialPageRoute(
          builder: (_) => const NotFoundScreen(),
          settings: settings,
        );
    }
  }

  /// Get route arguments safely
  static T? getArguments<T>(RouteSettings settings) {
    final arguments = settings.arguments;
    if (arguments is T) {
      return arguments;
    }
    return null;
  }

  /// Navigate to route with arguments
  static Future<T?> navigateTo<T extends Object?>(
    BuildContext context,
    String routeName, {
    Object? arguments,
    bool replace = false,
    bool clearStack = false,
  }) {
    if (clearStack) {
      return Navigator.of(context).pushNamedAndRemoveUntil(
        routeName,
        (route) => false,
        arguments: arguments,
      );
    } else if (replace) {
      return Navigator.of(context).pushReplacementNamed(
        routeName,
        arguments: arguments,
      );
    } else {
      return Navigator.of(context).pushNamed(
        routeName,
        arguments: arguments,
      );
    }
  }

  /// Navigate back
  static void navigateBack<T extends Object?>(BuildContext context, [T? result]) {
    Navigator.of(context).pop(result);
  }

  /// Check if can navigate back
  static bool canNavigateBack(BuildContext context) {
    return Navigator.of(context).canPop();
  }

  /// Navigate to login and clear stack
  static Future<void> navigateToLogin(BuildContext context) {
    return navigateTo(
      context,
      login,
      clearStack: true,
    );
  }

  /// Navigate to home and clear stack
  static Future<void> navigateToHome(BuildContext context) {
    return navigateTo(
      context,
      home,
      clearStack: true,
    );
  }

  /// Navigate to dashboard and clear stack
  static Future<void> navigateToDashboard(BuildContext context) {
    return navigateTo(
      context,
      dashboard,
      clearStack: true,
    );
  }
}

/// Not found screen widget
class NotFoundScreen extends StatelessWidget {
  const NotFoundScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Page Not Found'),
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              '404',
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Page Not Found',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
            SizedBox(height: 16),
            Text(
              'The page you are looking for does not exist.',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}