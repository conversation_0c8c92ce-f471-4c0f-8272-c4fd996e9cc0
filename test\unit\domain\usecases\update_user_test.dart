import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/repositories/user_repository.dart';
import 'package:safestride/domain/usecases/update_user.dart';

import 'update_user_test.mocks.dart';

@GenerateMocks([UserRepository])
void main() {
  late UpdateUserUseCase useCase;
  late MockUserRepository mockUserRepository;

  setUp(() {
    mockUserRepository = MockUserRepository();
    useCase = UpdateUserUseCase(mockUserRepository);
  });

  group('UpdateUserUseCase', () {
    final testUser = User(
      id: 'test-uid-123',
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime(2024, 1, 1),
      lastLoginAt: DateTime(2024, 1, 2),
      preferences: UserPreferences(),
    );

    final updatedUser = testUser.copyWith(
      displayName: 'Updated Test User',
      organization: 'Updated Test Org',
      updatedAt: DateTime(2024, 1, 3),
    );

    test('should return updated user when repository call succeeds', () async {
      // Arrange
      when(mockUserRepository.updateUser(testUser))
          .thenAnswer((_) async => updatedUser);

      // Act
      final result = await useCase(testUser);

      // Assert
      expect(result, equals(updatedUser));
      verify(mockUserRepository.updateUser(testUser)).called(1);
    });

    test('should throw exception when repository throws exception', () async {
      // Arrange
      when(mockUserRepository.updateUser(testUser))
          .thenThrow(Exception('Repository error'));

      // Act & Assert
      expect(
        () async => await useCase(testUser),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Failed to update user'),
        )),
      );
      verify(mockUserRepository.updateUser(testUser)).called(1);
    });

    test('should handle user with minimal required fields', () async {
      // Arrange
      final minimalUser = User(
        id: 'minimal-uid',
        email: '<EMAIL>',
      );
      final minimalUpdatedUser = minimalUser.copyWith(
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.updateUser(minimalUser))
          .thenAnswer((_) async => minimalUpdatedUser);

      // Act
      final result = await useCase(minimalUser);

      // Assert
      expect(result, equals(minimalUpdatedUser));
      verify(mockUserRepository.updateUser(minimalUser)).called(1);
    });

    test('should handle user with all fields populated', () async {
      // Arrange
      final fullUser = User(
        id: 'full-uid',
        email: '<EMAIL>',
        displayName: 'Full User',
        organization: 'Full Org',
        role: UserRole.admin,
        photoUrl: 'https://example.com/photo.jpg',
        createdAt: DateTime(2024, 1, 1),
        lastLoginAt: DateTime(2024, 1, 2),
        updatedAt: DateTime(2024, 1, 2),
        preferences: UserPreferences(
          notificationsEnabled: false,
          language: 'es',
          theme: 'dark',
          offlineMode: true,
        ),
        needsSync: true,
      );
      
      final fullUpdatedUser = fullUser.copyWith(
        displayName: 'Updated Full User',
        updatedAt: DateTime(2024, 1, 3),
      );
      
      when(mockUserRepository.updateUser(fullUser))
          .thenAnswer((_) async => fullUpdatedUser);

      // Act
      final result = await useCase(fullUser);

      // Assert
      expect(result, equals(fullUpdatedUser));
      verify(mockUserRepository.updateUser(fullUser)).called(1);
    });
  });
}