import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../domain/entities/user.dart';
import '../../providers/auth_provider.dart';
import '../../providers/user_profile_provider.dart';
import '../../widgets/app_bar_widget.dart';
import '../../widgets/loading_indicator.dart';
import 'profile_edit_screen.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  void initState() {
    super.initState();
    // Load user profile on screen initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final userProfileProvider = Provider.of<UserProfileProvider>(context, listen: false);
      
      if (authProvider.currentUser != null) {
        userProfileProvider.loadUserProfile(authProvider.currentUser!.uid);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Profile')),
      body: Consumer<UserProfileProvider>(
        builder: (context, userProfileProvider, _) {
          if (userProfileProvider.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (userProfileProvider.state == UserProfileState.error) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Error: ${userProfileProvider.errorMessage}',
                    style: const TextStyle(color: Colors.red),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      final authProvider = Provider.of<AuthProvider>(context, listen: false);
                      if (authProvider.currentUser != null) {
                        userProfileProvider.loadUserProfile(authProvider.currentUser!.uid);
                      }
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          final user = userProfileProvider.user;
          if (user == null) {
            return const Center(child: Text('User not found'));
          }

          return _buildProfileContent(context, user, userProfileProvider);
        },
      ),
    );
  }

  Widget _buildProfileContent(BuildContext context, User user, UserProfileProvider userProfileProvider) {
    return RefreshIndicator(
      onRefresh: () => userProfileProvider.refreshProfile(),
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileHeader(context, user),
            const SizedBox(height: 24),
            _buildProfileDetails(user),
            const SizedBox(height: 24),
            _buildPreferencesSection(user),
            const SizedBox(height: 24),
            _buildActionButtons(context, userProfileProvider),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(BuildContext context, User user) {
    return Center(
      child: Column(
        children: [
          CircleAvatar(
            radius: 50,
            backgroundColor: Theme.of(context).primaryColor,
            child: Text(
              (user.displayName?.isNotEmpty ?? false) ? user.displayName![0].toUpperCase() : '?',
              style: const TextStyle(fontSize: 40, color: Colors.white),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            user.displayName ?? 'Unknown User',
            style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
          ),
          Text(
            user.email,
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Chip(
            label: Text(user.role.toString().split('.').last),
            backgroundColor: _getRoleColor(user.role),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileDetails(User user) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Profile Details',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            _buildDetailItem('Organization', user.organization ?? 'Not specified'),
            _buildDetailItem('Created', _formatDate(user.createdAt)),
            _buildDetailItem('Last Login', _formatDate(user.lastLoginAt)),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesSection(User user) {
    final preferences = user.preferences;
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Preferences',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            _buildPreferenceItem(
              'Notifications',
              preferences.notificationsEnabled ? 'Enabled' : 'Disabled',
              preferences.notificationsEnabled ? Icons.notifications_active : Icons.notifications_off,
            ),
            _buildPreferenceItem(
              'Language',
              preferences.language,
              Icons.language,
            ),
            _buildPreferenceItem(
              'Theme',
              preferences.theme,
              Icons.palette,
            ),
            _buildPreferenceItem(
              'Offline Mode',
              preferences.offlineMode ? 'Enabled' : 'Disabled',
              preferences.offlineMode ? Icons.cloud_off : Icons.cloud_done,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 16, color: Colors.grey[700]),
          ),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildPreferenceItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[700]),
          const SizedBox(width: 8),
          Text(
            label,
            style: TextStyle(fontSize: 16, color: Colors.grey[700]),
          ),
          const Spacer(),
          Text(
            value,
            style: const TextStyle(fontSize: 16),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, UserProfileProvider userProfileProvider) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const ProfileEditScreen(),
              ),
            ).then((_) {
              // Refresh profile after returning from edit screen
              userProfileProvider.refreshProfile();
            });
          },
          icon: const Icon(Icons.edit),
          label: const Text('Edit Profile'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
        const SizedBox(height: 12),
        ElevatedButton.icon(
          onPressed: userProfileProvider.isSyncing
              ? null
              : () => userProfileProvider.syncUserData(),
          icon: userProfileProvider.isSyncing
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.sync),
          label: Text(userProfileProvider.isSyncing ? 'Syncing...' : 'Sync Data'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(vertical: 12),
          ),
        ),
      ],
    );
  }

  Color _getRoleColor(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return Colors.red[100]!;
      case UserRole.moderator:
        return Colors.orange[100]!;
      case UserRole.inspector:
      default:
        return Colors.blue[100]!;
    }
  }

  String _formatDate(DateTime? dateTime) {
    if (dateTime == null) return 'N/A';
    return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
  }
}