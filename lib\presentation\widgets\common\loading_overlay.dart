import 'package:flutter/material.dart';
import '../../../core/constants/app_colors.dart';

/// Loading overlay widget to show loading state over content
class LoadingOverlay extends StatelessWidget {
  final bool isLoading;
  final String? message;
  final String? loadingMessage;
  final Color? backgroundColor;
  final Color? indicatorColor;
  final Widget child;

  const LoadingOverlay({
    super.key,
    this.isLoading = false,
    this.message,
    this.loadingMessage,
    this.backgroundColor,
    this.indicatorColor,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    // If not loading, just show the child
    if (!isLoading) {
      return child;
    }
    
    // Determine which message to use
    final displayMessage = loadingMessage ?? message;
    
    // Show loading overlay on top of the child
    return Stack(
      children: [
        // The main content
        child,
        
        // The overlay
        Container(
          color: backgroundColor ?? Colors.black.withOpacity(0.5),
          child: Center(
            child: Card(
              elevation: 8,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Padding(
                padding: const EdgeInsets.all(24.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 40,
                      height: 40,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          indicatorColor ?? AppColors.primary,
                        ),
                      ),
                    ),
                    if (displayMessage != null) ...[                  
                      const SizedBox(height: 16),
                      Text(
                        displayMessage,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}