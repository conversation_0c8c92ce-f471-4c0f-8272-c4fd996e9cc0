import 'package:flutter/material.dart';

/// Widget for selecting walkabout templates
/// 
/// Provides interface for selecting predefined walkabout templates
/// to streamline the creation process.
class WalkaboutTemplateSelector extends StatelessWidget {
  final String? selectedTemplate;
  final ValueChanged<String?> onTemplateSelected;

  const WalkaboutTemplateSelector({
    super.key,
    required this.selectedTemplate,
    required this.onTemplateSelected,
  });

  // Predefined templates for walkabouts
  static const List<WalkaboutTemplate> _templates = [
    WalkaboutTemplate(
      id: 'general_safety',
      name: 'General Safety Inspection',
      description: 'Standard safety walkabout covering common hazards',
      icon: Icons.security,
    ),
    WalkaboutTemplate(
      id: 'construction_site',
      name: 'Construction Site',
      description: 'Specialized for construction and building sites',
      icon: Icons.construction,
    ),
    WalkaboutTemplate(
      id: 'office_environment',
      name: 'Office Environment',
      description: 'Office and workplace safety inspection',
      icon: Icons.business,
    ),
    WalkaboutTemplate(
      id: 'warehouse',
      name: 'Warehouse & Storage',
      description: 'Warehouse and storage facility inspection',
      icon: Icons.warehouse,
    ),
    WalkaboutTemplate(
      id: 'manufacturing',
      name: 'Manufacturing Floor',
      description: 'Manufacturing and production area inspection',
      icon: Icons.precision_manufacturing,
    ),
    WalkaboutTemplate(
      id: 'custom',
      name: 'Custom Walkabout',
      description: 'Create a custom walkabout from scratch',
      icon: Icons.edit,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.template_outlined,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8.0),
                Text(
                  'Template',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            const SizedBox(height: 8.0),
            Text(
              'Choose a template to get started quickly',
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 16.0),
            
            // Template grid
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 1.2,
                crossAxisSpacing: 12.0,
                mainAxisSpacing: 12.0,
              ),
              itemCount: _templates.length,
              itemBuilder: (context, index) {
                final template = _templates[index];
                final isSelected = selectedTemplate == template.id;
                
                return _buildTemplateCard(context, template, isSelected);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateCard(
    BuildContext context,
    WalkaboutTemplate template,
    bool isSelected,
  ) {
    return InkWell(
      onTap: () {
        onTemplateSelected(isSelected ? null : template.id);
      },
      borderRadius: BorderRadius.circular(12.0),
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline,
            width: isSelected ? 2.0 : 1.0,
          ),
          borderRadius: BorderRadius.circular(12.0),
          color: isSelected
              ? Theme.of(context).colorScheme.primaryContainer
              : Theme.of(context).colorScheme.surface,
        ),
        padding: const EdgeInsets.all(12.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              template.icon,
              size: 32.0,
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurface,
            ),
            const SizedBox(height: 8.0),
            Text(
              template.name,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : null,
                fontWeight: isSelected ? FontWeight.bold : null,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 4.0),
            Text(
              template.description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: isSelected
                    ? Theme.of(context).colorScheme.onPrimaryContainer
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            if (isSelected) ...[
              const SizedBox(height: 8.0),
              Icon(
                Icons.check_circle,
                size: 20.0,
                color: Theme.of(context).colorScheme.primary,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Data class for walkabout templates
class WalkaboutTemplate {
  final String id;
  final String name;
  final String description;
  final IconData icon;

  const WalkaboutTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
  });
}
