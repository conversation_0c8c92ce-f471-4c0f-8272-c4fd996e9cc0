import '../repositories/auth_repository.dart';

/// Use case for logging out the current user
class LogoutUseCase {
  final AuthRepository _authRepository;

  const LogoutUseCase(this._authRepository);

  /// Execute the logout process
  /// 
  /// Clears authentication state and cached credentials
  /// 
  /// Throws: Exception if logout fails
  Future<void> call() async {
    try {
      // Clear cached credentials first
      await _authRepository.clearCachedCredentials();
      
      // Logout from remote authentication
      await _authRepository.logout();
    } catch (e) {
      throw Exception('Logout failed: ${e.toString()}');
    }
  }
}