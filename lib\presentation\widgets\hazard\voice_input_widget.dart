import 'package:flutter/material.dart';

/// Widget for voice input functionality in hazard documentation
/// 
/// Provides interface for starting/stopping voice recognition,
/// displaying recognized text, and managing voice input state.
class VoiceInputWidget extends StatelessWidget {
  final bool isListening;
  final String recognizedText;
  final VoidCallback? onStartListening;
  final VoidCallback? onStopListening;
  final VoidCallback? onCancelListening;
  final VoidCallback? onUseText;
  final String? error;
  final double confidence;

  const VoiceInputWidget({
    super.key,
    required this.isListening,
    required this.recognizedText,
    this.onStartListening,
    this.onStopListening,
    this.onCancelListening,
    this.onUseText,
    this.error,
    this.confidence = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Voice input button
        Row(
          children: [
            IconButton(
              onPressed: isListening ? onStopListening : onStartListening,
              icon: Icon(
                isListening ? Icons.mic : Icons.mic_none,
                color: isListening ? Colors.red : null,
              ),
              tooltip: isListening ? 'Stop voice input' : 'Start voice input',
            ),
            if (isListening) ...[
              const SizedBox(width: 8),
              const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
              const SizedBox(width: 8),
              const Text(
                'Listening...',
                style: TextStyle(color: Colors.red, fontWeight: FontWeight.w500),
              ),
            ] else if (recognizedText.isEmpty) ...[
              const SizedBox(width: 8),
              Text(
                'Tap to start voice input',
                style: TextStyle(color: Colors.grey.shade600),
              ),
            ],
          ],
        ),

        // Error display
        if (error != null)
          Container(
            margin: const EdgeInsets.only(top: 8.0),
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              border: Border.all(color: Colors.red.shade200),
              borderRadius: BorderRadius.circular(4.0),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade600, size: 16),
                const SizedBox(width: 8.0),
                Expanded(
                  child: Text(
                    error!,
                    style: TextStyle(color: Colors.red.shade600, fontSize: 12),
                  ),
                ),
              ],
            ),
          ),

        // Recognized text display
        if (recognizedText.isNotEmpty)
          Container(
            margin: const EdgeInsets.only(top: 8.0),
            padding: const EdgeInsets.all(12.0),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              border: Border.all(color: Colors.blue.shade200),
              borderRadius: BorderRadius.circular(8.0),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.record_voice_over, color: Colors.blue.shade600, size: 16),
                    const SizedBox(width: 8.0),
                    Text(
                      'Voice Input',
                      style: TextStyle(
                        color: Colors.blue.shade600,
                        fontWeight: FontWeight.w500,
                        fontSize: 12,
                      ),
                    ),
                    const Spacer(),
                    if (confidence > 0)
                      Text(
                        '${(confidence * 100).toInt()}% confident',
                        style: TextStyle(
                          color: Colors.blue.shade600,
                          fontSize: 10,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 8.0),
                Text(
                  recognizedText,
                  style: TextStyle(color: Colors.blue.shade800),
                ),
                const SizedBox(height: 8.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    if (onCancelListening != null)
                      TextButton(
                        onPressed: onCancelListening,
                        child: const Text('Cancel'),
                      ),
                    const SizedBox(width: 8.0),
                    if (onUseText != null)
                      ElevatedButton(
                        onPressed: onUseText,
                        child: const Text('Use Text'),
                      ),
                  ],
                ),
              ],
            ),
          ),
      ],
    );
  }
}

/// Compact voice input button for use in forms
class CompactVoiceInputButton extends StatelessWidget {
  final bool isListening;
  final VoidCallback? onPressed;
  final String? tooltip;

  const CompactVoiceInputButton({
    super.key,
    required this.isListening,
    this.onPressed,
    this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: onPressed,
      icon: Icon(
        isListening ? Icons.mic : Icons.mic_none,
        color: isListening ? Colors.red : null,
      ),
      tooltip: tooltip ?? (isListening ? 'Stop voice input' : 'Start voice input'),
    );
  }
}

/// Voice input status indicator
class VoiceInputStatus extends StatelessWidget {
  final bool isListening;
  final bool isAvailable;
  final String? error;

  const VoiceInputStatus({
    super.key,
    required this.isListening,
    required this.isAvailable,
    this.error,
  });

  @override
  Widget build(BuildContext context) {
    if (error != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.mic_off, color: Colors.red.shade600, size: 16),
          const SizedBox(width: 4),
          Text(
            'Voice input error',
            style: TextStyle(color: Colors.red.shade600, fontSize: 12),
          ),
        ],
      );
    }

    if (!isAvailable) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.mic_off, color: Colors.grey.shade600, size: 16),
          const SizedBox(width: 4),
          Text(
            'Voice input unavailable',
            style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
          ),
        ],
      );
    }

    if (isListening) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.mic, color: Colors.red.shade600, size: 16),
          const SizedBox(width: 4),
          Text(
            'Listening...',
            style: TextStyle(color: Colors.red.shade600, fontSize: 12),
          ),
          const SizedBox(width: 4),
          SizedBox(
            width: 12,
            height: 12,
            child: CircularProgressIndicator(
              strokeWidth: 1.5,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.red.shade600),
            ),
          ),
        ],
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(Icons.mic_none, color: Colors.green.shade600, size: 16),
        const SizedBox(width: 4),
        Text(
          'Voice input ready',
          style: TextStyle(color: Colors.green.shade600, fontSize: 12),
        ),
      ],
    );
  }
}

/// Voice input help widget
class VoiceInputHelp extends StatelessWidget {
  const VoiceInputHelp({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.help_outline, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                Text(
                  'Voice Input Tips',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue.shade600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            _buildTip('Speak clearly and at a normal pace'),
            _buildTip('Ensure you\'re in a quiet environment'),
            _buildTip('Hold the device close to your mouth'),
            _buildTip('Pause briefly between sentences'),
            _buildTip('Review the text before using it'),
          ],
        ),
      ),
    );
  }

  Widget _buildTip(String tip) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '• ',
            style: TextStyle(color: Colors.grey.shade600),
          ),
          Expanded(
            child: Text(
              tip,
              style: TextStyle(color: Colors.grey.shade700, fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}

/// Animated voice input visualization
class VoiceInputVisualizer extends StatefulWidget {
  final bool isListening;
  final double amplitude;

  const VoiceInputVisualizer({
    super.key,
    required this.isListening,
    this.amplitude = 0.5,
  });

  @override
  State<VoiceInputVisualizer> createState() => _VoiceInputVisualizerState();
}

class _VoiceInputVisualizerState extends State<VoiceInputVisualizer>
    with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
  }

  @override
  void didUpdateWidget(VoiceInputVisualizer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isListening && !oldWidget.isListening) {
      _animationController.repeat();
    } else if (!widget.isListening && oldWidget.isListening) {
      _animationController.stop();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          width: 60,
          height: 30,
          decoration: BoxDecoration(
            color: widget.isListening ? Colors.red.shade50 : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: widget.isListening ? Colors.red.shade200 : Colors.grey.shade300,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: List.generate(3, (index) {
              final height = widget.isListening
                  ? 4 + (widget.amplitude * 16 * (0.5 + 0.5 * (_animationController.value + index * 0.3) % 1))
                  : 4.0;
              return Container(
                width: 3,
                height: height,
                decoration: BoxDecoration(
                  color: widget.isListening ? Colors.red : Colors.grey,
                  borderRadius: BorderRadius.circular(1.5),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}
