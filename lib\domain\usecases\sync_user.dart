import '../entities/user.dart';
import '../repositories/user_repository.dart';

/// Use case for syncing user data with remote server
class SyncUserUseCase {
  final UserRepository _userRepository;

  const SyncUserUseCase(this._userRepository);

  /// Execute syncing user data
  /// 
  /// Returns: Synced [User] object
  /// Throws: Exception if operation fails
  Future<User> call(String uid) async {
    try {
      return await _userRepository.syncUser(uid);
    } catch (e) {
      throw Exception('Failed to sync user: ${e.toString()}');
    }
  }
}