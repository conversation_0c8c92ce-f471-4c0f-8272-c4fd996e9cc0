# Core Workflows

```mermaid
sequenceDiagram
    participant U as User
    participant A as App
    participant L as Local DB
    participant F as Firebase
    
    Note over U,F: Create New Walkabout Workflow
    
    U->>A: Create new walkabout
    A->>A: Generate local ID
    A->>L: Save walkabout locally
    A->>U: Show walkabout screen
    
    Note over U,F: Document Hazard Workflow
    
    U->>A: Add new hazard
    A->>A: Capture photo
    A->>A: Get GPS location
    A->>L: Save hazard locally
    
    Note over U,F: Sync Workflow (when online)
    
    A->>A: Check connectivity
    A->>F: Upload photos to Storage
    F-->>A: Return photo URLs
    A->>F: Save walkabout to Firestore
    A->>F: Save hazards to Firestore
    A->>L: Update sync status
```
