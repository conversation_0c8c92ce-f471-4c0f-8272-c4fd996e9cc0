import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:safestride/data/datasources/local/user_local_datasource.dart';
import 'package:safestride/data/datasources/remote/user_remote_datasource.dart';
import 'package:safestride/data/models/user_model.dart';
import 'package:safestride/data/repositories/user_repository_impl.dart';
import 'package:safestride/domain/entities/user.dart';

import 'user_repository_impl_test.mocks.dart';

@GenerateMocks([UserRemoteDataSource, UserLocalDataSource])
void main() {
  late UserRepositoryImpl repository;
  late MockUserRemoteDataSource mockRemoteDataSource;
  late MockUserLocalDataSource mockLocalDataSource;

  setUp(() {
    mockRemoteDataSource = MockUserRemoteDataSource();
    mockLocalDataSource = MockUserLocalDataSource();
    repository = UserRepositoryImpl(
      remoteDataSource: mockRemoteDataSource,
      localDataSource: mockLocalDataSource,
    );
  });

  group('UserRepositoryImpl', () {
    const testUid = 'test-uid-123';
    final testUser = User(
      id: testUid,
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime(2024, 1, 1),
      lastLoginAt: DateTime(2024, 1, 2),
      preferences: UserPreferences(),
    );
    final testUserModel = UserModel.fromEntity(testUser);

    group('getUserById', () {
      test('should return user from remote when available', () async {
        // Arrange
        when(mockRemoteDataSource.getUserById(testUid))
            .thenAnswer((_) async => testUserModel);
        when(mockLocalDataSource.cacheUser(any))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.getUserById(testUid);

        // Assert
        expect(result, equals(testUser));
        verify(mockRemoteDataSource.getUserById(testUid)).called(1);
        verify(mockLocalDataSource.cacheUser(testUserModel)).called(1);
        verifyNever(mockLocalDataSource.getCachedUser(testUid));
      });

      test('should return cached user when remote fails', () async {
        // Arrange
        when(mockRemoteDataSource.getUserById(testUid))
            .thenThrow(Exception('Network error'));
        when(mockLocalDataSource.getCachedUser(testUid))
            .thenAnswer((_) async => testUserModel);

        // Act
        final result = await repository.getUserById(testUid);

        // Assert
        expect(result, equals(testUser));
        verify(mockRemoteDataSource.getUserById(testUid)).called(1);
        verify(mockLocalDataSource.getCachedUser(testUid)).called(1);
        verifyNever(mockLocalDataSource.cacheUser(any));
      });

      test('should throw exception when both remote and local fail', () async {
        // Arrange
        when(mockRemoteDataSource.getUserById(testUid))
            .thenThrow(Exception('Network error'));
        when(mockLocalDataSource.getCachedUser(testUid))
            .thenThrow(Exception('Cache error'));

        // Act & Assert
        expect(
          () async => await repository.getUserById(testUid),
          throwsA(isA<Exception>()),
        );
        verify(mockRemoteDataSource.getUserById(testUid)).called(1);
        verify(mockLocalDataSource.getCachedUser(testUid)).called(1);
      });

      test('should return null when user not found in remote or cache', () async {
        // Arrange
        when(mockRemoteDataSource.getUserById(testUid))
            .thenAnswer((_) async => null);
        when(mockLocalDataSource.getCachedUser(testUid))
            .thenAnswer((_) async => null);

        // Act
        final result = await repository.getUserById(testUid);

        // Assert
        expect(result, isNull);
        verify(mockRemoteDataSource.getUserById(testUid)).called(1);
        verify(mockLocalDataSource.getCachedUser(testUid)).called(1);
      });
    });

    group('updateUser', () {
      test('should update user in remote and cache when remote succeeds', () async {
        // Arrange
        final updatedUser = testUser.copyWith(
          displayName: 'Updated User',
          updatedAt: DateTime(2024, 1, 3),
        );
        final updatedUserModel = UserModel.fromEntity(updatedUser);
        
        when(mockRemoteDataSource.updateUser(any))
            .thenAnswer((_) async => updatedUserModel);
        when(mockLocalDataSource.cacheUser(any))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.updateUser(updatedUser);

        // Assert
        expect(result, equals(updatedUser));
        verify(mockRemoteDataSource.updateUser(any)).called(1);
        verify(mockLocalDataSource.cacheUser(updatedUserModel)).called(1);
        verifyNever(mockLocalDataSource.markForSync(any));
      });

      test('should mark for sync when remote update fails', () async {
        // Arrange
        final updatedUser = testUser.copyWith(
          displayName: 'Updated User',
          updatedAt: DateTime(2024, 1, 3),
        );
        final updatedUserModel = UserModel.fromEntity(updatedUser);
        
        when(mockRemoteDataSource.updateUser(any))
            .thenThrow(Exception('Network error'));
        when(mockLocalDataSource.cacheUser(any))
            .thenAnswer((_) async {});
        when(mockLocalDataSource.markForSync(testUid))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.updateUser(updatedUser);

        // Assert
        expect(result, equals(updatedUser));
        verify(mockRemoteDataSource.updateUser(any)).called(1);
        verify(mockLocalDataSource.cacheUser(updatedUserModel)).called(1);
        verify(mockLocalDataSource.markForSync(testUid)).called(1);
      });

      test('should throw exception when local cache also fails', () async {
        // Arrange
        final updatedUser = testUser.copyWith(
          displayName: 'Updated User',
          updatedAt: DateTime(2024, 1, 3),
        );
        
        when(mockRemoteDataSource.updateUser(any))
            .thenThrow(Exception('Network error'));
        when(mockLocalDataSource.cacheUser(any))
            .thenThrow(Exception('Cache error'));

        // Act & Assert
        expect(
          () async => await repository.updateUser(updatedUser),
          throwsA(isA<Exception>()),
        );
        verify(mockRemoteDataSource.updateUser(any)).called(1);
        verify(mockLocalDataSource.cacheUser(any)).called(1);
        verifyNever(mockLocalDataSource.markForSync(any));
      });
    });

    group('updateUserPreferences', () {
      test('should update user preferences successfully', () async {
        // Arrange
        final newPreferences = UserPreferences(
          language: 'es',
          theme: 'dark',
          notifications: false,
        );
        final updatedUser = testUser.copyWith(
          preferences: newPreferences,
          updatedAt: DateTime(2024, 1, 3),
        );
        final updatedUserModel = UserModel.fromEntity(updatedUser);
        
        when(mockRemoteDataSource.updateUser(any))
            .thenAnswer((_) async => updatedUserModel);
        when(mockLocalDataSource.cacheUser(any))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.updateUserPreferences(
          testUid,
          newPreferences,
        );

        // Assert
        expect(result.preferences, equals(newPreferences));
        verify(mockRemoteDataSource.updateUser(any)).called(1);
        verify(mockLocalDataSource.cacheUser(any)).called(1);
      });
    });

    group('updateUserRole', () {
      test('should update user role successfully', () async {
        // Arrange
        const newRole = UserRole.manager;
        final updatedUser = testUser.copyWith(
          role: newRole,
          updatedAt: DateTime(2024, 1, 3),
        );
        final updatedUserModel = UserModel.fromEntity(updatedUser);
        
        when(mockRemoteDataSource.updateUser(any))
            .thenAnswer((_) async => updatedUserModel);
        when(mockLocalDataSource.cacheUser(any))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.updateUserRole(testUid, newRole);

        // Assert
        expect(result.role, equals(newRole));
        verify(mockRemoteDataSource.updateUser(any)).called(1);
        verify(mockLocalDataSource.cacheUser(any)).called(1);
      });
    });

    group('syncUser', () {
      test('should sync user when marked for sync', () async {
        // Arrange
        when(mockLocalDataSource.needsSync(testUid))
            .thenAnswer((_) async => true);
        when(mockLocalDataSource.getCachedUser(testUid))
            .thenAnswer((_) async => testUserModel);
        when(mockRemoteDataSource.updateUser(any))
            .thenAnswer((_) async => testUserModel);
        when(mockLocalDataSource.cacheUser(any))
            .thenAnswer((_) async {});
        when(mockLocalDataSource.clearSyncFlag(testUid))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.syncUser(testUid);

        // Assert
        expect(result, equals(testUser));
        verify(mockLocalDataSource.needsSync(testUid)).called(1);
        verify(mockLocalDataSource.getCachedUser(testUid)).called(1);
        verify(mockRemoteDataSource.updateUser(any)).called(1);
        verify(mockLocalDataSource.cacheUser(any)).called(1);
        verify(mockLocalDataSource.clearSyncFlag(testUid)).called(1);
      });

      test('should get fresh user data when not marked for sync', () async {
        // Arrange
        when(mockLocalDataSource.needsSync(testUid))
            .thenAnswer((_) async => false);
        when(mockRemoteDataSource.getUserById(testUid))
            .thenAnswer((_) async => testUserModel);
        when(mockLocalDataSource.cacheUser(any))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.syncUser(testUid);

        // Assert
        expect(result, equals(testUser));
        verify(mockLocalDataSource.needsSync(testUid)).called(1);
        verify(mockRemoteDataSource.getUserById(testUid)).called(1);
        verify(mockLocalDataSource.cacheUser(any)).called(1);
        verifyNever(mockLocalDataSource.getCachedUser(testUid));
        verifyNever(mockLocalDataSource.clearSyncFlag(testUid));
      });

      test('should throw exception when sync fails', () async {
        // Arrange
        when(mockLocalDataSource.needsSync(testUid))
            .thenAnswer((_) async => true);
        when(mockLocalDataSource.getCachedUser(testUid))
            .thenAnswer((_) async => testUserModel);
        when(mockRemoteDataSource.updateUser(any))
            .thenThrow(Exception('Network error'));

        // Act & Assert
        expect(
          () async => await repository.syncUser(testUid),
          throwsA(isA<Exception>()),
        );
        verify(mockLocalDataSource.needsSync(testUid)).called(1);
        verify(mockLocalDataSource.getCachedUser(testUid)).called(1);
        verify(mockRemoteDataSource.updateUser(any)).called(1);
        verifyNever(mockLocalDataSource.clearSyncFlag(testUid));
      });
    });

    group('getCachedUser', () {
      test('should return cached user', () async {
        // Arrange
        when(mockLocalDataSource.getCachedUser(testUid))
            .thenAnswer((_) async => testUserModel);

        // Act
        final result = await repository.getCachedUser(testUid);

        // Assert
        expect(result, equals(testUser));
        verify(mockLocalDataSource.getCachedUser(testUid)).called(1);
      });

      test('should return null when no cached user', () async {
        // Arrange
        when(mockLocalDataSource.getCachedUser(testUid))
            .thenAnswer((_) async => null);

        // Act
        final result = await repository.getCachedUser(testUid);

        // Assert
        expect(result, isNull);
        verify(mockLocalDataSource.getCachedUser(testUid)).called(1);
      });
    });

    group('cacheUser', () {
      test('should cache user successfully', () async {
        // Arrange
        when(mockLocalDataSource.cacheUser(any))
            .thenAnswer((_) async {});

        // Act
        await repository.cacheUser(testUser);

        // Assert
        verify(mockLocalDataSource.cacheUser(any)).called(1);
      });

      test('should throw exception when caching fails', () async {
        // Arrange
        when(mockLocalDataSource.cacheUser(any))
            .thenThrow(Exception('Cache error'));

        // Act & Assert
        expect(
          () async => await repository.cacheUser(testUser),
          throwsA(isA<Exception>()),
        );
        verify(mockLocalDataSource.cacheUser(any)).called(1);
      });
    });
  });
}