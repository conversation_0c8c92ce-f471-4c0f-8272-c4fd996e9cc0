import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/entities/hazard.dart';
import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/domain/repositories/hazard_repository.dart';
import 'package:safestride/domain/usecases/update_hazard.dart';

import 'update_hazard_test.mocks.dart';

@GenerateMocks([HazardRepository])
void main() {
  group('UpdateHazardUseCase Tests', () {
    late UpdateHazardUseCase useCase;
    late MockHazardRepository mockRepository;
    late Hazard existingHazard;
    late UpdateHazardParams validParams;
    late GeoPoint testLocation;

    setUp(() {
      mockRepository = MockHazardRepository();
      useCase = UpdateHazardUseCase(repository: mockRepository);
      testLocation = const GeoPoint(latitude: 37.7749, longitude: -122.4194);

      existingHazard = Hazard(
        id: 'hazard_123',
        walkaboutId: 'walkabout_456',
        title: 'Original Title',
        description: 'Original description',
        severity: HazardSeverity.low,
        category: HazardCategory.slipTripFall,
        location: testLocation,
        photos: ['photo1.jpg'],
        notes: 'Original notes',
        createdAt: DateTime(2024, 1, 1, 10, 0, 0),
        updatedAt: DateTime(2024, 1, 1, 10, 0, 0),
        syncStatus: SyncStatus.synced,
      );

      validParams = const UpdateHazardParams(
        id: 'hazard_123',
        title: 'Updated Title',
        description: 'Updated description',
        severity: HazardSeverity.high,
        category: HazardCategory.electrical,
        location: GeoPoint(latitude: 40.7128, longitude: -74.0060),
        photos: ['photo1.jpg', 'photo2.jpg'],
        notes: 'Updated notes',
      );
    });

    group('Successful Updates', () {
      test('should update hazard successfully with all fields', () async {
        // Arrange
        when(
          mockRepository.getHazardById('hazard_123'),
        ).thenAnswer((_) async => existingHazard);

        final updatedHazard = existingHazard.copyWith(
          title: validParams.title,
          description: validParams.description,
          severity: validParams.severity,
          category: validParams.category,
          location: validParams.location,
          photos: validParams.photos,
          notes: validParams.notes,
          updatedAt: DateTime.now(),
          syncStatus: SyncStatus.local,
        );

        when(
          mockRepository.updateHazard(any),
        ).thenAnswer((_) async => updatedHazard);

        // Act
        final result = await useCase.call(validParams);

        // Assert
        expect(result.title, equals('Updated Title'));
        expect(result.description, equals('Updated description'));
        expect(result.severity, equals(HazardSeverity.high));
        expect(result.category, equals(HazardCategory.electrical));
        expect(result.location?.latitude, equals(40.7128));
        expect(result.location?.longitude, equals(-74.0060));
        expect(result.photos, equals(['photo1.jpg', 'photo2.jpg']));
        expect(result.notes, equals('Updated notes'));
        expect(result.syncStatus, equals(SyncStatus.local));

        verify(mockRepository.getHazardById('hazard_123')).called(1);
        verify(mockRepository.updateHazard(any)).called(1);
      });

      test('should update hazard with partial fields', () async {
        // Arrange
        final partialParams = const UpdateHazardParams(
          id: 'hazard_123',
          title: 'New Title Only',
        );

        when(
          mockRepository.getHazardById('hazard_123'),
        ).thenAnswer((_) async => existingHazard);

        final updatedHazard = existingHazard.copyWith(
          title: 'New Title Only',
          updatedAt: DateTime.now(),
          syncStatus: SyncStatus.local,
        );

        when(
          mockRepository.updateHazard(any),
        ).thenAnswer((_) async => updatedHazard);

        // Act
        final result = await useCase.call(partialParams);

        // Assert
        expect(result.title, equals('New Title Only'));
        expect(result.description, equals('Original description')); // Unchanged
        expect(result.severity, equals(HazardSeverity.low)); // Unchanged
        expect(result.syncStatus, equals(SyncStatus.local));

        verify(mockRepository.getHazardById('hazard_123')).called(1);
        verify(mockRepository.updateHazard(any)).called(1);
      });
    });

    group('Validation Tests', () {
      test('should throw ArgumentError for empty hazard ID', () async {
        // Arrange
        final invalidParams = validParams.copyWith(id: '');

        // Act & Assert
        expect(
          () => useCase.call(invalidParams),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              contains('Hazard ID is required'),
            ),
          ),
        );

        verifyNever(mockRepository.getHazardById(any));
        verifyNever(mockRepository.updateHazard(any));
      });

      test(
        'should throw ArgumentError for whitespace-only hazard ID',
        () async {
          // Arrange
          final invalidParams = validParams.copyWith(id: '   ');

          // Act & Assert
          expect(
            () => useCase.call(invalidParams),
            throwsA(
              isA<ArgumentError>().having(
                (e) => e.message,
                'message',
                contains('Hazard ID is required'),
              ),
            ),
          );
        },
      );

      test('should throw ArgumentError for empty title', () async {
        // Arrange
        final invalidParams = validParams.copyWith(title: '');

        // Act & Assert
        expect(
          () => useCase.call(invalidParams),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              contains('title cannot be empty'),
            ),
          ),
        );
      });

      test(
        'should throw ArgumentError for title exceeding 100 characters',
        () async {
          // Arrange
          final longTitle = 'a' * 101;
          final invalidParams = validParams.copyWith(title: longTitle);

          // Act & Assert
          expect(
            () => useCase.call(invalidParams),
            throwsA(
              isA<ArgumentError>().having(
                (e) => e.message,
                'message',
                contains('title cannot exceed 100 characters'),
              ),
            ),
          );
        },
      );

      test(
        'should throw ArgumentError for description exceeding 1000 characters',
        () async {
          // Arrange
          final longDescription = 'a' * 1001;
          final invalidParams = validParams.copyWith(
            description: longDescription,
          );

          // Act & Assert
          expect(
            () => useCase.call(invalidParams),
            throwsA(
              isA<ArgumentError>().having(
                (e) => e.message,
                'message',
                contains('description cannot exceed 1000 characters'),
              ),
            ),
          );
        },
      );

      test(
        'should throw ArgumentError for notes exceeding 500 characters',
        () async {
          // Arrange
          final longNotes = 'a' * 501;
          final invalidParams = validParams.copyWith(notes: longNotes);

          // Act & Assert
          expect(
            () => useCase.call(invalidParams),
            throwsA(
              isA<ArgumentError>().having(
                (e) => e.message,
                'message',
                contains('notes cannot exceed 500 characters'),
              ),
            ),
          );
        },
      );

      test('should throw ArgumentError for invalid latitude', () async {
        // Arrange
        final invalidLocation = const GeoPoint(latitude: 91.0, longitude: 0.0);
        final invalidParams = validParams.copyWith(location: invalidLocation);

        // Act & Assert
        expect(
          () => useCase.call(invalidParams),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              contains('Invalid latitude'),
            ),
          ),
        );
      });

      test('should throw ArgumentError for invalid longitude', () async {
        // Arrange
        final invalidLocation = const GeoPoint(latitude: 0.0, longitude: 181.0);
        final invalidParams = validParams.copyWith(location: invalidLocation);

        // Act & Assert
        expect(
          () => useCase.call(invalidParams),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              contains('Invalid longitude'),
            ),
          ),
        );
      });

      test('should throw ArgumentError for empty photo path', () async {
        // Arrange
        final invalidParams = validParams.copyWith(photos: ['photo1.jpg', '']);

        // Act & Assert
        expect(
          () => useCase.call(invalidParams),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              contains('Photo path cannot be empty'),
            ),
          ),
        );
      });
    });

    group('Error Handling', () {
      test('should throw ArgumentError when hazard not found', () async {
        // Arrange
        when(
          mockRepository.getHazardById('hazard_123'),
        ).thenAnswer((_) async => null);

        // Act & Assert
        expect(
          () => useCase.call(validParams),
          throwsA(
            isA<ArgumentError>().having(
              (e) => e.message,
              'message',
              contains('Hazard with ID hazard_123 not found'),
            ),
          ),
        );

        verify(mockRepository.getHazardById('hazard_123')).called(1);
        verifyNever(mockRepository.updateHazard(any));
      });

      test('should propagate repository get exceptions', () async {
        // Arrange
        when(
          mockRepository.getHazardById('hazard_123'),
        ).thenThrow(Exception('Database connection error'));

        // Act & Assert
        expect(
          () => useCase.call(validParams),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Database connection error'),
            ),
          ),
        );
      });

      test('should propagate repository update exceptions', () async {
        // Arrange
        when(
          mockRepository.getHazardById('hazard_123'),
        ).thenAnswer((_) async => existingHazard);
        when(
          mockRepository.updateHazard(any),
        ).thenThrow(Exception('Update failed'));

        // Act & Assert
        expect(
          () => useCase.call(validParams),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Update failed'),
            ),
          ),
        );
      });
    });

    group('UpdateHazardPhotosUseCase Tests', () {
      late UpdateHazardPhotosUseCase photosUseCase;
      late Hazard existingHazardWithPhotos;

      setUp(() {
        photosUseCase = UpdateHazardPhotosUseCase(repository: mockRepository);

        existingHazardWithPhotos = Hazard(
          id: 'hazard_photos',
          walkaboutId: 'walkabout_456',
          title: 'Hazard with Photos',
          severity: HazardSeverity.medium,
          category: HazardCategory.electrical,
          photos: ['existing1.jpg', 'existing2.jpg'],
          createdAt: DateTime(2024, 1, 1, 10, 0, 0),
          updatedAt: DateTime(2024, 1, 1, 10, 0, 0),
          syncStatus: SyncStatus.synced,
        );
      });

      group('Add Photos', () {
        test('should add photos to existing hazard successfully', () async {
          // Arrange
          final newPhotos = ['new1.jpg', 'new2.jpg'];
          when(
            mockRepository.getHazardById('hazard_photos'),
          ).thenAnswer((_) async => existingHazardWithPhotos);

          final updatedHazard = existingHazardWithPhotos.copyWith(
            photos: ['existing1.jpg', 'existing2.jpg', 'new1.jpg', 'new2.jpg'],
            updatedAt: DateTime.now(),
            syncStatus: SyncStatus.local,
          );

          when(
            mockRepository.updateHazard(any),
          ).thenAnswer((_) async => updatedHazard);

          // Act
          final result = await photosUseCase.addPhotos(
            'hazard_photos',
            newPhotos,
          );

          // Assert
          expect(
            result.photos,
            equals(['existing1.jpg', 'existing2.jpg', 'new1.jpg', 'new2.jpg']),
          );
          expect(result.syncStatus, equals(SyncStatus.local));

          verify(mockRepository.getHazardById('hazard_photos')).called(1);
          verify(mockRepository.updateHazard(any)).called(1);
        });

        test(
          'should throw ArgumentError when hazard not found for adding photos',
          () async {
            // Arrange
            when(
              mockRepository.getHazardById('non_existent'),
            ).thenAnswer((_) async => null);

            // Act & Assert
            expect(
              () => photosUseCase.addPhotos('non_existent', ['new.jpg']),
              throwsA(
                isA<ArgumentError>().having(
                  (e) => e.message,
                  'message',
                  contains('Hazard with ID non_existent not found'),
                ),
              ),
            );

            verifyNever(mockRepository.updateHazard(any));
          },
        );
      });

      group('Remove Photos', () {
        test(
          'should remove photos from existing hazard successfully',
          () async {
            // Arrange
            final photosToRemove = ['existing1.jpg'];
            when(
              mockRepository.getHazardById('hazard_photos'),
            ).thenAnswer((_) async => existingHazardWithPhotos);

            final updatedHazard = existingHazardWithPhotos.copyWith(
              photos: ['existing2.jpg'],
              updatedAt: DateTime.now(),
              syncStatus: SyncStatus.local,
            );

            when(
              mockRepository.updateHazard(any),
            ).thenAnswer((_) async => updatedHazard);

            // Act
            final result = await photosUseCase.removePhotos(
              'hazard_photos',
              photosToRemove,
            );

            // Assert
            expect(result.photos, equals(['existing2.jpg']));
            expect(result.syncStatus, equals(SyncStatus.local));

            verify(mockRepository.getHazardById('hazard_photos')).called(1);
            verify(mockRepository.updateHazard(any)).called(1);
          },
        );

        test('should handle removing non-existent photos gracefully', () async {
          // Arrange
          final photosToRemove = ['non_existent.jpg'];
          when(
            mockRepository.getHazardById('hazard_photos'),
          ).thenAnswer((_) async => existingHazardWithPhotos);

          final updatedHazard = existingHazardWithPhotos.copyWith(
            photos: ['existing1.jpg', 'existing2.jpg'], // No change
            updatedAt: DateTime.now(),
            syncStatus: SyncStatus.local,
          );

          when(
            mockRepository.updateHazard(any),
          ).thenAnswer((_) async => updatedHazard);

          // Act
          final result = await photosUseCase.removePhotos(
            'hazard_photos',
            photosToRemove,
          );

          // Assert
          expect(result.photos, equals(['existing1.jpg', 'existing2.jpg']));

          verify(mockRepository.getHazardById('hazard_photos')).called(1);
          verify(mockRepository.updateHazard(any)).called(1);
        });

        test(
          'should throw ArgumentError when hazard not found for removing photos',
          () async {
            // Arrange
            when(
              mockRepository.getHazardById('non_existent'),
            ).thenAnswer((_) async => null);

            // Act & Assert
            expect(
              () => photosUseCase.removePhotos('non_existent', ['photo.jpg']),
              throwsA(
                isA<ArgumentError>().having(
                  (e) => e.message,
                  'message',
                  contains('Hazard with ID non_existent not found'),
                ),
              ),
            );

            verifyNever(mockRepository.updateHazard(any));
          },
        );
      });
    });
  });
}

// Extension to help with copyWith for test parameters
extension UpdateHazardParamsExtension on UpdateHazardParams {
  UpdateHazardParams copyWith({
    String? id,
    String? title,
    String? description,
    HazardSeverity? severity,
    HazardCategory? category,
    GeoPoint? location,
    List<String>? photos,
    String? notes,
  }) {
    return UpdateHazardParams(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      severity: severity ?? this.severity,
      category: category ?? this.category,
      location: location ?? this.location,
      photos: photos ?? this.photos,
      notes: notes ?? this.notes,
    );
  }
}
