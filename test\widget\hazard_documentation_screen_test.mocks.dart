// Mocks generated by <PERSON>cki<PERSON> 5.4.6 from annotations
// in safestride/test/widget/hazard_documentation_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i11;
import 'dart:ui' as _i13;

import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i10;
import 'package:safestride/domain/entities/hazard.dart' as _i9;
import 'package:safestride/domain/entities/walkabout.dart' as _i12;
import 'package:safestride/domain/repositories/hazard_repository.dart' as _i4;
import 'package:safestride/domain/usecases/create_hazard.dart' as _i2;
import 'package:safestride/domain/usecases/update_hazard.dart' as _i3;
import 'package:safestride/presentation/providers/hazard_provider.dart' as _i8;
import 'package:safestride/services/camera/camera_service.dart' as _i5;
import 'package:safestride/services/location/location_service.dart' as _i6;
import 'package:safestride/services/voice/voice_input_service.dart' as _i7;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeCreateHazardUseCase_0 extends _i1.SmartFake
    implements _i2.CreateHazardUseCase {
  _FakeCreateHazardUseCase_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeUpdateHazardUseCase_1 extends _i1.SmartFake
    implements _i3.UpdateHazardUseCase {
  _FakeUpdateHazardUseCase_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeHazardRepository_2 extends _i1.SmartFake
    implements _i4.HazardRepository {
  _FakeHazardRepository_2(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeCameraService_3 extends _i1.SmartFake implements _i5.CameraService {
  _FakeCameraService_3(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLocationService_4 extends _i1.SmartFake
    implements _i6.LocationService {
  _FakeLocationService_4(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeVoiceInputService_5 extends _i1.SmartFake
    implements _i7.VoiceInputService {
  _FakeVoiceInputService_5(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [HazardProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockHazardProvider extends _i1.Mock implements _i8.HazardProvider {
  MockHazardProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.CreateHazardUseCase get createHazardUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#createHazardUseCase),
            returnValue: _FakeCreateHazardUseCase_0(
              this,
              Invocation.getter(#createHazardUseCase),
            ),
          )
          as _i2.CreateHazardUseCase);

  @override
  _i3.UpdateHazardUseCase get updateHazardUseCase =>
      (super.noSuchMethod(
            Invocation.getter(#updateHazardUseCase),
            returnValue: _FakeUpdateHazardUseCase_1(
              this,
              Invocation.getter(#updateHazardUseCase),
            ),
          )
          as _i3.UpdateHazardUseCase);

  @override
  _i4.HazardRepository get hazardRepository =>
      (super.noSuchMethod(
            Invocation.getter(#hazardRepository),
            returnValue: _FakeHazardRepository_2(
              this,
              Invocation.getter(#hazardRepository),
            ),
          )
          as _i4.HazardRepository);

  @override
  _i5.CameraService get cameraService =>
      (super.noSuchMethod(
            Invocation.getter(#cameraService),
            returnValue: _FakeCameraService_3(
              this,
              Invocation.getter(#cameraService),
            ),
          )
          as _i5.CameraService);

  @override
  _i6.LocationService get locationService =>
      (super.noSuchMethod(
            Invocation.getter(#locationService),
            returnValue: _FakeLocationService_4(
              this,
              Invocation.getter(#locationService),
            ),
          )
          as _i6.LocationService);

  @override
  _i7.VoiceInputService get voiceInputService =>
      (super.noSuchMethod(
            Invocation.getter(#voiceInputService),
            returnValue: _FakeVoiceInputService_5(
              this,
              Invocation.getter(#voiceInputService),
            ),
          )
          as _i7.VoiceInputService);

  @override
  List<_i9.Hazard> get hazards =>
      (super.noSuchMethod(
            Invocation.getter(#hazards),
            returnValue: <_i9.Hazard>[],
          )
          as List<_i9.Hazard>);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  _i9.HazardSeverity get selectedSeverity =>
      (super.noSuchMethod(
            Invocation.getter(#selectedSeverity),
            returnValue: _i9.HazardSeverity.low,
          )
          as _i9.HazardSeverity);

  @override
  _i9.HazardCategory get selectedCategory =>
      (super.noSuchMethod(
            Invocation.getter(#selectedCategory),
            returnValue: _i9.HazardCategory.slipTripFall,
          )
          as _i9.HazardCategory);

  @override
  List<String> get selectedPhotos =>
      (super.noSuchMethod(
            Invocation.getter(#selectedPhotos),
            returnValue: <String>[],
          )
          as List<String>);

  @override
  bool get isVoiceInputActive =>
      (super.noSuchMethod(
            Invocation.getter(#isVoiceInputActive),
            returnValue: false,
          )
          as bool);

  @override
  String get voiceInputText =>
      (super.noSuchMethod(
            Invocation.getter(#voiceInputText),
            returnValue: _i10.dummyValue<String>(
              this,
              Invocation.getter(#voiceInputText),
            ),
          )
          as String);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  List<_i9.Hazard> getHazardsByWalkabout(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsByWalkabout, [walkaboutId]),
            returnValue: <_i9.Hazard>[],
          )
          as List<_i9.Hazard>);

  @override
  List<_i9.Hazard> getHazardsBySeverity(
    String? walkaboutId,
    _i9.HazardSeverity? severity,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsBySeverity, [walkaboutId, severity]),
            returnValue: <_i9.Hazard>[],
          )
          as List<_i9.Hazard>);

  @override
  List<_i9.Hazard> getHazardsByCategory(
    String? walkaboutId,
    _i9.HazardCategory? category,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardsByCategory, [walkaboutId, category]),
            returnValue: <_i9.Hazard>[],
          )
          as List<_i9.Hazard>);

  @override
  Map<String, dynamic> getHazardStatistics(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#getHazardStatistics, [walkaboutId]),
            returnValue: <String, dynamic>{},
          )
          as Map<String, dynamic>);

  @override
  _i11.Future<void> loadHazards(String? walkaboutId) =>
      (super.noSuchMethod(
            Invocation.method(#loadHazards, [walkaboutId]),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<_i9.Hazard?> createHazard({
    required String? walkaboutId,
    required String? title,
    String? description,
    required _i9.HazardSeverity? severity,
    required _i9.HazardCategory? category,
    _i12.GeoPoint? location,
    List<String>? photos,
    String? notes,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#createHazard, [], {
              #walkaboutId: walkaboutId,
              #title: title,
              #description: description,
              #severity: severity,
              #category: category,
              #location: location,
              #photos: photos,
              #notes: notes,
            }),
            returnValue: _i11.Future<_i9.Hazard?>.value(),
          )
          as _i11.Future<_i9.Hazard?>);

  @override
  _i11.Future<_i9.Hazard?> updateHazard({
    required String? id,
    String? title,
    String? description,
    _i9.HazardSeverity? severity,
    _i9.HazardCategory? category,
    _i12.GeoPoint? location,
    List<String>? photos,
    String? notes,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateHazard, [], {
              #id: id,
              #title: title,
              #description: description,
              #severity: severity,
              #category: category,
              #location: location,
              #photos: photos,
              #notes: notes,
            }),
            returnValue: _i11.Future<_i9.Hazard?>.value(),
          )
          as _i11.Future<_i9.Hazard?>);

  @override
  _i11.Future<bool> deleteHazard(String? id) =>
      (super.noSuchMethod(
            Invocation.method(#deleteHazard, [id]),
            returnValue: _i11.Future<bool>.value(false),
          )
          as _i11.Future<bool>);

  @override
  void setCurrentHazard(_i9.Hazard? hazard) => super.noSuchMethod(
    Invocation.method(#setCurrentHazard, [hazard]),
    returnValueForMissingStub: null,
  );

  @override
  void setSelectedSeverity(_i9.HazardSeverity? severity) => super.noSuchMethod(
    Invocation.method(#setSelectedSeverity, [severity]),
    returnValueForMissingStub: null,
  );

  @override
  void setSelectedCategory(_i9.HazardCategory? category) => super.noSuchMethod(
    Invocation.method(#setSelectedCategory, [category]),
    returnValueForMissingStub: null,
  );

  @override
  void setSelectedLocation(_i12.GeoPoint? location) => super.noSuchMethod(
    Invocation.method(#setSelectedLocation, [location]),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<void> getCurrentLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentLocation, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> capturePhoto() =>
      (super.noSuchMethod(
            Invocation.method(#capturePhoto, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> selectPhotoFromGallery() =>
      (super.noSuchMethod(
            Invocation.method(#selectPhotoFromGallery, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> selectMultiplePhotos() =>
      (super.noSuchMethod(
            Invocation.method(#selectMultiplePhotos, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void removePhoto(String? photoPath) => super.noSuchMethod(
    Invocation.method(#removePhoto, [photoPath]),
    returnValueForMissingStub: null,
  );

  @override
  void clearPhotos() => super.noSuchMethod(
    Invocation.method(#clearPhotos, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<void> startVoiceInput() =>
      (super.noSuchMethod(
            Invocation.method(#startVoiceInput, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> stopVoiceInput() =>
      (super.noSuchMethod(
            Invocation.method(#stopVoiceInput, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  _i11.Future<void> cancelVoiceInput() =>
      (super.noSuchMethod(
            Invocation.method(#cancelVoiceInput, []),
            returnValue: _i11.Future<void>.value(),
            returnValueForMissingStub: _i11.Future<void>.value(),
          )
          as _i11.Future<void>);

  @override
  void clearForm() => super.noSuchMethod(
    Invocation.method(#clearForm, []),
    returnValueForMissingStub: null,
  );

  @override
  _i11.Future<List<_i9.Hazard>> searchHazards(
    String? walkaboutId,
    String? query,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#searchHazards, [walkaboutId, query]),
            returnValue: _i11.Future<List<_i9.Hazard>>.value(<_i9.Hazard>[]),
          )
          as _i11.Future<List<_i9.Hazard>>);

  @override
  void addListener(_i13.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i13.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}
