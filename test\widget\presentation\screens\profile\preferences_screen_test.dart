import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/presentation/providers/user_profile_provider.dart';
import 'package:safestride/presentation/screens/profile/preferences_screen.dart';
import 'package:safestride/presentation/widgets/app_bar_widget.dart';
import 'package:safestride/presentation/widgets/loading_indicator.dart';

import 'preferences_screen_test.mocks.dart';

@GenerateMocks([UserProfileProvider])
void main() {
  late MockUserProfileProvider mockUserProfileProvider;

  setUp(() {
    mockUserProfileProvider = MockUserProfileProvider();
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: ChangeNotifierProvider<UserProfileProvider>.value(
        value: mockUserProfileProvider,
        child: const PreferencesScreen(),
      ),
    );
  }

  final testUser = User(
    id: 'test-uid-123',
    email: '<EMAIL>',
    displayName: 'Test User',
    organization: 'Test Organization',
    role: UserRole.inspector,
    createdAt: DateTime(2024, 1, 1),
    lastLoginAt: DateTime(2024, 1, 2),
    preferences: UserPreferences(
      notificationsEnabled: true,
      language: 'English',
      theme: 'Light',
      offlineMode: false,
    ),
  );

  group('PreferencesScreen Widget Tests', () {
    testWidgets('should display app bar with correct title', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow initialization

      // Assert
      expect(find.byType(AppBarWidget), findsOneWidget);
      expect(find.text('Preferences'), findsOneWidget);
    });

    testWidgets('should display loading indicator when loading', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(true);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loading);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(LoadingIndicator), findsOneWidget);
    });

    testWidgets('should display user not found when user is null', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(null);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('User not found'), findsOneWidget);
    });

    testWidgets('should display preferences sections when loaded', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump(); // Allow initialization

      // Assert
      expect(find.text('Notifications'), findsOneWidget);
      expect(find.text('Appearance'), findsOneWidget);
      expect(find.text('Data & Storage'), findsOneWidget);
    });

    testWidgets('should display notifications section with correct initial values', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Enable Notifications'), findsOneWidget);
      expect(find.text('Receive alerts and updates'), findsOneWidget);
      expect(find.byIcon(Icons.notifications), findsOneWidget);
      expect(find.byIcon(Icons.notifications_active), findsOneWidget);
      
      final notificationSwitch = tester.widget<SwitchListTile>(
        find.widgetWithText(SwitchListTile, 'Enable Notifications'),
      );
      expect(notificationSwitch.value, isTrue);
    });

    testWidgets('should display appearance section with language and theme dropdowns', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
      expect(find.text('English'), findsOneWidget);
      expect(find.text('Light'), findsOneWidget);
      expect(find.byIcon(Icons.palette), findsOneWidget);
      expect(find.byIcon(Icons.language), findsOneWidget);
      expect(find.byIcon(Icons.dark_mode), findsOneWidget);
      expect(find.byType(DropdownButton<String>), findsNWidgets(2));
    });

    testWidgets('should display data section with offline mode switch', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Offline Mode'), findsOneWidget);
      expect(find.text('Access app features without internet'), findsOneWidget);
      expect(find.byIcon(Icons.storage), findsOneWidget);
      expect(find.byIcon(Icons.cloud_done), findsOneWidget);
      
      final offlineModeSwitch = tester.widget<SwitchListTile>(
        find.widgetWithText(SwitchListTile, 'Offline Mode'),
      );
      expect(offlineModeSwitch.value, isFalse);
    });

    testWidgets('should display action buttons', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text('Cancel'), findsOneWidget);
      expect(find.text('Save'), findsOneWidget);
      expect(find.byType(OutlinedButton), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should not show save icon in app bar initially', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.byIcon(Icons.save), findsNothing);
    });

    testWidgets('should show save icon in app bar when changes are made', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Toggle notification switch to make changes
      await tester.tap(find.widgetWithText(SwitchListTile, 'Enable Notifications'));
      await tester.pump();

      // Assert
      expect(find.byIcon(Icons.save), findsOneWidget);
    });

    testWidgets('should toggle notification switch and show changes', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Toggle notification switch
      await tester.tap(find.widgetWithText(SwitchListTile, 'Enable Notifications'));
      await tester.pump();

      // Assert
      final notificationSwitch = tester.widget<SwitchListTile>(
        find.widgetWithText(SwitchListTile, 'Enable Notifications'),
      );
      expect(notificationSwitch.value, isFalse);
      expect(find.byIcon(Icons.notifications_off), findsOneWidget);
    });

    testWidgets('should toggle offline mode switch and show changes', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Toggle offline mode switch
      await tester.tap(find.widgetWithText(SwitchListTile, 'Offline Mode'));
      await tester.pump();

      // Assert
      final offlineModeSwitch = tester.widget<SwitchListTile>(
        find.widgetWithText(SwitchListTile, 'Offline Mode'),
      );
      expect(offlineModeSwitch.value, isTrue);
      expect(find.byIcon(Icons.cloud_off), findsOneWidget);
    });

    testWidgets('should change language dropdown and show changes', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Find and tap the language dropdown
      final languageDropdown = find.ancestor(
        of: find.text('English'),
        matching: find.byType(DropdownButton<String>),
      );
      await tester.tap(languageDropdown);
      await tester.pumpAndSettle();

      // Select Spanish
      await tester.tap(find.text('Spanish').last);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Spanish'), findsOneWidget);
      expect(find.byIcon(Icons.save), findsOneWidget);
    });

    testWidgets('should change theme dropdown and show changes', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Find and tap the theme dropdown
      final themeDropdown = find.ancestor(
        of: find.text('Light'),
        matching: find.byType(DropdownButton<String>),
      );
      await tester.tap(themeDropdown);
      await tester.pumpAndSettle();

      // Select Dark
      await tester.tap(find.text('Dark').last);
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Dark'), findsOneWidget);
      expect(find.byIcon(Icons.save), findsOneWidget);
    });

    testWidgets('should disable save button when no changes', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      final saveButton = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(saveButton.onPressed, isNull);
    });

    testWidgets('should enable save button when changes are made', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Make a change
      await tester.tap(find.widgetWithText(SwitchListTile, 'Enable Notifications'));
      await tester.pump();

      // Assert
      final saveButton = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(saveButton.onPressed, isNotNull);
    });

    testWidgets('should call updatePreferences on save', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.updatePreferences(
        notificationsEnabled: anyNamed('notificationsEnabled'),
        language: anyNamed('language'),
        theme: anyNamed('theme'),
        offlineMode: anyNamed('offlineMode'),
      )).thenAnswer((_) async {});

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Make a change
      await tester.tap(find.widgetWithText(SwitchListTile, 'Enable Notifications'));
      await tester.pump();

      // Save
      await tester.tap(find.text('Save'));
      await tester.pump();

      // Assert
      verify(mockUserProfileProvider.updatePreferences(
        notificationsEnabled: false,
        language: 'English',
        theme: 'Light',
        offlineMode: false,
      )).called(1);
    });

    testWidgets('should show success message after saving', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.updatePreferences(
        notificationsEnabled: anyNamed('notificationsEnabled'),
        language: anyNamed('language'),
        theme: anyNamed('theme'),
        offlineMode: anyNamed('offlineMode'),
      )).thenAnswer((_) async {});

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Make a change and save
      await tester.tap(find.widgetWithText(SwitchListTile, 'Enable Notifications'));
      await tester.pump();
      await tester.tap(find.text('Save'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Preferences updated successfully'), findsOneWidget);
    });

    testWidgets('should show discard changes dialog when canceling with changes', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Make a change
      await tester.tap(find.widgetWithText(SwitchListTile, 'Enable Notifications'));
      await tester.pump();

      // Tap cancel
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Discard Changes?'), findsOneWidget);
      expect(find.text('You have unsaved changes. Are you sure you want to discard them?'), findsOneWidget);
      expect(find.text('Discard'), findsOneWidget);
    });

    testWidgets('should navigate back directly when canceling without changes', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Tap cancel without making changes
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Assert - Should navigate back (screen should be popped)
      expect(find.byType(PreferencesScreen), findsNothing);
    });

    testWidgets('should display error message when present', (WidgetTester tester) async {
      // Arrange
      const errorMessage = 'Failed to update preferences';
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.error);
      when(mockUserProfileProvider.errorMessage).thenReturn(errorMessage);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Assert
      expect(find.text(errorMessage), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should disable save button when loading', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(true);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loading);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Make a change to enable save button
      await tester.tap(find.widgetWithText(SwitchListTile, 'Enable Notifications'));
      await tester.pump();

      // Assert
      final saveButton = tester.widget<ElevatedButton>(find.byType(ElevatedButton));
      expect(saveButton.onPressed, isNull);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should save from app bar icon', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.updatePreferences(
        notificationsEnabled: anyNamed('notificationsEnabled'),
        language: anyNamed('language'),
        theme: anyNamed('theme'),
        offlineMode: anyNamed('offlineMode'),
      )).thenAnswer((_) async {});

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Make a change
      await tester.tap(find.widgetWithText(SwitchListTile, 'Enable Notifications'));
      await tester.pump();

      // Save from app bar
      await tester.tap(find.byIcon(Icons.save));
      await tester.pump();

      // Assert
      verify(mockUserProfileProvider.updatePreferences(
        notificationsEnabled: false,
        language: 'English',
        theme: 'Light',
        offlineMode: false,
      )).called(1);
    });

    testWidgets('should handle discard changes dialog actions', (WidgetTester tester) async {
      // Arrange
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.pump();

      // Make a change and cancel
      await tester.tap(find.widgetWithText(SwitchListTile, 'Enable Notifications'));
      await tester.pump();
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Cancel the dialog
      await tester.tap(find.text('Cancel').last);
      await tester.pumpAndSettle();

      // Assert - Should still be on preferences screen
      expect(find.byType(PreferencesScreen), findsOneWidget);
      expect(find.text('Discard Changes?'), findsNothing);

      // Now discard changes
      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Discard'));
      await tester.pumpAndSettle();

      // Assert - Should navigate back
      expect(find.byType(PreferencesScreen), findsNothing);
    });
  });
}