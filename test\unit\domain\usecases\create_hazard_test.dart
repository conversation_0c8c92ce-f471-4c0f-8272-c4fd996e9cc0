import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/entities/hazard.dart';
import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/domain/repositories/hazard_repository.dart';
import 'package:safestride/domain/usecases/create_hazard.dart';

import 'create_hazard_test.mocks.dart';

@GenerateMocks([HazardRepository])
void main() {
  group('CreateHazardUseCase Tests', () {
    late CreateHazardUseCase useCase;
    late MockHazardRepository mockRepository;
    late CreateHazardParams validParams;
    late GeoPoint testLocation;

    setUp(() {
      mockRepository = MockHazardRepository();
      useCase = CreateHazardUseCase(repository: mockRepository);
      testLocation = const GeoPoint(latitude: 37.7749, longitude: -122.4194);
      
      validParams = const CreateHazardParams(
        walkaboutId: 'walkabout_123',
        title: 'Test Hazard',
        description: 'Test hazard description',
        severity: HazardSeverity.medium,
        category: HazardCategory.slipTripFall,
        location: testLocation,
        photos: ['photo1.jpg', 'photo2.jpg'],
        notes: 'Test notes',
      );
    });

    test('should create hazard successfully with valid parameters', () async {
      // Arrange
      final expectedHazard = Hazard(
        id: 'hazard_123',
        walkaboutId: validParams.walkaboutId,
        title: validParams.title,
        description: validParams.description,
        severity: validParams.severity,
        category: validParams.category,
        location: validParams.location,
        photos: validParams.photos!,
        notes: validParams.notes,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        syncStatus: SyncStatus.local,
      );

      when(mockRepository.createHazard(any))
          .thenAnswer((_) async => expectedHazard);

      // Act
      final result = await useCase.call(validParams);

      // Assert
      expect(result.walkaboutId, equals(validParams.walkaboutId));
      expect(result.title, equals(validParams.title));
      expect(result.description, equals(validParams.description));
      expect(result.severity, equals(validParams.severity));
      expect(result.category, equals(validParams.category));
      expect(result.location, equals(validParams.location));
      expect(result.photos, equals(validParams.photos));
      expect(result.notes, equals(validParams.notes));
      expect(result.syncStatus, equals(SyncStatus.local));
      
      verify(mockRepository.createHazard(any)).called(1);
    });

    test('should create hazard with minimal parameters', () async {
      // Arrange
      final minimalParams = const CreateHazardParams(
        walkaboutId: 'walkabout_123',
        title: 'Minimal Hazard',
        severity: HazardSeverity.low,
        category: HazardCategory.other,
      );

      final expectedHazard = Hazard(
        id: 'hazard_minimal',
        walkaboutId: minimalParams.walkaboutId,
        title: minimalParams.title,
        severity: minimalParams.severity,
        category: minimalParams.category,
        photos: [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        syncStatus: SyncStatus.local,
      );

      when(mockRepository.createHazard(any))
          .thenAnswer((_) async => expectedHazard);

      // Act
      final result = await useCase.call(minimalParams);

      // Assert
      expect(result.title, equals(minimalParams.title));
      expect(result.severity, equals(minimalParams.severity));
      expect(result.category, equals(minimalParams.category));
      expect(result.description, isNull);
      expect(result.location, isNull);
      expect(result.notes, isNull);
      expect(result.photos, isEmpty);
      
      verify(mockRepository.createHazard(any)).called(1);
    });

    test('should throw ArgumentError for empty title', () async {
      // Arrange
      final invalidParams = validParams.copyWith(title: '');

      // Act & Assert
      expect(
        () => useCase.call(invalidParams),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('title cannot be empty'),
        )),
      );
      
      verifyNever(mockRepository.createHazard(any));
    });

    test('should throw ArgumentError for title exceeding 100 characters', () async {
      // Arrange
      final longTitle = 'a' * 101;
      final invalidParams = validParams.copyWith(title: longTitle);

      // Act & Assert
      expect(
        () => useCase.call(invalidParams),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('title cannot exceed 100 characters'),
        )),
      );
      
      verifyNever(mockRepository.createHazard(any));
    });

    test('should throw ArgumentError for empty walkabout ID', () async {
      // Arrange
      final invalidParams = validParams.copyWith(walkaboutId: '');

      // Act & Assert
      expect(
        () => useCase.call(invalidParams),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('Walkabout ID is required'),
        )),
      );
      
      verifyNever(mockRepository.createHazard(any));
    });

    test('should throw ArgumentError for description exceeding 1000 characters', () async {
      // Arrange
      final longDescription = 'a' * 1001;
      final invalidParams = validParams.copyWith(description: longDescription);

      // Act & Assert
      expect(
        () => useCase.call(invalidParams),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('description cannot exceed 1000 characters'),
        )),
      );
      
      verifyNever(mockRepository.createHazard(any));
    });

    test('should throw ArgumentError for invalid latitude', () async {
      // Arrange
      final invalidLocation = const GeoPoint(latitude: 91.0, longitude: 0.0);
      final invalidParams = validParams.copyWith(location: invalidLocation);

      // Act & Assert
      expect(
        () => useCase.call(invalidParams),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('Invalid latitude'),
        )),
      );
      
      verifyNever(mockRepository.createHazard(any));
    });

    test('should throw ArgumentError for invalid longitude', () async {
      // Arrange
      final invalidLocation = const GeoPoint(latitude: 0.0, longitude: 181.0);
      final invalidParams = validParams.copyWith(location: invalidLocation);

      // Act & Assert
      expect(
        () => useCase.call(invalidParams),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('Invalid longitude'),
        )),
      );
      
      verifyNever(mockRepository.createHazard(any));
    });

    test('should throw ArgumentError for empty photo path', () async {
      // Arrange
      final invalidParams = validParams.copyWith(photos: ['photo1.jpg', '']);

      // Act & Assert
      expect(
        () => useCase.call(invalidParams),
        throwsA(isA<ArgumentError>().having(
          (e) => e.message,
          'message',
          contains('Photo path cannot be empty'),
        )),
      );
      
      verifyNever(mockRepository.createHazard(any));
    });

    test('should propagate repository exceptions', () async {
      // Arrange
      when(mockRepository.createHazard(any))
          .thenThrow(Exception('Database error'));

      // Act & Assert
      expect(
        () => useCase.call(validParams),
        throwsA(isA<Exception>().having(
          (e) => e.toString(),
          'message',
          contains('Database error'),
        )),
      );
    });
  });
}

// Extension to help with copyWith for test parameters
extension CreateHazardParamsExtension on CreateHazardParams {
  CreateHazardParams copyWith({
    String? walkaboutId,
    String? title,
    String? description,
    HazardSeverity? severity,
    HazardCategory? category,
    GeoPoint? location,
    List<String>? photos,
    String? notes,
  }) {
    return CreateHazardParams(
      walkaboutId: walkaboutId ?? this.walkaboutId,
      title: title ?? this.title,
      description: description ?? this.description,
      severity: severity ?? this.severity,
      category: category ?? this.category,
      location: location ?? this.location,
      photos: photos ?? this.photos,
      notes: notes ?? this.notes,
    );
  }
}
