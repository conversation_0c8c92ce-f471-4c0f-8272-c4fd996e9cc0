import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/data/repositories/auth_repository_impl.dart';
import 'package:safestride/data/datasources/remote/auth_remote_datasource.dart';
import 'package:safestride/data/datasources/local/auth_local_datasource.dart';
import 'package:safestride/data/models/user_model.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/core/error/failures.dart';
// NetworkInfo not needed for this implementation
import 'dart:async';

import 'auth_repository_impl_test.mocks.dart';

@GenerateMocks([
  AuthRemoteDataSource,
  AuthLocalDataSource,
])
void main() {
  late AuthRepositoryImpl repository;
  late MockAuthRemoteDataSource mockRemoteDataSource;
  late MockAuthLocalDataSource mockLocalDataSource;
  late StreamController<UserModel?> authStreamController;

  setUp(() {
    mockRemoteDataSource = MockAuthRemoteDataSource();
    mockLocalDataSource = MockAuthLocalDataSource();
    authStreamController = StreamController<UserModel?>.broadcast();
    repository = AuthRepositoryImpl(
      remoteDataSource: mockRemoteDataSource,
      localDataSource: mockLocalDataSource,
    );
  });

  tearDown(() {
    authStreamController.close();
  });

  const testEmail = '<EMAIL>';
  const testPassword = 'password123';
  const testDisplayName = 'Test User';
  const testOrganization = 'Test Org';
  const testUid = 'test-uid';

  final testUserModel = UserModel(
    uid: testUid,
    email: testEmail,
    displayName: testDisplayName,
    organization: testOrganization,
    role: UserRole.inspector,
    createdAt: DateTime.now(),
    lastLoginAt: DateTime.now(),
  );

  group('AuthRepositoryImpl', () {
    group('registerWithEmail', () {
      test('should register user successfully', () async {
        // Arrange
        when(mockRemoteDataSource.registerWithEmail(
          email: testEmail,
          password: testPassword,
          displayName: testDisplayName,
          organization: testOrganization,
        )).thenAnswer((_) async => testUserModel);
        when(mockLocalDataSource.cacheUser(testUserModel))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.registerWithEmail(
          email: testEmail,
          password: testPassword,
          displayName: testDisplayName,
          organization: testOrganization,
        );

        // Assert
        expect(result, testUserModel.toDomain());
        verify(mockRemoteDataSource.registerWithEmail(
          email: testEmail,
          password: testPassword,
          displayName: testDisplayName,
          organization: testOrganization,
        )).called(1);
        verify(mockLocalDataSource.cacheUser(testUserModel)).called(1);
      });



      test('should throw exception when remote data source fails', () async {
        // Arrange
        when(mockRemoteDataSource.registerWithEmail(
          email: testEmail,
          password: testPassword,
          displayName: testDisplayName,
          organization: testOrganization,
        )).thenThrow(Exception('Registration failed'));

        // Act & Assert
        expect(
          () => repository.registerWithEmail(
            email: testEmail,
            password: testPassword,
            displayName: testDisplayName,
            organization: testOrganization,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('should still succeed if caching fails', () async {
        // Arrange
        when(mockRemoteDataSource.registerWithEmail(
          email: testEmail,
          password: testPassword,
          displayName: testDisplayName,
          organization: testOrganization,
        )).thenAnswer((_) async => testUserModel);
        when(mockLocalDataSource.cacheUser(testUserModel))
            .thenThrow(Exception('Cache write error'));

        // Act
        final result = await repository.registerWithEmail(
          email: testEmail,
          password: testPassword,
          displayName: testDisplayName,
          organization: testOrganization,
        );

        // Assert
        expect(result, testUserModel.toDomain());
        verify(mockRemoteDataSource.registerWithEmail(
          email: testEmail,
          password: testPassword,
          displayName: testDisplayName,
          organization: testOrganization,
        )).called(1);
      });
    });

    group('loginWithEmail', () {
      test('should login user successfully', () async {
        // Arrange
        when(mockRemoteDataSource.loginWithEmail(
          email: testEmail,
          password: testPassword,
        )).thenAnswer((_) async => testUserModel);
        when(mockLocalDataSource.cacheUser(testUserModel))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.loginWithEmail(
          email: testEmail,
          password: testPassword,
        );

        // Assert
        expect(result, testUserModel.toDomain());
        verify(mockRemoteDataSource.loginWithEmail(
          email: testEmail,
          password: testPassword,
        )).called(1);
        verify(mockLocalDataSource.cacheUser(testUserModel)).called(1);
      });



      test('should throw exception when credentials are invalid', () async {
        // Arrange
        when(mockRemoteDataSource.loginWithEmail(
          email: testEmail,
          password: testPassword,
        )).thenThrow(Exception('Invalid credentials'));

        // Act & Assert
        expect(
          () => repository.loginWithEmail(
            email: testEmail,
            password: testPassword,
          ),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('logout', () {
      test('should logout user successfully', () async {
        // Arrange
        when(mockRemoteDataSource.logout()).thenAnswer((_) async {});
        when(mockLocalDataSource.clearCachedUser()).thenAnswer((_) async {});

        // Act
        await repository.logout();

        // Assert
        verify(mockLocalDataSource.clearCachedUser()).called(1);
        verify(mockRemoteDataSource.logout()).called(1);
      });

      test('should throw exception when logout fails', () async {
        // Arrange
        when(mockLocalDataSource.clearCachedUser()).thenAnswer((_) async {});
        when(mockRemoteDataSource.logout())
            .thenThrow(Exception('Logout failed'));

        // Act & Assert
        expect(
          () => repository.logout(),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('resetPassword', () {
      test('should reset password successfully', () async {
        // Arrange
        when(mockRemoteDataSource.resetPassword(email: testEmail))
            .thenAnswer((_) async {});

        // Act
        await repository.resetPassword(email: testEmail);

        // Assert
        verify(mockRemoteDataSource.resetPassword(email: testEmail)).called(1);
      });

      test('should throw exception when reset password fails', () async {
        // Arrange
        when(mockRemoteDataSource.resetPassword(email: testEmail))
            .thenThrow(Exception('Reset password failed'));

        // Act & Assert
        expect(
          () => repository.resetPassword(email: testEmail),
          throwsA(isA<Exception>()),
        );
      });
    });

    group('getCurrentUser', () {
      test('should return current user from remote', () async {
        // Arrange
        when(mockRemoteDataSource.getCurrentUser())
            .thenAnswer((_) async => testUserModel);
        when(mockLocalDataSource.cacheUser(testUserModel))
            .thenAnswer((_) async {});

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, testUserModel.toDomain());
        verify(mockRemoteDataSource.getCurrentUser()).called(1);
        verify(mockLocalDataSource.cacheUser(testUserModel)).called(1);
      });

      test('should return cached user when remote fails', () async {
        // Arrange
        when(mockRemoteDataSource.getCurrentUser())
            .thenThrow(Exception('Remote error'));
        when(mockLocalDataSource.getCachedUser())
            .thenAnswer((_) async => testUserModel);

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, testUserModel.toDomain());
        verify(mockLocalDataSource.getCachedUser()).called(1);
      });

      test('should return null when no user cached and remote fails', () async {
        // Arrange
        when(mockRemoteDataSource.getCurrentUser())
            .thenThrow(Exception('Remote error'));
        when(mockLocalDataSource.getCachedUser())
            .thenAnswer((_) async => null);

        // Act
        final result = await repository.getCurrentUser();

        // Assert
        expect(result, isNull);
      });
    });

    group('authStateStream', () {
      test('should provide auth state stream from remote data source', () {
        // Arrange
        when(mockRemoteDataSource.authStateStream)
            .thenAnswer((_) => authStreamController.stream);

        // Act
        final stream = repository.authStateStream;

        // Assert
        expect(stream, authStreamController.stream);
        verify(mockRemoteDataSource.authStateStream).called(1);
      });

      test('should cache user when auth state changes', () async {
        // Arrange
        when(mockRemoteDataSource.authStateStream)
            .thenAnswer((_) => authStreamController.stream);
        when(mockLocalDataSource.cacheUser(testUserModel))
            .thenAnswer((_) async {});
        when(mockLocalDataSource.clearCachedUser()).thenAnswer((_) async {});

        final stream = repository.authStateStream;
        final subscription = stream.listen((_) {});

        // Act
        authStreamController.add(testUserModel);
        authStreamController.add(null);
        await Future.delayed(Duration.zero);

        // Assert
        verify(mockLocalDataSource.cacheUser(testUserModel)).called(1);
        verify(mockLocalDataSource.clearCachedUser()).called(1);
        
        // Cleanup
        await subscription.cancel();
      });
    });

    group('Error Handling', () {
      test('should handle remote data source exceptions', () async {
        // Arrange
        when(mockRemoteDataSource.loginWithEmail(
          email: testEmail,
          password: testPassword,
        )).thenThrow(Exception('Remote error'));

        // Act & Assert
        expect(
          () => repository.loginWithEmail(
            email: testEmail,
            password: testPassword,
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('should handle cache failures gracefully', () async {
        // Arrange
        when(mockRemoteDataSource.getCurrentUser())
            .thenThrow(Exception('Remote error'));
        when(mockLocalDataSource.getCachedUser())
            .thenThrow(Exception('Cache error'));

        // Act & Assert
        expect(
          () => repository.getCurrentUser(),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}