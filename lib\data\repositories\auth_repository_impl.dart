import '../../domain/entities/user.dart';
import '../../domain/repositories/auth_repository.dart';
import '../datasources/local/auth_local_datasource.dart';
import '../datasources/remote/auth_remote_datasource.dart';
import '../models/user_model.dart';

/// Implementation of AuthRepository
class AuthRepositoryImpl implements AuthRepository {
  final AuthRemoteDataSource _remoteDataSource;
  final AuthLocalDataSource _localDataSource;

  AuthRepositoryImpl({
    required AuthRemoteDataSource remoteDataSource,
    required AuthLocalDataSource localDataSource,
  })
      : _remoteDataSource = remoteDataSource,
        _localDataSource = localDataSource;

  @override
  Future<User> registerWithEmail({
    required String email,
    required String password,
    String? displayName,
    String? organization,
  }) async {
    try {
      // Register with remote data source
      final userModel = await _remoteDataSource.registerWithEmail(
        email: email,
        password: password,
        displayName: displayName,
        organization: organization,
      );
      
      // Cache the user locally
      await _localDataSource.cacheUser(userModel);

      return userModel.toDomain();
    } catch (e) {
      throw Exception('Registration failed: ${e.toString()}');
    }
  }

  @override
  Future<User> loginWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      // Login with remote data source
      final userModel = await _remoteDataSource.loginWithEmail(
        email: email,
        password: password,
      );

      // Cache the user locally
      await _localDataSource.cacheUser(userModel);

      return userModel.toDomain();
    } catch (e) {
      throw Exception('Login failed: ${e.toString()}');
    }
  }

  @override
  Future<User> loginWithSSO() async {
    try {
      // Login with SSO via remote data source
      final userModel = await _remoteDataSource.loginWithSSO();

      // Cache the user locally
      await _localDataSource.cacheUser(userModel);

      return userModel.toDomain();
    } catch (e) {
      throw Exception('SSO login failed: ${e.toString()}');
    }
  }

  @override
  Future<void> logout() async {
    try {
      // Clear local cache first
      await _localDataSource.clearCachedUser();

      // Then logout from remote
      await _remoteDataSource.logout();
    } catch (e) {
      throw Exception('Logout failed: ${e.toString()}');
    }
  }

  @override
  Future<void> resetPassword({required String email}) async {
    try {
      await _remoteDataSource.resetPassword(email: email);
    } catch (e) {
      throw Exception('Password reset failed: ${e.toString()}');
    }
  }

  @override
  Future<User?> getCurrentUser() async {
    try {
      // First try to get from remote (most up-to-date)
      final remoteUser = await _remoteDataSource.getCurrentUser();
      
      if (remoteUser != null) {
        // Update local cache with fresh data
        await _localDataSource.updateCachedUser(remoteUser);
        return remoteUser;
      }

      // If remote fails, try local cache
      final cachedUser = await _localDataSource.getCachedUser();
      return cachedUser;
    } catch (e) {
      // If remote fails, try local cache as fallback
      try {
        return await _localDataSource.getCachedUser();
      } catch (localError) {
        // If both fail, return null
        return null;
      }
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    try {
      // Check remote authentication first
      final isRemoteAuthenticated = await _remoteDataSource.isAuthenticated();
      
      if (isRemoteAuthenticated) {
        return true;
      }

      // If not authenticated remotely, check local cache
      return await _localDataSource.hasValidCache();
    } catch (e) {
      // If remote check fails, fallback to local cache
      try {
        return await _localDataSource.hasValidCache();
      } catch (localError) {
        return false;
      }
    }
  }

  @override
  Stream<User?> get authStateChanges {
    return _remoteDataSource.authStateChanges.map((userModel) {
      if (userModel != null) {
        // Cache user when auth state changes
        _localDataSource.cacheUser(userModel).catchError((error) {
          // Log error but don't break the stream
          print('Failed to cache user on auth state change: $error');
        });
      } else {
        // Clear cache when user logs out
        _localDataSource.clearCachedUser().catchError((error) {
          // Log error but don't break the stream
          print('Failed to clear cache on auth state change: $error');
        });
      }
      return userModel?.toDomain();
    });
  }

  @override
  Future<User?> getCachedUser() async {
    try {
      final userModel = await _localDataSource.getCachedUser();
      return userModel?.toDomain();
    } catch (e) {
      return null;
    }
  }

  @override
  Future<void> clearCachedCredentials() async {
    try {
      await _localDataSource.clearCachedUser();
    } catch (e) {
      throw Exception('Failed to clear cached credentials: ${e.toString()}');
    }
  }
}