{"buildFiles": ["D:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\safestride.bmad\\safestride\\android\\app\\.cxx\\Debug\\3q6i2w34\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\safestride.bmad\\safestride\\android\\app\\.cxx\\Debug\\3q6i2w34\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}