import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';
import 'package:safestride/presentation/providers/auth_provider.dart';
import 'package:safestride/presentation/screens/auth/login_screen.dart';

import 'login_screen_test.mocks.dart';

@GenerateMocks([AuthProvider])
void main() {
  late MockAuthProvider mockAuthProvider;

  setUp(() {
    mockAuthProvider = MockAuthProvider();
    when(mockAuthProvider.state).thenReturn(AuthState.initial);
    when(mockAuthProvider.currentUser).thenReturn(null);
    when(mockAuthProvider.errorMessage).thenReturn(null);
    when(mockAuthProvider.isLoading).thenReturn(false);
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: ChangeNotifierProvider<AuthProvider>.value(
        value: mockAuthProvider,
        child: const LoginScreen(),
      ),
    );
  }

  group('LoginScreen Widget Tests', () {
    testWidgets('should display login form elements', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('SafeStride'), findsOneWidget);
      expect(find.text('Sign in to your account'), findsOneWidget);
      expect(find.byType(TextFormField), findsNWidgets(2)); // Email and password fields
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Password'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);
      expect(find.text('Continue with Google'), findsOneWidget);
      expect(find.text('Forgot Password?'), findsOneWidget);
      expect(find.text("Don't have an account? "), findsOneWidget);
      expect(find.text('Sign Up'), findsOneWidget);
    });

    testWidgets('should validate email field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Try to submit with empty email
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter your email'), findsOneWidget);
    });

    testWidgets('should validate email format', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter invalid email format
      await tester.enterText(find.byType(TextFormField).first, 'invalid-email');
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('should validate password field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter valid email but empty password
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter your password'), findsOneWidget);
    });

    testWidgets('should call loginWithEmail when form is valid', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter valid credentials and submit
      await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
      await tester.enterText(find.byType(TextFormField).last, 'password123');
      await tester.tap(find.text('Sign In'));
      await tester.pump();

      // Assert
      verify(mockAuthProvider.loginWithEmail(
        email: '<EMAIL>',
        password: 'password123',
      )).called(1);
    });

    testWidgets('should call loginWithSSO when SSO button is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Scroll to make the SSO button visible
      await tester.scrollUntilVisible(
        find.text('Continue with Google'),
        500.0,
        scrollable: find.byType(Scrollable).first,
      );
      
      // Act
      await tester.tap(find.text('Continue with Google'));
      await tester.pump();

      // Assert
      verify(mockAuthProvider.loginWithSSO()).called(1);
    });

    testWidgets('should show loading indicator when state is loading', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.isLoading).thenReturn(true);
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show error message when state is error', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.error);
      when(mockAuthProvider.errorMessage).thenReturn('Login failed');
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Login failed'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should navigate to register screen when sign up is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const LoginScreen(),
        ),
        routes: {
          '/register': (context) => const Scaffold(body: Text('Register Screen')),
        },
      ));
      
      // Act
      await tester.scrollUntilVisible(
        find.text('Sign Up'),
        500.0,
        scrollable: find.byType(Scrollable).first,
      );
      await tester.tap(find.text('Sign Up'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Register Screen'), findsOneWidget);
    });

    testWidgets('should navigate to forgot password screen when forgot password is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const LoginScreen(),
        ),
        routes: {
          '/forgot-password': (context) => const Scaffold(body: Text('Forgot Password Screen')),
        },
      ));
      
      // Act
      await tester.tap(find.text('Forgot Password?'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Forgot Password Screen'), findsOneWidget);
    });

    testWidgets('should toggle password visibility', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Assert initial state (password hidden)
      expect(find.byIcon(Icons.visibility), findsOneWidget);
      
      // Act - Tap visibility toggle
      await tester.tap(find.byIcon(Icons.visibility));
      await tester.pump();

      // Assert password is now visible
      expect(find.byIcon(Icons.visibility_off), findsOneWidget);
    });

    testWidgets('should disable sign in button when loading', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.isLoading).thenReturn(true);
      await tester.pumpWidget(createTestWidget());
      
      // Act & Assert
      final signInButton = find.text('Sign In');
      expect(signInButton, findsOneWidget);
      
      // Button should be disabled (not tappable) when loading
      await tester.tap(signInButton);
      await tester.pump();
      
      // Verify loginWithEmail is not called when button is disabled
      verifyNever(mockAuthProvider.loginWithEmail(
        email: anyNamed('email'),
        password: anyNamed('password'),
      ));
    });

    testWidgets('should clear error when user starts typing', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.error);
      when(mockAuthProvider.errorMessage).thenReturn('Login failed');
      await tester.pumpWidget(createTestWidget());
      
      // Act - Start typing in email field
      await tester.enterText(find.byType(TextFormField).first, 'a');
      await tester.pump();

      // Assert
      verify(mockAuthProvider.clearError()).called(1);
    });

    group('offline authentication scenarios', () {
      testWidgets('should show cached login success message when offline', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.authenticated);
        when(mockAuthProvider.errorMessage).thenReturn('Logged in with cached credentials');
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Logged in with cached credentials'), findsOneWidget);
      });

      testWidgets('should show network error when login fails offline', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('No network connection available');
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('No network connection available'), findsOneWidget);
        expect(find.byIcon(Icons.error_outline), findsOneWidget);
      });

      testWidgets('should show appropriate error for offline SSO attempt', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('SSO requires network connection');
        await tester.pumpWidget(createTestWidget());
        
        // Scroll to make the SSO button visible
        await tester.scrollUntilVisible(
          find.text('Continue with Google'),
          500.0,
          scrollable: find.byType(Scrollable).first,
        );
        
        // Act
        await tester.tap(find.text('Continue with Google'));
        await tester.pump();

        // Assert
        expect(find.text('SSO requires network connection'), findsOneWidget);
        verify(mockAuthProvider.loginWithSSO()).called(1);
      });

      testWidgets('should handle cached credentials expiration', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('Cached credentials expired - please login again');
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Cached credentials expired - please login again'), findsOneWidget);
      });

      testWidgets('should show retry option when network timeout occurs', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('Request timeout - check your connection');
        await tester.pumpWidget(createTestWidget());
        
        // Assert error is shown
        expect(find.text('Request timeout - check your connection'), findsOneWidget);
        
        // Act - Clear error and retry
        await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
        await tester.pump();
        
        // Assert error is cleared
        verify(mockAuthProvider.clearError()).called(1);
      });

      testWidgets('should disable form submission when offline without cached credentials', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.error);
        when(mockAuthProvider.errorMessage).thenReturn('Device is offline and no cached credentials available');
        await tester.pumpWidget(createTestWidget());
        
        // Act - Try to submit form
        await tester.enterText(find.byType(TextFormField).first, '<EMAIL>');
        await tester.enterText(find.byType(TextFormField).last, 'password123');
        await tester.tap(find.text('Sign In'));
        await tester.pump();

        // Assert offline error is shown
        expect(find.text('Device is offline and no cached credentials available'), findsOneWidget);
      });

      testWidgets('should show connection restored message when back online', (WidgetTester tester) async {
        // Arrange
        when(mockAuthProvider.state).thenReturn(AuthState.authenticated);
        when(mockAuthProvider.errorMessage).thenReturn('Connection restored - login successful');
        await tester.pumpWidget(createTestWidget());

        // Assert
        expect(find.text('Connection restored - login successful'), findsOneWidget);
      });
    });
  });
}