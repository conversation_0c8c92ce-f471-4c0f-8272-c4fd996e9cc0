import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../presentation/providers/auth_provider.dart';
import 'app_routes.dart';

/// A widget that protects routes requiring authentication
/// 
/// This widget checks if the user is authenticated and redirects to the login
/// screen if they are not. If the user is authenticated, it displays the child widget.
class AuthGuard extends StatelessWidget {
  final Widget child;
  
  const AuthGuard({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    // If still initializing auth state, show loading
    if (authProvider.state == AuthState.initial || authProvider.isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    // If not authenticated, redirect to login
    if (!authProvider.isAuthenticated) {
      // Use a post-frame callback to avoid build-time navigation
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AppRoutes.navigateToLogin(context);
      });
      
      // Return an empty container while redirecting
      return Container();
    }
    
    // User is authenticated, show the protected route
    return child;
  }
}

/// A widget that prevents authenticated users from accessing auth screens
/// 
/// This widget checks if the user is authenticated and redirects to the home
/// screen if they are. If the user is not authenticated, it displays the child widget.
class UnauthenticatedGuard extends StatelessWidget {
  final Widget child;
  
  const UnauthenticatedGuard({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    
    // If still initializing auth state, show loading
    if (authProvider.state == AuthState.initial || authProvider.isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }
    
    // If authenticated, redirect to home
    if (authProvider.isAuthenticated) {
      // Use a post-frame callback to avoid build-time navigation
      WidgetsBinding.instance.addPostFrameCallback((_) {
        AppRoutes.navigateToDashboard(context);
      });
      
      // Return an empty container while redirecting
      return Container();
    }
    
    // User is not authenticated, show the auth screen
    return child;
  }
}