/// Base class for all failures in the application
abstract class Failure {
  const Failure();
}

/// Failure for server-related errors
class ServerFailure extends Failure {
  final String message;
  
  const ServerFailure(this.message);
  
  @override
  String toString() => 'ServerFailure: $message';
}

/// Failure for network-related errors
class NetworkFailure extends Failure {
  final String message;
  
  const NetworkFailure(this.message);
  
  @override
  String toString() => 'NetworkFailure: $message';
}

/// Failure for authentication-related errors
class AuthFailure extends Failure {
  final String message;
  
  const AuthFailure(this.message);
  
  @override
  String toString() => 'AuthFailure: $message';
}

/// Failure for validation-related errors
class ValidationFailure extends Failure {
  final String message;
  
  const ValidationFailure(this.message);
  
  @override
  String toString() => 'ValidationFailure: $message';
}

/// Failure for cache-related errors
class CacheFailure extends Failure {
  final String message;
  
  const CacheFailure(this.message);
  
  @override
  String toString() => 'CacheFailure: $message';
}