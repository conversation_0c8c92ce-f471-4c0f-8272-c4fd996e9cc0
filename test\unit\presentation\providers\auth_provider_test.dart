import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/usecases/get_current_user.dart';
import 'package:safestride/domain/usecases/login_with_email.dart';
import 'package:safestride/domain/usecases/login_with_sso.dart';
import 'package:safestride/domain/usecases/logout.dart';
import 'package:safestride/domain/usecases/register_with_email.dart';
import 'package:safestride/domain/usecases/reset_password.dart';
import 'package:safestride/presentation/providers/auth_provider.dart';

import 'auth_provider_test.mocks.dart';

@GenerateMocks([
  RegisterWithEmailUseCase,
  LoginWithEmailUseCase,
  LoginWithSSOUseCase,
  LogoutUseCase,
  ResetPasswordUseCase,
  GetCurrentUserUseCase,
])
void main() {
  late AuthProvider authProvider;
  late MockRegisterWithEmailUseCase mockRegisterUseCase;
  late MockLoginWithEmailUseCase mockLoginEmailUseCase;
  late MockLoginWithSSOUseCase mockLoginSSOUseCase;
  late MockLogoutUseCase mockLogoutUseCase;
  late MockResetPasswordUseCase mockResetPasswordUseCase;
  late MockGetCurrentUserUseCase mockGetCurrentUserUseCase;
  late User testUser;

  setUp(() {
    mockRegisterUseCase = MockRegisterWithEmailUseCase();
    mockLoginEmailUseCase = MockLoginWithEmailUseCase();
    mockLoginSSOUseCase = MockLoginWithSSOUseCase();
    mockLogoutUseCase = MockLogoutUseCase();
    mockGetCurrentUserUseCase = MockGetCurrentUserUseCase();
    mockResetPasswordUseCase = MockResetPasswordUseCase();

    // Set up default mock behavior for getCurrentUser to return null (unauthenticated)
    when(mockGetCurrentUserUseCase.call()).thenAnswer((_) async => null);

    authProvider = AuthProvider(
      registerWithEmailUseCase: mockRegisterUseCase,
      loginWithEmailUseCase: mockLoginEmailUseCase,
      loginWithSSOUseCase: mockLoginSSOUseCase,
      logoutUseCase: mockLogoutUseCase,
      getCurrentUserUseCase: mockGetCurrentUserUseCase,
      resetPasswordUseCase: mockResetPasswordUseCase,
    );

    testUser = User(
      uid: 'test-uid',
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      preferences: UserPreferences(
        theme: 'light',
        language: 'en',
        notificationsEnabled: true,
        offlineMode: false,
      ),
    );
  });

  group('AuthProvider', () {
    test('initial state should be AuthState.unauthenticated after initialization', () async {
      // Wait for initialization to complete
      await Future.delayed(Duration.zero);
      expect(authProvider.state, AuthState.unauthenticated);
      expect(authProvider.currentUser, isNull);
      expect(authProvider.errorMessage, isNull);
    });

    group('register', () {
      test('should register user successfully', () async {
        // Arrange
        when(mockRegisterUseCase.call(
          email: anyNamed('email'),
          password: anyNamed('password'),
          displayName: anyNamed('displayName'),
        )).thenAnswer((_) async => testUser);

        // Act
        await authProvider.registerWithEmail(
          email: '<EMAIL>',
          password: 'password123',
          displayName: 'Test User',
        );

        // Assert
        expect(authProvider.state, AuthState.authenticated);
        expect(authProvider.currentUser, testUser);
        expect(authProvider.errorMessage, isNull);
      });

      test('should handle registration error', () async {
        // Arrange
        const errorMessage = 'Registration failed';
        when(mockRegisterUseCase.call(
          email: anyNamed('email'),
          password: anyNamed('password'),
          displayName: anyNamed('displayName'),
        )).thenThrow(Exception(errorMessage));

        // Act
        await authProvider.registerWithEmail(
          email: '<EMAIL>',
          password: 'password123',
          displayName: 'Test User',
        );

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, contains(errorMessage));
      });

      test('should set loading state during registration', () async {
        // Arrange
        when(mockRegisterUseCase.call(
          email: anyNamed('email'),
          password: anyNamed('password'),
          displayName: anyNamed('displayName'),
        )).thenAnswer((_) async {
          await Future.delayed(const Duration(milliseconds: 100));
          return testUser;
        });

        // Act
        final future = authProvider.registerWithEmail(
          email: '<EMAIL>',
          password: 'password123',
          displayName: 'Test User',
        );

        // Assert loading state
        expect(authProvider.state, AuthState.loading);
        
        await future;
        expect(authProvider.state, AuthState.authenticated);
      });
    });

    group('loginWithEmail', () {
      test('should login with email successfully', () async {
        // Arrange
        when(mockLoginEmailUseCase.call(
          email: anyNamed('email'),
          password: anyNamed('password'),
        )).thenAnswer((_) async => testUser);

        // Act
        await authProvider.loginWithEmail(
          email: '<EMAIL>',
          password: 'password123',
        );

        // Assert
        expect(authProvider.state, AuthState.authenticated);
        expect(authProvider.currentUser, testUser);
        expect(authProvider.errorMessage, isNull);
      });

      test('should handle login error', () async {
        // Arrange
        const errorMessage = 'Invalid credentials';
        when(mockLoginEmailUseCase.call(
          email: anyNamed('email'),
          password: anyNamed('password'),
        )).thenThrow(Exception(errorMessage));

        // Act
        await authProvider.loginWithEmail(
          email: '<EMAIL>',
          password: 'wrongpassword',
        );

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, contains(errorMessage));
      });
    });

    group('loginWithSSO', () {
      test('should login with SSO successfully', () async {
        // Arrange
        when(mockLoginSSOUseCase.call()).thenAnswer((_) async => testUser);

        // Act
        await authProvider.loginWithSSO();

        // Assert
        expect(authProvider.state, AuthState.authenticated);
        expect(authProvider.currentUser, testUser);
        expect(authProvider.errorMessage, isNull);
      });

      test('should handle SSO login error', () async {
        // Arrange
        const errorMessage = 'SSO login cancelled';
        when(mockLoginSSOUseCase.call()).thenThrow(Exception(errorMessage));

        // Act
        await authProvider.loginWithSSO();

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, contains(errorMessage));
      });
    });

    group('logout', () {
      test('should logout successfully', () async {
        // Arrange
        // Set up authenticated state manually
        when(mockGetCurrentUserUseCase.call()).thenAnswer((_) async => testUser);
        await authProvider.refreshUser();
        when(mockLogoutUseCase.call()).thenAnswer((_) async => {});

        // Act
        await authProvider.logout();

        // Assert
        expect(authProvider.state, AuthState.unauthenticated);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, isNull);
      });

      test('should handle logout error', () async {
        // Arrange
        // Set up authenticated state manually
        when(mockGetCurrentUserUseCase.call()).thenAnswer((_) async => testUser);
        await authProvider.refreshUser();
        const errorMessage = 'Logout failed';
        when(mockLogoutUseCase.call()).thenThrow(Exception(errorMessage));

        // Act
        await authProvider.logout();

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.errorMessage, contains(errorMessage));
      });
    });

    group('resetPassword', () {
      test('should reset password successfully', () async {
        // Arrange
        when(mockResetPasswordUseCase.call(email: anyNamed('email')))
            .thenAnswer((_) async => {});

        // Act
        await authProvider.resetPassword(email: '<EMAIL>');

        // Assert
        expect(authProvider.errorMessage, isNull);
        verify(mockResetPasswordUseCase.call(email: '<EMAIL>')).called(1);
      });

      test('should handle reset password error', () async {
        // Arrange
        const errorMessage = 'User not found';
        when(mockResetPasswordUseCase.call(email: anyNamed('email')))
            .thenThrow(Exception(errorMessage));

        // Act
        await authProvider.resetPassword(email: '<EMAIL>');

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.errorMessage, contains(errorMessage));
      });
    });

    group('getCurrentUser', () {
      test('should get current user successfully', () async {
        // Arrange
        when(mockGetCurrentUserUseCase.call()).thenAnswer((_) async => testUser);

        // Act
        await authProvider.refreshUser();

        // Assert
        expect(authProvider.state, AuthState.authenticated);
        expect(authProvider.currentUser, testUser);
        expect(authProvider.errorMessage, isNull);
      });

      test('should handle no current user', () async {
        // Arrange
        when(mockGetCurrentUserUseCase.call()).thenAnswer((_) async => null);

        // Act
        await authProvider.refreshUser();

        // Assert
        expect(authProvider.state, AuthState.unauthenticated);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, isNull);
      });

      test('should handle get current user error', () async {
        // Arrange
        const errorMessage = 'Failed to get user';
        when(mockGetCurrentUserUseCase.call()).thenThrow(Exception(errorMessage));

        // Act
        await authProvider.refreshUser();

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, contains(errorMessage));
      });
    });

    group('offline authentication', () {
      test('should handle offline login with cached credentials', () async {
        // Arrange
        when(mockLoginEmailUseCase.call(
          email: anyNamed('email'),
          password: anyNamed('password'),
        )).thenAnswer((_) async => testUser);

        // Act
        await authProvider.loginWithEmail(
          email: '<EMAIL>',
          password: 'password123',
        );

        // Assert
        expect(authProvider.state, AuthState.authenticated);
        expect(authProvider.currentUser, testUser);
        expect(authProvider.errorMessage, isNull);
      });

      test('should handle offline login failure when no cached credentials', () async {
        // Arrange
        const errorMessage = 'No network connection and no cached credentials';
        when(mockLoginEmailUseCase.call(
          email: anyNamed('email'),
          password: anyNamed('password'),
        )).thenThrow(Exception(errorMessage));

        // Act
        await authProvider.loginWithEmail(
          email: '<EMAIL>',
          password: 'password123',
        );

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, contains(errorMessage));
      });

      test('should handle offline registration failure', () async {
        // Arrange
        const errorMessage = 'Registration requires network connection';
        when(mockRegisterUseCase.call(
          email: anyNamed('email'),
          password: anyNamed('password'),
          displayName: anyNamed('displayName'),
        )).thenThrow(Exception(errorMessage));

        // Act
        await authProvider.registerWithEmail(
          email: '<EMAIL>',
          password: 'password123',
          displayName: 'Test User',
        );

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, contains(errorMessage));
      });

      test('should handle offline SSO login failure', () async {
        // Arrange
        const errorMessage = 'SSO requires network connection';
        when(mockLoginSSOUseCase.call()).thenThrow(Exception(errorMessage));

        // Act
        await authProvider.loginWithSSO();

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, contains(errorMessage));
      });

      test('should handle offline password reset failure', () async {
        // Arrange
        const errorMessage = 'Password reset requires network connection';
        when(mockResetPasswordUseCase.call(email: anyNamed('email')))
            .thenThrow(Exception(errorMessage));

        // Act
        await authProvider.resetPassword(email: '<EMAIL>');

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.errorMessage, contains(errorMessage));
      });

      test('should maintain authentication state when going offline', () async {
        // Arrange - First authenticate the user
        when(mockGetCurrentUserUseCase.call()).thenAnswer((_) async => testUser);
        await authProvider.refreshUser();
        expect(authProvider.state, AuthState.authenticated);

        // Act - Simulate going offline (getCurrentUser still returns cached user)
        when(mockGetCurrentUserUseCase.call()).thenAnswer((_) async => testUser);
        await authProvider.refreshUser();

        // Assert - Should maintain authenticated state
        expect(authProvider.state, AuthState.authenticated);
        expect(authProvider.currentUser, testUser);
        expect(authProvider.errorMessage, isNull);
      });

      test('should handle cached credential expiration', () async {
        // Arrange
        const errorMessage = 'Cached credentials have expired';
        when(mockGetCurrentUserUseCase.call()).thenThrow(Exception(errorMessage));

        // Act
        await authProvider.refreshUser();

        // Assert
        expect(authProvider.state, AuthState.error);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, contains(errorMessage));
      });
    });

    group('state management', () {
      test('clearError should clear error message and set state to initial', () async {
        // Arrange
        // Manually set error state by calling a method that will fail
        when(mockGetCurrentUserUseCase.call()).thenThrow(Exception('Test error'));
        await authProvider.refreshUser();
        expect(authProvider.state, AuthState.error);
        expect(authProvider.errorMessage, contains('Test error'));

        // Act
        authProvider.clearError();

        // Assert
        expect(authProvider.state, AuthState.unauthenticated);
        expect(authProvider.errorMessage, isNull);
      });

      test('should set authenticated state when user is available', () async {
        // Arrange
        when(mockGetCurrentUserUseCase.call()).thenAnswer((_) async => testUser);

        // Act
        await authProvider.refreshUser();

        // Assert
        expect(authProvider.state, AuthState.authenticated);
        expect(authProvider.currentUser, testUser);
        expect(authProvider.errorMessage, isNull);
      });

      test('should set unauthenticated state when no user is available', () async {
        // Arrange
        when(mockGetCurrentUserUseCase.call()).thenAnswer((_) async => null);

        // Act
        await authProvider.refreshUser();

        // Assert
        expect(authProvider.state, AuthState.unauthenticated);
        expect(authProvider.currentUser, isNull);
        expect(authProvider.errorMessage, isNull);
      });
    });
  });
}