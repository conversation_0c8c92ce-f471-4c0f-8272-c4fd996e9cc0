import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/entities/hazard.dart';
import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/data/repositories/hazard_repository_impl.dart';
import 'package:safestride/data/datasources/local/hazard_local_datasource.dart';

import 'hazard_repository_impl_test.mocks.dart';

@GenerateMocks([HazardLocalDataSource])
void main() {
  group('HazardRepositoryImpl Tests', () {
    late HazardRepositoryImpl repository;
    late MockHazardLocalDataSource mockLocalDataSource;
    late Hazard testHazard;
    late GeoPoint testLocation;

    setUp(() {
      mockLocalDataSource = MockHazardLocalDataSource();
      repository = HazardRepositoryImpl(localDataSource: mockLocalDataSource);
      testLocation = const GeoPoint(latitude: 37.7749, longitude: -122.4194);

      testHazard = Hazard(
        id: 'hazard_123',
        walkaboutId: 'walkabout_456',
        title: 'Test Hazard',
        description: 'Test hazard description',
        severity: HazardSeverity.medium,
        category: HazardCategory.slipTripFall,
        location: testLocation,
        photos: ['photo1.jpg', 'photo2.jpg'],
        notes: 'Test notes',
        createdAt: DateTime(2024, 1, 1, 12, 0, 0),
        updatedAt: DateTime(2024, 1, 1, 12, 0, 0),
        syncStatus: SyncStatus.local,
      );
    });

    group('createHazard', () {
      test('should create hazard successfully', () async {
        // Arrange
        when(
          mockLocalDataSource.createHazard(any),
        ).thenAnswer((_) async => testHazard);

        // Act
        final result = await repository.createHazard(testHazard);

        // Assert
        expect(result, equals(testHazard));
        verify(mockLocalDataSource.createHazard(testHazard)).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.createHazard(any),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.createHazard(testHazard),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to create hazard'),
            ),
          ),
        );
      });
    });

    group('getHazardById', () {
      test('should return hazard when found', () async {
        // Arrange
        when(
          mockLocalDataSource.getHazardById('hazard_123'),
        ).thenAnswer((_) async => testHazard);

        // Act
        final result = await repository.getHazardById('hazard_123');

        // Assert
        expect(result, equals(testHazard));
        verify(mockLocalDataSource.getHazardById('hazard_123')).called(1);
      });

      test('should return null when not found', () async {
        // Arrange
        when(
          mockLocalDataSource.getHazardById('non_existent'),
        ).thenAnswer((_) async => null);

        // Act
        final result = await repository.getHazardById('non_existent');

        // Assert
        expect(result, isNull);
        verify(mockLocalDataSource.getHazardById('non_existent')).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.getHazardById('hazard_123'),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getHazardById('hazard_123'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to get hazard by ID'),
            ),
          ),
        );
      });
    });

    group('getHazardsByWalkaboutId', () {
      test('should return list of hazards', () async {
        // Arrange
        final hazards = [testHazard];
        when(
          mockLocalDataSource.getHazardsByWalkaboutId('walkabout_456'),
        ).thenAnswer((_) async => hazards);

        // Act
        final result = await repository.getHazardsByWalkaboutId(
          'walkabout_456',
        );

        // Assert
        expect(result, equals(hazards));
        verify(
          mockLocalDataSource.getHazardsByWalkaboutId('walkabout_456'),
        ).called(1);
      });

      test('should return empty list when no hazards found', () async {
        // Arrange
        when(
          mockLocalDataSource.getHazardsByWalkaboutId('empty_walkabout'),
        ).thenAnswer((_) async => []);

        // Act
        final result = await repository.getHazardsByWalkaboutId(
          'empty_walkabout',
        );

        // Assert
        expect(result, isEmpty);
        verify(
          mockLocalDataSource.getHazardsByWalkaboutId('empty_walkabout'),
        ).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.getHazardsByWalkaboutId('walkabout_456'),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getHazardsByWalkaboutId('walkabout_456'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to get hazards by walkabout ID'),
            ),
          ),
        );
      });
    });

    group('getHazardsBySeverity', () {
      test('should return hazards filtered by severity', () async {
        // Arrange
        final hazards = [testHazard];
        when(
          mockLocalDataSource.getHazardsBySeverity(
            'walkabout_456',
            HazardSeverity.medium,
          ),
        ).thenAnswer((_) async => hazards);

        // Act
        final result = await repository.getHazardsBySeverity(
          'walkabout_456',
          HazardSeverity.medium,
        );

        // Assert
        expect(result, equals(hazards));
        verify(
          mockLocalDataSource.getHazardsBySeverity(
            'walkabout_456',
            HazardSeverity.medium,
          ),
        ).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.getHazardsBySeverity(
            'walkabout_456',
            HazardSeverity.high,
          ),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getHazardsBySeverity(
            'walkabout_456',
            HazardSeverity.high,
          ),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to get hazards by severity'),
            ),
          ),
        );
      });
    });

    group('getHazardsByCategory', () {
      test('should return hazards filtered by category', () async {
        // Arrange
        final hazards = [testHazard];
        when(
          mockLocalDataSource.getHazardsByCategory(
            'walkabout_456',
            HazardCategory.slipTripFall,
          ),
        ).thenAnswer((_) async => hazards);

        // Act
        final result = await repository.getHazardsByCategory(
          'walkabout_456',
          HazardCategory.slipTripFall,
        );

        // Assert
        expect(result, equals(hazards));
        verify(
          mockLocalDataSource.getHazardsByCategory(
            'walkabout_456',
            HazardCategory.slipTripFall,
          ),
        ).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.getHazardsByCategory(
            'walkabout_456',
            HazardCategory.electrical,
          ),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getHazardsByCategory(
            'walkabout_456',
            HazardCategory.electrical,
          ),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to get hazards by category'),
            ),
          ),
        );
      });
    });

    group('updateHazard', () {
      test('should update hazard successfully', () async {
        // Arrange
        final updatedHazard = testHazard.copyWith(title: 'Updated Title');
        when(
          mockLocalDataSource.updateHazard(any),
        ).thenAnswer((_) async => updatedHazard);

        // Act
        final result = await repository.updateHazard(updatedHazard);

        // Assert
        expect(result, equals(updatedHazard));
        verify(mockLocalDataSource.updateHazard(updatedHazard)).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.updateHazard(any),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.updateHazard(testHazard),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to update hazard'),
            ),
          ),
        );
      });
    });

    group('deleteHazard', () {
      test('should delete hazard successfully', () async {
        // Arrange
        when(
          mockLocalDataSource.deleteHazard('hazard_123'),
        ).thenAnswer((_) async => true);

        // Act
        final result = await repository.deleteHazard('hazard_123');

        // Assert
        expect(result, isTrue);
        verify(mockLocalDataSource.deleteHazard('hazard_123')).called(1);
      });

      test('should return false when hazard not found', () async {
        // Arrange
        when(
          mockLocalDataSource.deleteHazard('non_existent'),
        ).thenAnswer((_) async => false);

        // Act
        final result = await repository.deleteHazard('non_existent');

        // Assert
        expect(result, isFalse);
        verify(mockLocalDataSource.deleteHazard('non_existent')).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.deleteHazard('hazard_123'),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.deleteHazard('hazard_123'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to delete hazard'),
            ),
          ),
        );
      });
    });

    group('getHazardsToSync', () {
      test('should return hazards that need syncing', () async {
        // Arrange
        final hazardsToSync = [testHazard];
        when(
          mockLocalDataSource.getHazardsToSync(),
        ).thenAnswer((_) async => hazardsToSync);

        // Act
        final result = await repository.getHazardsToSync();

        // Assert
        expect(result, equals(hazardsToSync));
        verify(mockLocalDataSource.getHazardsToSync()).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.getHazardsToSync(),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getHazardsToSync(),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to get hazards to sync'),
            ),
          ),
        );
      });
    });

    group('updateSyncStatus', () {
      test('should update sync status successfully', () async {
        // Arrange
        final updatedHazard = testHazard.copyWith(
          syncStatus: SyncStatus.synced,
        );
        when(
          mockLocalDataSource.updateSyncStatus('hazard_123', SyncStatus.synced),
        ).thenAnswer((_) async => updatedHazard);

        // Act
        final result = await repository.updateSyncStatus(
          'hazard_123',
          SyncStatus.synced,
        );

        // Assert
        expect(result, equals(updatedHazard));
        expect(result.syncStatus, equals(SyncStatus.synced));
        verify(
          mockLocalDataSource.updateSyncStatus('hazard_123', SyncStatus.synced),
        ).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.updateSyncStatus('hazard_123', SyncStatus.synced),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.updateSyncStatus('hazard_123', SyncStatus.synced),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to update sync status'),
            ),
          ),
        );
      });
    });

    group('searchHazards', () {
      test('should return search results', () async {
        // Arrange
        final searchResults = [testHazard];
        when(
          mockLocalDataSource.searchHazards('walkabout_456', 'test'),
        ).thenAnswer((_) async => searchResults);

        // Act
        final result = await repository.searchHazards('walkabout_456', 'test');

        // Assert
        expect(result, equals(searchResults));
        verify(
          mockLocalDataSource.searchHazards('walkabout_456', 'test'),
        ).called(1);
      });

      test('should return empty list when no matches found', () async {
        // Arrange
        when(
          mockLocalDataSource.searchHazards('walkabout_456', 'nonexistent'),
        ).thenAnswer((_) async => []);

        // Act
        final result = await repository.searchHazards(
          'walkabout_456',
          'nonexistent',
        );

        // Assert
        expect(result, isEmpty);
        verify(
          mockLocalDataSource.searchHazards('walkabout_456', 'nonexistent'),
        ).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.searchHazards('walkabout_456', 'test'),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.searchHazards('walkabout_456', 'test'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to search hazards'),
            ),
          ),
        );
      });
    });

    group('getHazardsInArea', () {
      test('should return hazards in specified area', () async {
        // Arrange
        final center = const GeoPoint(latitude: 37.7749, longitude: -122.4194);
        const radiusMeters = 1000.0;
        final hazardsInArea = [testHazard];
        when(
          mockLocalDataSource.getHazardsInArea(
            'walkabout_456',
            center,
            radiusMeters,
          ),
        ).thenAnswer((_) async => hazardsInArea);

        // Act
        final result = await repository.getHazardsInArea(
          'walkabout_456',
          center,
          radiusMeters,
        );

        // Assert
        expect(result, equals(hazardsInArea));
        verify(
          mockLocalDataSource.getHazardsInArea(
            'walkabout_456',
            center,
            radiusMeters,
          ),
        ).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        final center = const GeoPoint(latitude: 37.7749, longitude: -122.4194);
        const radiusMeters = 1000.0;
        when(
          mockLocalDataSource.getHazardsInArea(
            'walkabout_456',
            center,
            radiusMeters,
          ),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getHazardsInArea(
            'walkabout_456',
            center,
            radiusMeters,
          ),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to get hazards in area'),
            ),
          ),
        );
      });
    });

    group('getHazardStatistics', () {
      test('should return hazard statistics', () async {
        // Arrange
        final statistics = {
          'total': 5,
          'by_severity': {'low': 2, 'medium': 2, 'high': 1, 'critical': 0},
          'by_category': {'slipTripFall': 3, 'electrical': 1, 'chemical': 1},
        };
        when(
          mockLocalDataSource.getHazardStatistics('walkabout_456'),
        ).thenAnswer((_) async => statistics);

        // Act
        final result = await repository.getHazardStatistics('walkabout_456');

        // Assert
        expect(result, equals(statistics));
        expect(result['total'], equals(5));
        expect(result['by_severity']['high'], equals(1));
        verify(
          mockLocalDataSource.getHazardStatistics('walkabout_456'),
        ).called(1);
      });

      test('should throw exception when local data source fails', () async {
        // Arrange
        when(
          mockLocalDataSource.getHazardStatistics('walkabout_456'),
        ).thenThrow(Exception('Database error'));

        // Act & Assert
        expect(
          () => repository.getHazardStatistics('walkabout_456'),
          throwsA(
            isA<Exception>().having(
              (e) => e.toString(),
              'message',
              contains('Failed to get hazard statistics'),
            ),
          ),
        );
      });
    });
  });
}
