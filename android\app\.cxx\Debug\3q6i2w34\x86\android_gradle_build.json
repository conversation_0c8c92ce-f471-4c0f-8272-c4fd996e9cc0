{"buildFiles": ["D:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\safestride.bmad\\safestride\\android\\app\\.cxx\\Debug\\3q6i2w34\\x86", "clean"]], "buildTargetsCommandComponents": ["D:\\src\\AndroidSDK\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\GitHub\\safestride.bmad\\safestride\\android\\app\\.cxx\\Debug\\3q6i2w34\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}, "toolchains": {"toolchain": {"cCompilerExecutable": "D:\\src\\AndroidSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang.exe", "cppCompilerExecutable": "D:\\src\\AndroidSDK\\ndk\\27.0.12077973\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe"}}, "cFileExtensions": [], "cppFileExtensions": []}