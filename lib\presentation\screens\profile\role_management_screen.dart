import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../domain/entities/user.dart';
import '../../providers/auth_provider.dart';
import '../../providers/user_profile_provider.dart';


class RoleManagementScreen extends StatefulWidget {
  const RoleManagementScreen({Key? key}) : super(key: key);

  @override
  State<RoleManagementScreen> createState() => _RoleManagementScreenState();
}

class _RoleManagementScreenState extends State<RoleManagementScreen> {
  UserRole _selectedRole = UserRole.inspector;

  @override
  void initState() {
    super.initState();
    // Initialize with current user role
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeRole();
    });
  }

  void _initializeRole() {
    final userProfileProvider = Provider.of<UserProfileProvider>(context, listen: false);
    final user = userProfileProvider.user;
    
    if (user != null) {
      setState(() {
        _selectedRole = user.role;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Role Management'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: Consumer2<UserProfileProvider, AuthProvider>(
        builder: (context, userProfileProvider, authProvider, _) {
          if (userProfileProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          final user = userProfileProvider.user;
          if (user == null) {
            return const Center(child: Text('User not found'));
          }

          // Check if current user is an admin
          final currentUser = authProvider.currentUser;
          final isAdmin = currentUser?.role == UserRole.admin;

          if (!isAdmin) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.lock, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Access Denied',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'You need administrator privileges to manage roles.',
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          }

          return _buildRoleManagementContent(context, userProfileProvider, user);
        },
      ),
    );
  }

  Widget _buildRoleManagementContent(BuildContext context, UserProfileProvider userProfileProvider, User user) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildUserInfoCard(user),
          const SizedBox(height: 24),
          _buildRoleSelectionCard(context, user),
          const SizedBox(height: 24),
          if (userProfileProvider.errorMessage != null)
            Container(
              padding: const EdgeInsets.all(8.0),
              decoration: BoxDecoration(
                color: Colors.red[50],
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(color: Colors.red),
              ),
              child: Row(
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(width: 8.0),
                  Expanded(
                    child: Text(
                      userProfileProvider.errorMessage!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
            ),
          const SizedBox(height: 24),
          _buildActionButtons(context, userProfileProvider, user),
        ],
      ),
    );
  }

  Widget _buildUserInfoCard(User user) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'User Information',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.blue,
                child: Text(
                  (user.displayName?.isNotEmpty == true) ? user.displayName![0].toUpperCase() : '?',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
              title: Text(user.displayName ?? 'Unknown User'),
              subtitle: Text(user.email),
            ),
            const Divider(),
            _buildInfoRow('User ID', user.uid),
            _buildInfoRow('Organization', user.organization ?? 'Not specified'),
            _buildInfoRow('Current Role', _getRoleName(user.role)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          Text(value),
        ],
      ),
    );
  }

  Widget _buildRoleSelectionCard(BuildContext context, User user) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Role Assignment',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const Divider(),
            const SizedBox(height: 8),
            const Text(
              'Select a role to assign to this user:',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            _buildRoleOption(
              context,
              UserRole.admin,
              'Administrator',
              'Full access to all features and settings',
              Icons.admin_panel_settings,
              Colors.red,
            ),
            const SizedBox(height: 8),
            _buildRoleOption(
              context,
              UserRole.moderator,
              'Moderator',
              'Can manage content but not system settings',
              Icons.security,
              Colors.orange,
            ),
            const SizedBox(height: 8),
            _buildRoleOption(
              context,
              UserRole.inspector,
              'Inspector',
              'Standard access to application features',
              Icons.person,
              Colors.blue,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleOption(
    BuildContext context,
    UserRole role,
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    return RadioListTile<UserRole>(
      title: Row(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Text(title),
        ],
      ),
      subtitle: Text(description),
      value: role,
      groupValue: _selectedRole,
      onChanged: (UserRole? value) {
        if (value != null) {
          setState(() {
            _selectedRole = value;
          });
        }
      },
      activeColor: color,
      selected: _selectedRole == role,
      contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
        side: BorderSide(
          color: _selectedRole == role ? color : Colors.grey.shade300,
          width: _selectedRole == role ? 2 : 1,
        ),
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, UserProfileProvider userProfileProvider, User user) {
    final isRoleChanged = user.role != _selectedRole;

    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: () {
              Navigator.pop(context);
            },
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: userProfileProvider.isLoading || !isRoleChanged
                ? null
                : () => _updateUserRole(context, userProfileProvider),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
              backgroundColor: isRoleChanged ? Theme.of(context).primaryColor : Colors.grey,
            ),
            child: userProfileProvider.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Update Role'),
          ),
        ),
      ],
    );
  }

  Future<void> _updateUserRole(BuildContext context, UserProfileProvider userProfileProvider) async {
    // Show confirmation dialog
    final confirmed = await _showConfirmationDialog(context);
    
    if (confirmed && context.mounted) {
      await userProfileProvider.updateRole(_selectedRole);

      if (userProfileProvider.state != UserProfileState.error && context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('User role updated to ${_getRoleName(_selectedRole)}')),
        );
        Navigator.pop(context);
      }
    }
  }

  Future<bool> _showConfirmationDialog(BuildContext context) async {
    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Confirm Role Change'),
              content: Text(
                'Are you sure you want to change this user\'s role to ${_getRoleName(_selectedRole)}?\n\n'
                'This will modify their permissions and access level.',
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                  child: const Text('Cancel'),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.red,
                  ),
                  child: const Text('Change Role'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  String _getRoleName(UserRole role) {
    switch (role) {
      case UserRole.admin:
        return 'Administrator';
      case UserRole.moderator:
        return 'Moderator';
      case UserRole.inspector:
        return 'Inspector';
      case UserRole.manager:
        return 'Manager';
      default:
        return 'Unknown';
    }
  }
}