import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:provider/provider.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/presentation/providers/auth_provider.dart';
import 'package:safestride/presentation/providers/user_profile_provider.dart';
import 'package:safestride/presentation/screens/profile/profile_screen.dart';
import 'package:safestride/presentation/widgets/app_bar_widget.dart';
import 'package:safestride/presentation/widgets/loading_indicator.dart';

import 'profile_screen_test.mocks.dart';

@GenerateMocks([AuthProvider, UserProfileProvider])
void main() {
  late MockAuthProvider mockAuthProvider;
  late MockUserProfileProvider mockUserProfileProvider;

  setUp(() {
    mockAuthProvider = MockAuthProvider();
    mockUserProfileProvider = MockUserProfileProvider();
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: MultiProvider(
        providers: [
          ChangeNotifierProvider<AuthProvider>.value(value: mockAuthProvider),
          ChangeNotifierProvider<UserProfileProvider>.value(value: mockUserProfileProvider),
        ],
        child: const ProfileScreen(),
      ),
    );
  }

  final testUser = User(
    id: 'test-uid-123',
    email: '<EMAIL>',
    displayName: 'Test User',
    organization: 'Test Organization',
    role: UserRole.inspector,
    createdAt: DateTime(2024, 1, 1),
    lastLoginAt: DateTime(2024, 1, 2),
    preferences: UserPreferences(
      notificationsEnabled: true,
      language: 'English',
      theme: 'Light',
      offlineMode: false,
    ),
  );

  group('ProfileScreen Widget Tests', () {
    testWidgets('should display app bar with correct title', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.initial);
      when(mockUserProfileProvider.user).thenReturn(null);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(AppBarWidget), findsOneWidget);
      expect(find.text('Profile'), findsOneWidget);
    });

    testWidgets('should display loading indicator when loading', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(true);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loading);
      when(mockUserProfileProvider.user).thenReturn(null);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(LoadingIndicator), findsOneWidget);
    });

    testWidgets('should display error message when in error state', (WidgetTester tester) async {
      // Arrange
      const errorMessage = 'Failed to load user profile';
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.error);
      when(mockUserProfileProvider.user).thenReturn(null);
      when(mockUserProfileProvider.errorMessage).thenReturn(errorMessage);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Error: $errorMessage'), findsOneWidget);
      expect(find.text('Retry'), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
    });

    testWidgets('should display user not found when user is null', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(null);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('User not found'), findsOneWidget);
    });

    testWidgets('should display user profile when loaded successfully', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Test User'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('inspector'), findsOneWidget);
      expect(find.text('Test Organization'), findsOneWidget);
    });

    testWidgets('should display profile header with avatar and user info', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircleAvatar), findsOneWidget);
      expect(find.text('T'), findsOneWidget); // First letter of display name
      expect(find.byType(Chip), findsOneWidget);
    });

    testWidgets('should display profile details card', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Profile Details'), findsOneWidget);
      expect(find.text('Organization'), findsOneWidget);
      expect(find.text('Created'), findsOneWidget);
      expect(find.text('Last Login'), findsOneWidget);
    });

    testWidgets('should display preferences section', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Preferences'), findsOneWidget);
      expect(find.text('Notifications'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
      expect(find.text('Offline Mode'), findsOneWidget);
      expect(find.text('Enabled'), findsNWidgets(1)); // Notifications enabled
      expect(find.text('English'), findsOneWidget);
      expect(find.text('Light'), findsOneWidget);
      expect(find.text('Disabled'), findsOneWidget); // Offline mode disabled
    });

    testWidgets('should display action buttons', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Edit Profile'), findsOneWidget);
      expect(find.text('Sync Data'), findsOneWidget);
      expect(find.byIcon(Icons.edit), findsOneWidget);
      expect(find.byIcon(Icons.sync), findsOneWidget);
    });

    testWidgets('should show syncing state when syncing', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(true);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Syncing...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should call syncUserData when sync button is tapped', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text('Sync Data'));
      await tester.pump();

      // Assert
      verify(mockUserProfileProvider.syncUserData()).called(1);
    });

    testWidgets('should call loadUserProfile on retry button tap', (WidgetTester tester) async {
      // Arrange
      const errorMessage = 'Failed to load user profile';
      final mockUser = User(
        id: 'test-uid',
        email: '<EMAIL>',
        displayName: 'Test User',
        organization: 'Test Org',
        role: UserRole.inspector,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
        preferences: UserPreferences(),
      );
      
      when(mockAuthProvider.currentUser).thenReturn(mockUser);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.error);
      when(mockUserProfileProvider.user).thenReturn(null);
      when(mockUserProfileProvider.errorMessage).thenReturn(errorMessage);

      // Act
      await tester.pumpWidget(createTestWidget());
      await tester.tap(find.text('Retry'));
      await tester.pump();

      // Assert
      verify(mockUserProfileProvider.loadUserProfile('test-uid')).called(1);
    });

    testWidgets('should support pull to refresh', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());
      
      // Find the RefreshIndicator and trigger refresh
      await tester.fling(find.byType(RefreshIndicator), const Offset(0, 300), 1000);
      await tester.pump();
      await tester.pump(const Duration(seconds: 1));

      // Assert
      verify(mockUserProfileProvider.refreshProfile()).called(1);
    });

    testWidgets('should display correct role colors', (WidgetTester tester) async {
      // Arrange
      final adminUser = testUser.copyWith(role: UserRole.admin);
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(adminUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('admin'), findsOneWidget);
      final chip = tester.widget<Chip>(find.byType(Chip));
      expect(chip.backgroundColor, equals(Colors.red[100]));
    });

    testWidgets('should format dates correctly', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(testUser);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('1/1/2024'), findsOneWidget); // Created date
      expect(find.text('2/1/2024'), findsOneWidget); // Last login date
    });

    testWidgets('should handle empty display name gracefully', (WidgetTester tester) async {
      // Arrange
      final userWithEmptyName = testUser.copyWith(displayName: '');
      when(mockAuthProvider.currentUser).thenReturn(null);
      when(mockUserProfileProvider.isLoading).thenReturn(false);
      when(mockUserProfileProvider.state).thenReturn(UserProfileState.loaded);
      when(mockUserProfileProvider.user).thenReturn(userWithEmptyName);
      when(mockUserProfileProvider.errorMessage).thenReturn(null);
      when(mockUserProfileProvider.isSyncing).thenReturn(false);

      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('?'), findsOneWidget); // Default avatar text
    });
  });
}