// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in safestride/test/integration/location_tagger_integration_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i5;

import 'package:geolocator/geolocator.dart' as _i3;
import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/walkabout.dart' as _i2;
import 'package:safestride/services/location/location_service.dart' as _i4;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeGeoPoint_0 extends _i1.SmartFake implements _i2.GeoPoint {
  _FakeGeoPoint_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

class _FakeLocationSettings_1 extends _i1.SmartFake
    implements _i3.LocationSettings {
  _FakeLocationSettings_1(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [LocationService].
///
/// See the documentation for Mockito's code generation for more information.
class MockLocationService extends _i1.Mock implements _i4.LocationService {
  MockLocationService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i5.Future<_i2.GeoPoint> getCurrentLocation() =>
      (super.noSuchMethod(
            Invocation.method(#getCurrentLocation, []),
            returnValue: _i5.Future<_i2.GeoPoint>.value(
              _FakeGeoPoint_0(this, Invocation.method(#getCurrentLocation, [])),
            ),
          )
          as _i5.Future<_i2.GeoPoint>);

  @override
  _i5.Future<bool> isLocationServiceEnabled() =>
      (super.noSuchMethod(
            Invocation.method(#isLocationServiceEnabled, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);

  @override
  _i5.Future<_i4.LocationPermissionStatus> checkLocationPermission() =>
      (super.noSuchMethod(
            Invocation.method(#checkLocationPermission, []),
            returnValue: _i5.Future<_i4.LocationPermissionStatus>.value(
              _i4.LocationPermissionStatus.denied,
            ),
          )
          as _i5.Future<_i4.LocationPermissionStatus>);

  @override
  _i5.Future<_i4.LocationPermissionStatus> requestLocationPermission() =>
      (super.noSuchMethod(
            Invocation.method(#requestLocationPermission, []),
            returnValue: _i5.Future<_i4.LocationPermissionStatus>.value(
              _i4.LocationPermissionStatus.denied,
            ),
          )
          as _i5.Future<_i4.LocationPermissionStatus>);

  @override
  _i5.Future<_i2.GeoPoint> getLocationWithAccuracy(
    _i3.LocationAccuracy? accuracy,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#getLocationWithAccuracy, [accuracy]),
            returnValue: _i5.Future<_i2.GeoPoint>.value(
              _FakeGeoPoint_0(
                this,
                Invocation.method(#getLocationWithAccuracy, [accuracy]),
              ),
            ),
          )
          as _i5.Future<_i2.GeoPoint>);

  @override
  double calculateDistance(_i2.GeoPoint? point1, _i2.GeoPoint? point2) =>
      (super.noSuchMethod(
            Invocation.method(#calculateDistance, [point1, point2]),
            returnValue: 0.0,
          )
          as double);

  @override
  bool isWithinRadius(
    _i2.GeoPoint? center,
    _i2.GeoPoint? point,
    double? radiusMeters,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#isWithinRadius, [center, point, radiusMeters]),
            returnValue: false,
          )
          as bool);

  @override
  _i5.Future<_i3.LocationSettings> getLocationSettings() =>
      (super.noSuchMethod(
            Invocation.method(#getLocationSettings, []),
            returnValue: _i5.Future<_i3.LocationSettings>.value(
              _FakeLocationSettings_1(
                this,
                Invocation.method(#getLocationSettings, []),
              ),
            ),
          )
          as _i5.Future<_i3.LocationSettings>);

  @override
  _i5.Future<bool> openLocationSettings() =>
      (super.noSuchMethod(
            Invocation.method(#openLocationSettings, []),
            returnValue: _i5.Future<bool>.value(false),
          )
          as _i5.Future<bool>);
}
