// Mocks generated by <PERSON><PERSON>to 5.4.6 from annotations
// in safestride/test/widget/presentation/screens/profile/preferences_screen_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i3;
import 'dart:ui' as _i5;

import 'package:mockito/mockito.dart' as _i1;
import 'package:safestride/domain/entities/user.dart' as _i4;
import 'package:safestride/presentation/providers/user_profile_provider.dart'
    as _i2;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

/// A class which mocks [UserProfileProvider].
///
/// See the documentation for Mockito's code generation for more information.
class MockUserProfileProvider extends _i1.Mock
    implements _i2.UserProfileProvider {
  MockUserProfileProvider() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i2.UserProfileState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _i2.UserProfileState.initial,
          )
          as _i2.UserProfileState);

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  bool get isSyncing =>
      (super.noSuchMethod(Invocation.getter(#isSyncing), returnValue: false)
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i3.Future<void> loadUserProfile(String? uid) =>
      (super.noSuchMethod(
            Invocation.method(#loadUserProfile, [uid]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updateProfile({
    required String? displayName,
    required String? organization,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updateProfile, [], {
              #displayName: displayName,
              #organization: organization,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updatePreferences({
    required bool? notificationsEnabled,
    required String? language,
    required String? theme,
    required bool? offlineMode,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#updatePreferences, [], {
              #notificationsEnabled: notificationsEnabled,
              #language: language,
              #theme: theme,
              #offlineMode: offlineMode,
            }),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> updateRole(_i4.UserRole? role) =>
      (super.noSuchMethod(
            Invocation.method(#updateRole, [role]),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> syncUserData() =>
      (super.noSuchMethod(
            Invocation.method(#syncUserData, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  _i3.Future<void> refreshProfile() =>
      (super.noSuchMethod(
            Invocation.method(#refreshProfile, []),
            returnValue: _i3.Future<void>.value(),
            returnValueForMissingStub: _i3.Future<void>.value(),
          )
          as _i3.Future<void>);

  @override
  void addListener(_i5.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i5.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}
