# Data Models

## Walkabout

**Purpose:** Represents a safety inspection session with metadata and hazard collection

**Key Attributes:**
- id: String - Unique identifier
- title: String - User-defined walkabout name
- description: String - Optional walkabout description
- createdAt: DateTime - Creation timestamp
- updatedAt: DateTime - Last modification timestamp
- status: WalkaboutStatus - (draft, in_progress, completed, archived)
- location: GeoPoint - Starting location coordinates
- userId: String - C<PERSON>'s user ID
- isCompleted: bool - Completion status
- syncStatus: SyncStatus - (local, syncing, synced, error)

**Relationships:**
- One-to-many with Hazard entities
- Belongs to User entity

## Hazard

**Purpose:** Represents individual safety hazards documented during walkabouts

**Key Attributes:**
- id: String - Unique identifier
- walkaboutId: String - Parent walkabout reference
- title: String - Hazard title/summary
- description: String - Detailed hazard description
- severity: HazardSeverity - (low, medium, high, critical)
- category: HazardCategory - (slip_trip_fall, electrical, chemical, etc.)
- location: GeoPoint - Precise hazard location
- photos: List<String> - Photo file paths/URLs
- notes: String - Additional notes
- createdAt: DateTime - Documentation timestamp
- updatedAt: DateTime - Last modification timestamp
- syncStatus: SyncStatus - (local, syncing, synced, error)

**Relationships:**
- Belongs to Walkabout entity
- One-to-many with Photo entities

## User

**Purpose:** User account and profile information

**Key Attributes:**
- uid: String - Firebase Auth user ID
- email: String - User email address
- displayName: String - User's display name
- organization: String - User's organization/company
- role: UserRole - (inspector, manager, admin)
- createdAt: DateTime - Account creation timestamp
- lastLoginAt: DateTime - Last login timestamp
- preferences: UserPreferences - App settings and preferences

**Relationships:**
- One-to-many with Walkabout entities
