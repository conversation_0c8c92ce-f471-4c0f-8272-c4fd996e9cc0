# Component Library / Design System

**Design System Approach:** Custom Flutter component library optimized for offline-first operation

## Core Components

### Action Button

**Purpose:** Primary interaction element for key actions

**Variants:**
- Primary (New Walkabout, Add Hazard)
- Secondary (Save Draft, Continue)
- Destructive (Delete, Cancel Walkabout)

**States:**
- Default
- Pressed
- Loading
- Disabled
- Offline

**Usage Guidelines:**
- Use Primary for main actions
- Loading state shows sync status
- Offline state indicates queued actions

### Status Indicator

**Purpose:** Show sync and system status

**Variants:**
- Sync Status (Online/Offline/Syncing)
- Progress (Upload/Download)
- Warning (Storage/Battery)

**States:**
- Online (Green)
- Offline (Gray)
- Warning (Yellow)
- Error (Red)

**Usage Guidelines:**
- Always visible in header
- Tap for detailed status
- Automatic updates
