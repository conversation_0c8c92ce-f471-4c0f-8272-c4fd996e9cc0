import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../domain/entities/walkabout.dart';
import '../../../services/location/location_service.dart';

/// Widget for tagging hazard locations with enhanced functionality
/// 
/// Provides comprehensive location tagging interface for hazards including:
/// - Current GPS location retrieval
/// - Manual coordinate entry
/// - Location accuracy display
/// - Permission handling
/// - Location validation
class LocationTagger extends StatefulWidget {
  final GeoPoint? selectedLocation;
  final ValueChanged<GeoPoint?> onLocationChanged;
  final bool isRequired;
  final String? label;
  final LocationService? locationService;

  const LocationTagger({
    super.key,
    required this.selectedLocation,
    required this.onLocationChanged,
    this.isRequired = false,
    this.label,
    this.locationService,
  });

  @override
  State<LocationTagger> createState() => _LocationTaggerState();
}

class _LocationTaggerState extends State<LocationTagger> {
  final _latitudeController = TextEditingController();
  final _longitudeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  
  bool _isManualEntry = false;
  bool _isGettingLocation = false;
  String? _locationError;
  LocationPermissionStatus? _permissionStatus;

  @override
  void initState() {
    super.initState();
    _updateControllers();
    _checkLocationPermission();
  }

  @override
  void didUpdateWidget(LocationTagger oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.selectedLocation != widget.selectedLocation) {
      _updateControllers();
    }
  }

  @override
  void dispose() {
    _latitudeController.dispose();
    _longitudeController.dispose();
    super.dispose();
  }

  void _updateControllers() {
    if (widget.selectedLocation != null) {
      _latitudeController.text = widget.selectedLocation!.latitude.toStringAsFixed(6);
      _longitudeController.text = widget.selectedLocation!.longitude.toStringAsFixed(6);
    } else {
      _latitudeController.clear();
      _longitudeController.clear();
    }
  }

  Future<void> _checkLocationPermission() async {
    if (widget.locationService != null) {
      try {
        final status = await widget.locationService!.checkLocationPermission();
        setState(() {
          _permissionStatus = status;
        });
      } catch (e) {
        // Permission check failed, continue without status
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16.0),
            
            if (_locationError != null) ...[
              _buildErrorDisplay(),
              const SizedBox(height: 12.0),
            ],
            
            if (!_isManualEntry) ...[
              _buildCurrentLocationSection(),
            ] else ...[
              _buildManualEntrySection(),
            ],
            
            if (widget.selectedLocation != null) ...[
              const SizedBox(height: 12.0),
              _buildLocationDisplay(),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Icon(
          Icons.location_on,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8.0),
        Expanded(
          child: Text(
            widget.label ?? 'Hazard Location',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        if (widget.isRequired)
          Text(
            '*',
            style: TextStyle(
              color: Theme.of(context).colorScheme.error,
              fontSize: 16,
            ),
          ),
        const SizedBox(width: 8.0),
        Switch(
          value: _isManualEntry,
          onChanged: (value) {
            setState(() {
              _isManualEntry = value;
              _locationError = null;
              if (!value) {
                _latitudeController.clear();
                _longitudeController.clear();
                widget.onLocationChanged(null);
              }
            });
          },
        ),
        const SizedBox(width: 4.0),
        const Text('Manual'),
      ],
    );
  }

  Widget _buildErrorDisplay() {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        border: Border.all(color: Colors.red.shade200),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red.shade600, size: 20),
          const SizedBox(width: 8.0),
          Expanded(
            child: Text(
              _locationError!,
              style: TextStyle(color: Colors.red.shade600),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentLocationSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        ElevatedButton.icon(
          onPressed: _isGettingLocation ? null : _getCurrentLocation,
          icon: _isGettingLocation
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.my_location),
          label: Text(_isGettingLocation ? 'Getting Location...' : 'Get Current Location'),
        ),
        
        if (_permissionStatus != null && !_permissionStatus!.isGranted) ...[
          const SizedBox(height: 8.0),
          Container(
            padding: const EdgeInsets.all(8.0),
            decoration: BoxDecoration(
              color: Colors.orange.shade50,
              border: Border.all(color: Colors.orange.shade200),
              borderRadius: BorderRadius.circular(6.0),
            ),
            child: Row(
              children: [
                Icon(Icons.warning_amber, color: Colors.orange.shade600, size: 16),
                const SizedBox(width: 8.0),
                Expanded(
                  child: Text(
                    _permissionStatus!.description,
                    style: TextStyle(
                      color: Colors.orange.shade600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildManualEntrySection() {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: _latitudeController,
                  decoration: const InputDecoration(
                    labelText: 'Latitude',
                    hintText: '37.7749',
                    border: OutlineInputBorder(),
                    isDense: true,
                  ),
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                    signed: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
                  ],
                  validator: _validateLatitude,
                  onChanged: _onManualLocationChanged,
                ),
              ),
              const SizedBox(width: 12.0),
              Expanded(
                child: TextFormField(
                  controller: _longitudeController,
                  decoration: const InputDecoration(
                    labelText: 'Longitude',
                    hintText: '-122.4194',
                    border: OutlineInputBorder(),
                    isDense: true,
                  ),
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                    signed: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
                  ],
                  validator: _validateLongitude,
                  onChanged: _onManualLocationChanged,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8.0),
          Text(
            'Enter coordinates in decimal degrees format',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationDisplay() {
    return Container(
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        border: Border.all(color: Colors.green.shade200),
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Row(
        children: [
          Icon(Icons.check_circle, color: Colors.green.shade600, size: 20),
          const SizedBox(width: 8.0),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Location Tagged',
                  style: TextStyle(
                    color: Colors.green.shade600,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  'Lat: ${widget.selectedLocation!.latitude.toStringAsFixed(6)}, '
                  'Lng: ${widget.selectedLocation!.longitude.toStringAsFixed(6)}',
                  style: TextStyle(
                    color: Colors.green.shade600,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: _clearLocation,
            icon: Icon(Icons.clear, color: Colors.green.shade600, size: 18),
            tooltip: 'Clear location',
            constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
          ),
        ],
      ),
    );
  }

  // Event handlers and validation methods
  Future<void> _getCurrentLocation() async {
    if (widget.locationService == null) {
      setState(() {
        _locationError = 'Location service not available';
      });
      return;
    }

    setState(() {
      _isGettingLocation = true;
      _locationError = null;
    });

    try {
      final location = await widget.locationService!.getCurrentLocation();
      widget.onLocationChanged(location);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Current location retrieved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _locationError = e.toString().replaceFirst('LocationException: ', '');
      });
    } finally {
      setState(() {
        _isGettingLocation = false;
      });
    }
  }

  void _onManualLocationChanged(String value) {
    if (_formKey.currentState?.validate() == true) {
      final lat = double.tryParse(_latitudeController.text);
      final lng = double.tryParse(_longitudeController.text);
      
      if (lat != null && lng != null) {
        widget.onLocationChanged(GeoPoint(latitude: lat, longitude: lng));
      }
    } else {
      widget.onLocationChanged(null);
    }
  }

  void _clearLocation() {
    _latitudeController.clear();
    _longitudeController.clear();
    widget.onLocationChanged(null);
    setState(() {
      _locationError = null;
    });
  }

  String? _validateLatitude(String? value) {
    if (value == null || value.isEmpty) {
      return widget.isRequired ? 'Latitude is required' : null;
    }
    
    final lat = double.tryParse(value);
    if (lat == null) {
      return 'Invalid latitude format';
    }
    
    if (lat < -90 || lat > 90) {
      return 'Latitude must be between -90 and 90';
    }
    
    return null;
  }

  String? _validateLongitude(String? value) {
    if (value == null || value.isEmpty) {
      return widget.isRequired ? 'Longitude is required' : null;
    }
    
    final lng = double.tryParse(value);
    if (lng == null) {
      return 'Invalid longitude format';
    }
    
    if (lng < -180 || lng > 180) {
      return 'Longitude must be between -180 and 180';
    }
    
    return null;
  }
}
