import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/entities/user.dart';
import 'package:safestride/domain/entities/user_preferences.dart';
import 'package:safestride/domain/repositories/auth_repository.dart';
import 'package:safestride/domain/usecases/get_current_user.dart';

import 'get_current_user_test.mocks.dart';

@GenerateMocks([AuthRepository])
void main() {
  late GetCurrentUserUseCase useCase;
  late MockAuthRepository mockAuthRepository;
  late User testUser;

  setUp(() {
    mockAuthRepository = MockAuthRepository();
    useCase = GetCurrentUserUseCase(mockAuthRepository);
    testUser = User(
      uid: 'test-uid',
      email: '<EMAIL>',
      displayName: 'Test User',
      organization: 'Test Org',
      role: UserRole.inspector,
      createdAt: DateTime.now(),
      lastLoginAt: DateTime.now(),
      preferences: UserPreferences(
        theme: 'light',
        language: 'en',
        notifications: true,
      ),
    );
  });

  group('GetCurrentUserUseCase', () {
    test('should return current user when user is authenticated', () async {
      // Arrange
      when(mockAuthRepository.getCurrentUser())
          .thenAnswer((_) async => testUser);

      // Act
      final result = await useCase.call();

      // Assert
      expect(result, equals(testUser));
      verify(mockAuthRepository.getCurrentUser()).called(1);
    });

    test('should return null when no user is authenticated', () async {
      // Arrange
      when(mockAuthRepository.getCurrentUser())
          .thenAnswer((_) async => null);

      // Act
      final result = await useCase.call();

      // Assert
      expect(result, isNull);
      verify(mockAuthRepository.getCurrentUser()).called(1);
    });

    test('should return cached user when repository throws exception', () async {
      // Arrange
      when(mockAuthRepository.getCurrentUser())
          .thenThrow(Exception('Network error'));
      when(mockAuthRepository.getCachedUser())
          .thenAnswer((_) async => testUser);

      // Act
      final result = await useCase.call();

      // Assert
      expect(result, equals(testUser));
      verify(mockAuthRepository.getCurrentUser()).called(1);
      verify(mockAuthRepository.getCachedUser()).called(1);
    });

    test('should return null when both current user and cached user fail', () async {
      // Arrange
      when(mockAuthRepository.getCurrentUser())
          .thenThrow(Exception('Network error'));
      when(mockAuthRepository.getCachedUser())
          .thenThrow(Exception('Cache error'));

      // Act
      final result = await useCase.call();

      // Assert
      expect(result, isNull);
      verify(mockAuthRepository.getCurrentUser()).called(1);
      verify(mockAuthRepository.getCachedUser()).called(1);
    });

    test('should return null when cached user is null', () async {
      // Arrange
      when(mockAuthRepository.getCurrentUser())
          .thenThrow(Exception('Network error'));
      when(mockAuthRepository.getCachedUser())
          .thenAnswer((_) async => null);

      // Act
      final result = await useCase.call();

      // Assert
      expect(result, isNull);
      verify(mockAuthRepository.getCurrentUser()).called(1);
      verify(mockAuthRepository.getCachedUser()).called(1);
    });
  });
}