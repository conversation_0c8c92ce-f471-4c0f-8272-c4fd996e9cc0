import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import '../../domain/entities/user.dart';
import '../../domain/usecases/register_with_email.dart';
import '../../domain/usecases/login_with_email.dart';
import '../../domain/usecases/login_with_sso.dart';
import '../../domain/usecases/logout.dart';
import '../../domain/usecases/reset_password.dart';
import '../../domain/usecases/get_current_user.dart';
import '../../services/sync/profile_sync_service.dart';

/// Authentication state enumeration
enum AuthState { initial, loading, authenticated, unauthenticated, error }

/// Provider for managing authentication state and operations
class AuthProvider extends ChangeNotifier {
  final RegisterWithEmailUseCase _registerWithEmailUseCase;
  final LoginWithEmailUseCase _loginWithEmailUseCase;
  final LoginWithSSOUseCase _loginWithSSOUseCase;
  final LogoutUseCase _logoutUseCase;
  final ResetPasswordUseCase _resetPasswordUseCase;
  final GetCurrentUserUseCase _getCurrentUserUseCase;

  AuthProvider({
    required RegisterWithEmailUseCase registerWithEmailUseCase,
    required LoginWithEmailUseCase loginWithEmailUseCase,
    required LoginWithSSOUseCase loginWithSSOUseCase,
    required LogoutUseCase logoutUseCase,
    required ResetPasswordUseCase resetPasswordUseCase,
    required GetCurrentUserUseCase getCurrentUserUseCase,
  }) : _registerWithEmailUseCase = registerWithEmailUseCase,
       _loginWithEmailUseCase = loginWithEmailUseCase,
       _loginWithSSOUseCase = loginWithSSOUseCase,
       _logoutUseCase = logoutUseCase,
       _resetPasswordUseCase = resetPasswordUseCase,
       _getCurrentUserUseCase = getCurrentUserUseCase {
    _initializeAuthState();
  }

  // Private fields
  AuthState _state = AuthState.initial;
  User? _currentUser;
  String? _errorMessage;
  bool _isLoading = false;

  // Public getters
  AuthState get state => _state;
  User? get currentUser => _currentUser;
  String? get errorMessage => _errorMessage;
  bool get isLoading => _isLoading;
  bool get isAuthenticated =>
      _state == AuthState.authenticated && _currentUser != null;

  /// Initialize authentication state by checking current user
  Future<void> _initializeAuthState() async {
    try {
      _setLoading(true);
      final user = await _getCurrentUserUseCase.call();

      if (user != null) {
        _setAuthenticatedState(user);
      } else {
        _setUnauthenticatedState();
      }
    } catch (e) {
      _setErrorState('Failed to initialize authentication: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Register a new user with email and password
  Future<void> registerWithEmail({
    required String email,
    required String password,
    String? displayName,
    String? organization,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _registerWithEmailUseCase.call(
        email: email,
        password: password,
        displayName: displayName,
        organization: organization,
      );

      _setAuthenticatedState(user);
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Login user with email and password
  Future<void> loginWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _loginWithEmailUseCase.call(
        email: email,
        password: password,
      );

      _setAuthenticatedState(user);
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Login user with Google SSO
  Future<void> loginWithSSO() async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _loginWithSSOUseCase.call();
      _setAuthenticatedState(user);
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Logout current user
  Future<void> logout() async {
    try {
      _setLoading(true);
      _clearError();

      await _logoutUseCase.call();
      _setUnauthenticatedState();
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Reset password for given email
  Future<void> resetPassword({required String email}) async {
    try {
      _setLoading(true);
      _clearError();

      await _resetPasswordUseCase.call(email: email);
      // Password reset email sent successfully
      // Note: This doesn't change authentication state
    } catch (e) {
      _setErrorState(e.toString());
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh current user data
  Future<void> refreshUser() async {
    try {
      _setLoading(true);
      _clearError();

      final user = await _getCurrentUserUseCase.call();

      if (user != null) {
        _setAuthenticatedState(user);
      } else {
        _setUnauthenticatedState();
      }
    } catch (e) {
      _setErrorState('Failed to refresh user: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Listen to authentication state changes
  void listenToAuthChanges() {
    _getCurrentUserUseCase.authStateStream.listen(
      (user) {
        if (user != null) {
          _setAuthenticatedState(user);
        } else {
          _setUnauthenticatedState();
        }
      },
      onError: (error) {
        _setErrorState('Auth state change error: ${error.toString()}');
      },
    );
  }

  /// Clear any error messages
  void clearError() {
    _clearError();
    notifyListeners();
  }

  /// Check if user has specific role
  bool hasRole(UserRole role) {
    return _currentUser?.role == role;
  }

  /// Check if user is admin
  bool get isAdmin => hasRole(UserRole.admin);

  /// Check if user is inspector
  bool get isInspector => hasRole(UserRole.inspector);

  /// Check if user is manager
  bool get isManager => hasRole(UserRole.manager);

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    if (loading) {
      _state = AuthState.loading;
    }
    notifyListeners();
  }

  void _setAuthenticatedState(User user) {
    _state = AuthState.authenticated;
    _currentUser = user;
    _errorMessage = null;
    _isLoading = false;

    // Initialize profile sync service with current user ID
    try {
      final profileSyncService = GetIt.instance<ProfileSyncService>();
      profileSyncService.initialize(user.uid);
    } catch (e) {
      print('Failed to initialize profile sync service: $e');
    }

    notifyListeners();
  }

  void _setUnauthenticatedState() {
    _state = AuthState.unauthenticated;
    _currentUser = null;
    _errorMessage = null;
    _isLoading = false;
    
    // Clean up profile sync service
    try {
      final profileSyncService = GetIt.instance<ProfileSyncService>();
      profileSyncService.dispose();
    } catch (e) {
      print('Failed to dispose profile sync service: $e');
    }
    
    notifyListeners();
  }

  void _setErrorState(String error) {
    _state = AuthState.error;
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  void _clearError() {
    _errorMessage = null;
    if (_state == AuthState.error) {
      _state =
          _currentUser != null
              ? AuthState.authenticated
              : AuthState.unauthenticated;
    }
  }

  @override
  void dispose() {
    // Clean up any subscriptions if needed
    super.dispose();
  }
}
