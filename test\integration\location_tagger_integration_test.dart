import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:safestride/domain/entities/walkabout.dart';
import 'package:safestride/services/location/location_service.dart';
import 'package:safestride/presentation/widgets/hazard/location_tagger.dart';

import 'location_tagger_integration_test.mocks.dart';

@GenerateMocks([LocationService])
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('LocationTagger Widget Integration Tests', () {
    late MockLocationService mockLocationService;
    GeoPoint? selectedLocation;

    setUp(() {
      mockLocationService = MockLocationService();
      selectedLocation = null;
    });

    Widget createTestApp({
      bool isRequired = false,
      String? label,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: LocationTagger(
              selectedLocation: selectedLocation,
              onLocationChanged: (location) {
                selectedLocation = location;
              },
              isRequired: isRequired,
              label: label,
              locationService: mockLocationService,
            ),
          ),
        ),
      );
    }

    testWidgets('displays default UI elements correctly', (tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Hazard Location'), findsOneWidget);
      expect(find.byIcon(Icons.location_on), findsOneWidget);
      expect(find.byType(Switch), findsOneWidget);
      expect(find.text('Manual'), findsOneWidget);
      expect(find.text('Get Current Location'), findsOneWidget);
    });

    testWidgets('displays custom label when provided', (tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestApp(label: 'Custom Location Label'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Custom Location Label'), findsOneWidget);
      expect(find.text('Hazard Location'), findsNothing);
    });

    testWidgets('shows required indicator when isRequired is true', (tester) async {
      // Arrange & Act
      await tester.pumpWidget(createTestApp(isRequired: true));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('*'), findsOneWidget);
    });

    testWidgets('switches between GPS and manual entry modes', (tester) async {
      // Arrange
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Assert initial state (GPS mode)
      expect(find.text('Get Current Location'), findsOneWidget);
      expect(find.text('Latitude'), findsNothing);
      expect(find.text('Longitude'), findsNothing);

      // Act: Switch to manual mode
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      // Assert manual mode
      expect(find.text('Get Current Location'), findsNothing);
      expect(find.text('Latitude'), findsOneWidget);
      expect(find.text('Longitude'), findsOneWidget);
      expect(find.text('Enter coordinates in decimal degrees format'), findsOneWidget);

      // Act: Switch back to GPS mode
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      // Assert back to GPS mode
      expect(find.text('Get Current Location'), findsOneWidget);
      expect(find.text('Latitude'), findsNothing);
      expect(find.text('Longitude'), findsNothing);
    });

    testWidgets('handles successful GPS location retrieval', (tester) async {
      // Arrange
      const testLocation = GeoPoint(latitude: 37.7749, longitude: -122.4194);
      when(mockLocationService.getCurrentLocation())
          .thenAnswer((_) async => testLocation);
      when(mockLocationService.checkLocationPermission())
          .thenAnswer((_) async => LocationPermissionStatus.whileInUse);

      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text('Get Current Location'));
      await tester.pump(); // Start the async operation
      await tester.pump(const Duration(seconds: 1)); // Wait for completion

      // Assert
      expect(find.text('Location Tagged'), findsOneWidget);
      expect(find.text('Lat: 37.774900, Lng: -122.419400'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
      expect(find.text('Current location retrieved successfully'), findsOneWidget);
      
      verify(mockLocationService.getCurrentLocation()).called(1);
    });

    testWidgets('handles GPS location error gracefully', (tester) async {
      // Arrange
      when(mockLocationService.getCurrentLocation())
          .thenThrow(const LocationException('Location services are disabled'));
      when(mockLocationService.checkLocationPermission())
          .thenAnswer((_) async => LocationPermissionStatus.denied);

      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text('Get Current Location'));
      await tester.pump(); // Start the async operation
      await tester.pump(const Duration(seconds: 1)); // Wait for completion

      // Assert
      expect(find.text('Location services are disabled'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
      expect(find.text('Location Tagged'), findsNothing);
      
      verify(mockLocationService.getCurrentLocation()).called(1);
    });

    testWidgets('validates manual coordinate entry', (tester) async {
      // Arrange
      await tester.pumpWidget(createTestApp(isRequired: true));
      await tester.pumpAndSettle();

      // Switch to manual mode
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      // Act & Assert: Test invalid latitude
      await tester.enterText(find.widgetWithText(TextFormField, 'Latitude'), '91');
      await tester.pump();
      expect(find.text('Latitude must be between -90 and 90'), findsOneWidget);

      await tester.enterText(find.widgetWithText(TextFormField, 'Latitude'), '-91');
      await tester.pump();
      expect(find.text('Latitude must be between -90 and 90'), findsOneWidget);

      // Act & Assert: Test invalid longitude
      await tester.enterText(find.widgetWithText(TextFormField, 'Longitude'), '181');
      await tester.pump();
      expect(find.text('Longitude must be between -180 and 180'), findsOneWidget);

      await tester.enterText(find.widgetWithText(TextFormField, 'Longitude'), '-181');
      await tester.pump();
      expect(find.text('Longitude must be between -180 and 180'), findsOneWidget);

      // Act & Assert: Test invalid format
      await tester.enterText(find.widgetWithText(TextFormField, 'Latitude'), 'invalid');
      await tester.pump();
      expect(find.text('Invalid latitude format'), findsOneWidget);

      await tester.enterText(find.widgetWithText(TextFormField, 'Longitude'), 'invalid');
      await tester.pump();
      expect(find.text('Invalid longitude format'), findsOneWidget);
    });

    testWidgets('accepts valid manual coordinates', (tester) async {
      // Arrange
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Switch to manual mode
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      // Act: Enter valid coordinates
      await tester.enterText(find.widgetWithText(TextFormField, 'Latitude'), '37.7749');
      await tester.enterText(find.widgetWithText(TextFormField, 'Longitude'), '-122.4194');
      await tester.pump();

      // Assert: Location should be tagged
      expect(find.text('Location Tagged'), findsOneWidget);
      expect(find.text('Lat: 37.774900, Lng: -122.419400'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('clears location when clear button is tapped', (tester) async {
      // Arrange: Set up widget with a location
      const testLocation = GeoPoint(latitude: 37.7749, longitude: -122.4194);
      when(mockLocationService.getCurrentLocation())
          .thenAnswer((_) async => testLocation);
      when(mockLocationService.checkLocationPermission())
          .thenAnswer((_) async => LocationPermissionStatus.whileInUse);

      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Get location first
      await tester.tap(find.text('Get Current Location'));
      await tester.pump();
      await tester.pump(const Duration(seconds: 1));

      // Verify location is set
      expect(find.text('Location Tagged'), findsOneWidget);

      // Act: Clear the location
      await tester.tap(find.byIcon(Icons.clear));
      await tester.pump();

      // Assert: Location should be cleared
      expect(find.text('Location Tagged'), findsNothing);
      expect(find.text('Get Current Location'), findsOneWidget);
    });

    testWidgets('shows permission warning for denied permissions', (tester) async {
      // Arrange
      when(mockLocationService.checkLocationPermission())
          .thenAnswer((_) async => LocationPermissionStatus.denied);

      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Assert
      expect(find.byIcon(Icons.warning_amber), findsOneWidget);
      expect(find.text('Location access is denied'), findsOneWidget);
    });

    testWidgets('shows loading state during GPS retrieval', (tester) async {
      // Arrange
      when(mockLocationService.getCurrentLocation())
          .thenAnswer((_) async {
            await Future.delayed(const Duration(seconds: 2));
            return const GeoPoint(latitude: 37.7749, longitude: -122.4194);
          });
      when(mockLocationService.checkLocationPermission())
          .thenAnswer((_) async => LocationPermissionStatus.whileInUse);

      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Act
      await tester.tap(find.text('Get Current Location'));
      await tester.pump();

      // Assert loading state
      expect(find.text('Getting Location...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for completion
      await tester.pump(const Duration(seconds: 3));

      // Assert final state
      expect(find.text('Get Current Location'), findsOneWidget);
      expect(find.text('Location Tagged'), findsOneWidget);
    });

    testWidgets('handles coordinate input formatting correctly', (tester) async {
      // Arrange
      await tester.pumpWidget(createTestApp());
      await tester.pumpAndSettle();

      // Switch to manual mode
      await tester.tap(find.byType(Switch));
      await tester.pumpAndSettle();

      // Act: Test various coordinate formats
      await tester.enterText(find.widgetWithText(TextFormField, 'Latitude'), '37.7749');
      await tester.enterText(find.widgetWithText(TextFormField, 'Longitude'), '-122.4194');
      await tester.pump();

      // Assert: Should accept decimal format
      expect(find.text('Location Tagged'), findsOneWidget);

      // Test negative coordinates
      await tester.enterText(find.widgetWithText(TextFormField, 'Latitude'), '-37.7749');
      await tester.enterText(find.widgetWithText(TextFormField, 'Longitude'), '122.4194');
      await tester.pump();

      // Assert: Should accept negative coordinates
      expect(find.text('Location Tagged'), findsOneWidget);
      expect(find.text('Lat: -37.774900, Lng: 122.419400'), findsOneWidget);
    });
  });
}
