import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:provider/provider.dart';
import 'package:safestride/presentation/providers/auth_provider.dart';
import 'package:safestride/presentation/screens/auth/forgot_password_screen.dart';

import 'forgot_password_screen_test.mocks.dart';

@GenerateMocks([AuthProvider])
void main() {
  late MockAuthProvider mockAuthProvider;

  setUp(() {
    mockAuthProvider = MockAuthProvider();
    when(mockAuthProvider.state).thenReturn(AuthState.initial);
    when(mockAuthProvider.user).thenReturn(null);
    when(mockAuthProvider.errorMessage).thenReturn(null);
  });

  Widget createTestWidget() {
    return MaterialApp(
      home: ChangeNotifierProvider<AuthProvider>.value(
        value: mockAuthProvider,
        child: const ForgotPasswordScreen(),
      ),
    );
  }

  group('ForgotPasswordScreen Widget Tests', () {
    testWidgets('should display forgot password form elements', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('Reset Password'), findsOneWidget);
      expect(find.text('Enter your email address and we\'ll send you a link to reset your password'), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget); // Email field
      expect(find.text('Email'), findsOneWidget);
      expect(find.text('Send Reset Link'), findsOneWidget);
      expect(find.text('Back to Sign In'), findsOneWidget);
    });

    testWidgets('should validate email field when empty', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Try to submit with empty email
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter your email'), findsOneWidget);
    });

    testWidgets('should validate email format', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter invalid email format
      await tester.enterText(find.byType(TextFormField), 'invalid-email');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Assert
      expect(find.text('Please enter a valid email'), findsOneWidget);
    });

    testWidgets('should call resetPassword when form is valid', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter valid email and submit
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Assert
      verify(mockAuthProvider.resetPassword(email: '<EMAIL>')).called(1);
    });

    testWidgets('should show loading indicator when state is loading', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.loading);
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should show error message when state is error', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.error);
      when(mockAuthProvider.errorMessage).thenReturn('User not found');
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.text('User not found'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('should show success message when reset email is sent', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.initial);
      await tester.pumpWidget(createTestWidget());
      
      // Simulate successful reset password call
      when(mockAuthProvider.resetPassword(email: anyNamed('email')))
          .thenAnswer((_) async => {});
      
      // Act - Enter valid email and submit
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Assert
      expect(find.text('Reset link sent! Check your email for instructions.'), findsOneWidget);
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('should navigate back to login when back button is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(MaterialApp(
        home: ChangeNotifierProvider<AuthProvider>.value(
          value: mockAuthProvider,
          child: const ForgotPasswordScreen(),
        ),
        routes: {
          '/login': (context) => const Scaffold(body: Text('Login Screen')),
        },
      ));
      
      // Act
      await tester.tap(find.text('Back to Sign In'));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('Login Screen'), findsOneWidget);
    });

    testWidgets('should navigate back when app bar back button is tapped', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: ChangeNotifierProvider<AuthProvider>.value(
            value: mockAuthProvider,
            child: const ForgotPasswordScreen(),
          ),
        ),
      ));
      
      // Act
      await tester.tap(find.byIcon(Icons.arrow_back));
      await tester.pumpAndSettle();

      // Assert - Should navigate back (screen should be popped)
      expect(find.text('Reset Password'), findsNothing);
    });

    testWidgets('should disable send button when loading', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.loading);
      await tester.pumpWidget(createTestWidget());
      
      // Act & Assert
      final sendButton = find.text('Send Reset Link');
      expect(sendButton, findsOneWidget);
      
      // Button should be disabled (not tappable) when loading
      await tester.tap(sendButton);
      await tester.pump();
      
      // Verify resetPassword is not called when button is disabled
      verifyNever(mockAuthProvider.resetPassword(email: anyNamed('email')));
    });

    testWidgets('should clear error when user starts typing', (WidgetTester tester) async {
      // Arrange
      when(mockAuthProvider.state).thenReturn(AuthState.error);
      when(mockAuthProvider.errorMessage).thenReturn('User not found');
      await tester.pumpWidget(createTestWidget());
      
      // Act - Start typing in email field
      await tester.enterText(find.byType(TextFormField), 't');
      await tester.pump();

      // Assert
      verify(mockAuthProvider.clearError()).called(1);
    });

    testWidgets('should show helpful instructions', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.textContaining('Enter your email address'), findsOneWidget);
      expect(find.textContaining('we\'ll send you a link'), findsOneWidget);
      expect(find.textContaining('reset your password'), findsOneWidget);
    });

    testWidgets('should trim whitespace from email input', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter email with whitespace and submit
      await tester.enterText(find.byType(TextFormField), '  <EMAIL>  ');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Assert - Should call with trimmed email
      verify(mockAuthProvider.resetPassword(email: '<EMAIL>')).called(1);
    });

    testWidgets('should convert email to lowercase', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());
      
      // Act - Enter uppercase email and submit
      await tester.enterText(find.byType(TextFormField), '<EMAIL>');
      await tester.tap(find.text('Send Reset Link'));
      await tester.pump();

      // Assert - Should call with lowercase email
      verify(mockAuthProvider.resetPassword(email: '<EMAIL>')).called(1);
    });

    testWidgets('should show email icon in text field', (WidgetTester tester) async {
      // Arrange
      await tester.pumpWidget(createTestWidget());

      // Assert
      expect(find.byIcon(Icons.email), findsOneWidget);
    });
  });
}